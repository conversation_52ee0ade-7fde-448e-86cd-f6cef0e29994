#!/bin/bash
# GuiXiaoxi RAG 环境变量设置脚本

echo "🔧 设置 GuiXiaoxi RAG 环境变量..."

# 从 config.json 读取配置
if [ -f "config.json" ]; then
    echo "📄 从 config.json 读取配置..."
    
    # 提取配置值
    LLM_API_BASE=$(python3 -c "import json; config=json.load(open('config.json')); print(config['llm']['api_base'])" 2>/dev/null || echo "http://localhost:8100/v1")
    LLM_API_KEY=$(python3 -c "import json; config=json.load(open('config.json')); print(config['llm']['api_key'])" 2>/dev/null || echo "sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q")
    LLM_MODEL=$(python3 -c "import json; config=json.load(open('config.json')); print(config['llm']['model'])" 2>/dev/null || echo "qwen14b")
    
    EMBEDDING_API_BASE=$(python3 -c "import json; config=json.load(open('config.json')); print(config['embedding']['api_base'])" 2>/dev/null || echo "http://localhost:8200/v1")
    EMBEDDING_API_KEY=$(python3 -c "import json; config=json.load(open('config.json')); print(config['embedding']['api_key'])" 2>/dev/null || echo "sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q")
    EMBEDDING_MODEL=$(python3 -c "import json; config=json.load(open('config.json')); print(config['embedding']['model'])" 2>/dev/null || echo "embedding_qwen")
    
    WORKING_DIR=$(python3 -c "import json; config=json.load(open('config.json')); print(config['rag']['working_dir'])" 2>/dev/null || echo "./knowledgeBase")
    API_PORT=$(python3 -c "import json; config=json.load(open('config.json')); print(config['api']['port'])" 2>/dev/null || echo "8000")
else
    echo "⚠️  config.json 不存在，使用默认配置..."
    LLM_API_BASE="http://localhost:8100/v1"
    LLM_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
    LLM_MODEL="qwen14b"
    EMBEDDING_API_BASE="http://localhost:8200/v1"
    EMBEDDING_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
    EMBEDDING_MODEL="embedding_qwen"
    WORKING_DIR="./knowledgeBase"
    API_PORT="8000"
fi

# 设置环境变量
echo "🚀 设置环境变量..."

# OpenAI 兼容 API 配置（必需）
export OPENAI_API_KEY="$LLM_API_KEY"
export OPENAI_API_BASE="$LLM_API_BASE"
export LLM_MODEL="$LLM_MODEL"

# 嵌入服务配置
export EMBEDDING_API_KEY="$EMBEDDING_API_KEY"
export EMBEDDING_API_BASE="$EMBEDDING_API_BASE"
export EMBEDDING_MODEL="$EMBEDDING_MODEL"

# GuiXiaoxi 特定配置
export GUIXIAOXI_LLM_API_BASE="$LLM_API_BASE"
export GUIXIAOXI_EMBEDDING_API_BASE="$EMBEDDING_API_BASE"
export GUIXIAOXI_WORKING_DIR="$WORKING_DIR"
export GUIXIAOXI_API_PORT="$API_PORT"

# LightRAG 兼容配置
export LLM_BINDING_HOST="$LLM_API_BASE"
export LLM_BINDING_API_KEY="$LLM_API_KEY"
export EMBEDDING_BINDING_HOST="$EMBEDDING_API_BASE"
export EMBEDDING_BINDING_API_KEY="$EMBEDDING_API_KEY"

echo "✅ 环境变量设置完成！"
echo ""
echo "📋 当前配置："
echo "  LLM API Base: $OPENAI_API_BASE"
echo "  LLM Model: $LLM_MODEL"
echo "  Embedding API Base: $EMBEDDING_API_BASE"
echo "  Embedding Model: $EMBEDDING_MODEL"
echo "  Working Dir: $GUIXIAOXI_WORKING_DIR"
echo "  API Port: $GUIXIAOXI_API_PORT"
echo ""

# 验证服务连接
echo "🔍 验证服务连接..."

# 检查 LLM 服务
if curl -s "$LLM_API_BASE/models" > /dev/null 2>&1; then
    echo "✅ LLM 服务连接正常 ($LLM_API_BASE)"
else
    echo "❌ LLM 服务连接失败 ($LLM_API_BASE)"
fi

# 检查嵌入服务
if curl -s "$EMBEDDING_API_BASE/models" > /dev/null 2>&1; then
    echo "✅ 嵌入服务连接正常 ($EMBEDDING_API_BASE)"
else
    echo "❌ 嵌入服务连接失败 ($EMBEDDING_API_BASE)"
fi

echo ""
echo "💡 使用方法："
echo "  source setup_env.sh    # 在当前shell中设置环境变量"
echo "  ./setup_env.sh         # 仅显示配置信息"
echo ""
echo "🚀 现在可以启动服务："
echo "  python start_server.py"
echo "  python start_streamlit.py"
