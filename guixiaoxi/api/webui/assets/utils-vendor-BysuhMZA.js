import{R as ut}from"./react-vendor-DEwriMA6.js";const dt=n=>{let e;const t=new Set,r=(c,u)=>{const d=typeof c=="function"?c(e):c;if(!Object.is(d,e)){const h=e;e=u??(typeof d!="object"||d===null)?d:Object.assign({},e,d),t.forEach(b=>b(e,h))}},s=()=>e,a={setState:r,getState:s,getInitialState:()=>l,subscribe:c=>(t.add(c),()=>t.delete(c))},l=e=n(r,s,a);return a},Cn=n=>n?dt(n):dt,Ln=n=>n;function An(n,e=Ln){const t=ut.useSyncExternalStore(n.subscribe,()=>e(n.getState()),()=>e(n.getInitialState()));return ut.useDebugValue(t),t}const ft=n=>{const e=Cn(n),t=r=>An(e,r);return Object.assign(t,e),t},uo=n=>n?ft(n):ft;function Ht(n){var e,t,r="";if(typeof n=="string"||typeof n=="number")r+=n;else if(typeof n=="object")if(Array.isArray(n)){var s=n.length;for(e=0;e<s;e++)n[e]&&(t=Ht(n[e]))&&(r&&(r+=" "),r+=t)}else for(t in n)n[t]&&(r&&(r+=" "),r+=t);return r}function fo(){for(var n,e,t=0,r="",s=arguments.length;t<s;t++)(n=arguments[t])&&(e=Ht(n))&&(r&&(r+=" "),r+=e);return r}const ot="-",Pn=n=>{const e=kn(n),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=n;return{getClassGroupId:i=>{const a=i.split(ot);return a[0]===""&&a.length!==1&&a.shift(),Kt(a,e)||Tn(i)},getConflictingClassGroupIds:(i,a)=>{const l=t[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},Kt=(n,e)=>{var i;if(n.length===0)return e.classGroupId;const t=n[0],r=e.nextPart.get(t),s=r?Kt(n.slice(1),r):void 0;if(s)return s;if(e.validators.length===0)return;const o=n.join(ot);return(i=e.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},ht=/^\[(.+)\]$/,Tn=n=>{if(ht.test(n)){const e=ht.exec(n)[1],t=e==null?void 0:e.substring(0,e.indexOf(":"));if(t)return"arbitrary.."+t}},kn=n=>{const{theme:e,classGroups:t}=n,r={nextPart:new Map,validators:[]};for(const s in t)We(t[s],r,s,e);return r},We=(n,e,t,r)=>{n.forEach(s=>{if(typeof s=="string"){const o=s===""?e:pt(e,s);o.classGroupId=t;return}if(typeof s=="function"){if(Nn(s)){We(s(r),e,t,r);return}e.validators.push({validator:s,classGroupId:t});return}Object.entries(s).forEach(([o,i])=>{We(i,pt(e,o),t,r)})})},pt=(n,e)=>{let t=n;return e.split(ot).forEach(r=>{t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)}),t},Nn=n=>n.isThemeGetter,Fn=n=>{if(n<1)return{get:()=>{},set:()=>{}};let e=0,t=new Map,r=new Map;const s=(o,i)=>{t.set(o,i),e++,e>n&&(e=0,r=t,t=new Map)};return{get(o){let i=t.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return s(o,i),i},set(o,i){t.has(o)?t.set(o,i):s(o,i)}}},Xe="!",Qe=":",jn=Qe.length,$n=n=>{const{prefix:e,experimentalParseClassName:t}=n;let r=s=>{const o=[];let i=0,a=0,l=0,c;for(let p=0;p<s.length;p++){let m=s[p];if(i===0&&a===0){if(m===Qe){o.push(s.slice(l,p)),l=p+jn;continue}if(m==="/"){c=p;continue}}m==="["?i++:m==="]"?i--:m==="("?a++:m===")"&&a--}const u=o.length===0?s:s.substring(l),d=In(u),h=d!==u,b=c&&c>l?c-l:void 0;return{modifiers:o,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:b}};if(e){const s=e+Qe,o=r;r=i=>i.startsWith(s)?o(i.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(t){const s=r;r=o=>t({className:o,parseClassName:s})}return r},In=n=>n.endsWith(Xe)?n.substring(0,n.length-1):n.startsWith(Xe)?n.substring(1):n,Dn=n=>{const e=Object.fromEntries(n.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let o=[];return r.forEach(i=>{i[0]==="["||e[i]?(s.push(...o.sort(),i),o=[]):o.push(i)}),s.push(...o.sort()),s}},Mn=n=>({cache:Fn(n.cacheSize),parseClassName:$n(n),sortModifiers:Dn(n),...Pn(n)}),Un=/\s+/,zn=(n,e)=>{const{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:o}=e,i=[],a=n.trim().split(Un);let l="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{isExternal:d,modifiers:h,hasImportantModifier:b,baseClassName:p,maybePostfixModifierPosition:m}=t(u);if(d){l=u+(l.length>0?" "+l:l);continue}let g=!!m,R=r(g?p.substring(0,m):p);if(!R){if(!g){l=u+(l.length>0?" "+l:l);continue}if(R=r(p),!R){l=u+(l.length>0?" "+l:l);continue}g=!1}const E=o(h).join(":"),L=b?E+Xe:E,P=L+R;if(i.includes(P))continue;i.push(P);const T=s(R,g);for(let w=0;w<T.length;++w){const v=T[w];i.push(L+v)}l=u+(l.length>0?" "+l:l)}return l};function Bn(){let n=0,e,t,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=_t(e))&&(r&&(r+=" "),r+=t);return r}const _t=n=>{if(typeof n=="string")return n;let e,t="";for(let r=0;r<n.length;r++)n[r]&&(e=_t(n[r]))&&(t&&(t+=" "),t+=e);return t};function Vn(n,...e){let t,r,s,o=i;function i(l){const c=e.reduce((u,d)=>d(u),n());return t=Mn(c),r=t.cache.get,s=t.cache.set,o=a,a(l)}function a(l){const c=r(l);if(c)return c;const u=zn(l,t);return s(l,u),u}return function(){return o(Bn.apply(null,arguments))}}const I=n=>{const e=t=>t[n]||[];return e.isThemeGetter=!0,e},qt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Jt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Hn=/^\d+\/\d+$/,Kn=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,_n=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,qn=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Jn=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Gn=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,oe=n=>Hn.test(n),C=n=>!!n&&!Number.isNaN(Number(n)),ee=n=>!!n&&Number.isInteger(Number(n)),gt=n=>n.endsWith("%")&&C(n.slice(0,-1)),Z=n=>Kn.test(n),Wn=()=>!0,Xn=n=>_n.test(n)&&!qn.test(n),it=()=>!1,Qn=n=>Jn.test(n),Yn=n=>Gn.test(n),Zn=n=>!y(n)&&!x(n),er=n=>le(n,Xt,it),y=n=>qt.test(n),te=n=>le(n,Qt,Xn),He=n=>le(n,dr,C),tr=n=>le(n,Gt,it),nr=n=>le(n,Wt,Yn),rr=n=>le(n,it,Qn),x=n=>Jt.test(n),Ee=n=>ce(n,Qt),sr=n=>ce(n,fr),or=n=>ce(n,Gt),ir=n=>ce(n,Xt),ar=n=>ce(n,Wt),lr=n=>ce(n,hr,!0),le=(n,e,t)=>{const r=qt.exec(n);return r?r[1]?e(r[1]):t(r[2]):!1},ce=(n,e,t=!1)=>{const r=Jt.exec(n);return r?r[1]?e(r[1]):t:!1},Gt=n=>n==="position",cr=new Set(["image","url"]),Wt=n=>cr.has(n),ur=new Set(["length","size","percentage"]),Xt=n=>ur.has(n),Qt=n=>n==="length",dr=n=>n==="number",fr=n=>n==="family-name",hr=n=>n==="shadow",pr=()=>{const n=I("color"),e=I("font"),t=I("text"),r=I("font-weight"),s=I("tracking"),o=I("leading"),i=I("breakpoint"),a=I("container"),l=I("spacing"),c=I("radius"),u=I("shadow"),d=I("inset-shadow"),h=I("drop-shadow"),b=I("blur"),p=I("perspective"),m=I("aspect"),g=I("ease"),R=I("animate"),E=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],P=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],w=()=>[x,y,l],v=()=>[oe,"full","auto",...w()],N=()=>[ee,"none","subgrid",x,y],K=()=>["auto",{span:["full",ee,x,y]},x,y],q=()=>[ee,"auto",x,y],M=()=>["auto","min","max","fr",x,y],B=()=>["start","end","center","between","around","evenly","stretch","baseline"],X=()=>["start","end","center","stretch"],F=()=>["auto",...w()],D=()=>[oe,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],A=()=>[n,x,y],Q=()=>[gt,te],$=()=>["","none","full",c,x,y],k=()=>["",C,Ee,te],_=()=>["solid","dashed","dotted","double"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],fe=()=>["","none",b,x,y],he=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",x,y],Se=()=>["none",C,x,y],Re=()=>["none",C,x,y],Ve=()=>[C,x,y],Oe=()=>[oe,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Z],breakpoint:[Z],color:[Wn],container:[Z],"drop-shadow":[Z],ease:["in","out","in-out"],font:[Zn],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Z],shadow:[Z],spacing:["px",C],text:[Z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",oe,y,x,m]}],container:["container"],columns:[{columns:[C,y,x,a]}],"break-after":[{"break-after":E()}],"break-before":[{"break-before":E()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...L(),y,x]}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[ee,"auto",x,y]}],basis:[{basis:[oe,"full","auto",a,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,oe,"auto","initial","none",y]}],grow:[{grow:["",C,x,y]}],shrink:[{shrink:["",C,x,y]}],order:[{order:[ee,"first","last","none",x,y]}],"grid-cols":[{"grid-cols":N()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":N()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...X(),"normal"]}],"justify-self":[{"justify-self":["auto",...X()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...X(),"baseline"]}],"align-self":[{self:["auto",...X(),"baseline"]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...X(),"baseline"]}],"place-self":[{"place-self":["auto",...X()]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:F()}],mx:[{mx:F()}],my:[{my:F()}],ms:[{ms:F()}],me:[{me:F()}],mt:[{mt:F()}],mr:[{mr:F()}],mb:[{mb:F()}],ml:[{ml:F()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[a,"screen",...D()]}],"min-w":[{"min-w":[a,"screen","none",...D()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...D()]}],h:[{h:["screen",...D()]}],"min-h":[{"min-h":["screen","none",...D()]}],"max-h":[{"max-h":["screen",...D()]}],"font-size":[{text:["base",t,Ee,te]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,x,He]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",gt,y]}],"font-family":[{font:[sr,y,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,x,y]}],"line-clamp":[{"line-clamp":[C,"none",x,He]}],leading:[{leading:[o,...w()]}],"list-image":[{"list-image":["none",x,y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",x,y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:A()}],"text-color":[{text:A()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",x,te]}],"text-decoration-color":[{decoration:A()}],"underline-offset":[{"underline-offset":[C,"auto",x,y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",x,y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",x,y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...L(),or,tr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",ir,er]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ee,x,y],radial:["",x,y],conic:[ee,x,y]},ar,nr]}],"bg-color":[{bg:A()}],"gradient-from-pos":[{from:Q()}],"gradient-via-pos":[{via:Q()}],"gradient-to-pos":[{to:Q()}],"gradient-from":[{from:A()}],"gradient-via":[{via:A()}],"gradient-to":[{to:A()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:k()}],"border-w-x":[{"border-x":k()}],"border-w-y":[{"border-y":k()}],"border-w-s":[{"border-s":k()}],"border-w-e":[{"border-e":k()}],"border-w-t":[{"border-t":k()}],"border-w-r":[{"border-r":k()}],"border-w-b":[{"border-b":k()}],"border-w-l":[{"border-l":k()}],"divide-x":[{"divide-x":k()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":k()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[..._(),"hidden","none"]}],"divide-style":[{divide:[..._(),"hidden","none"]}],"border-color":[{border:A()}],"border-color-x":[{"border-x":A()}],"border-color-y":[{"border-y":A()}],"border-color-s":[{"border-s":A()}],"border-color-e":[{"border-e":A()}],"border-color-t":[{"border-t":A()}],"border-color-r":[{"border-r":A()}],"border-color-b":[{"border-b":A()}],"border-color-l":[{"border-l":A()}],"divide-color":[{divide:A()}],"outline-style":[{outline:[..._(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,x,y]}],"outline-w":[{outline:["",C,Ee,te]}],"outline-color":[{outline:[n]}],shadow:[{shadow:["","none",u,lr,rr]}],"shadow-color":[{shadow:A()}],"inset-shadow":[{"inset-shadow":["none",x,y,d]}],"inset-shadow-color":[{"inset-shadow":A()}],"ring-w":[{ring:k()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:A()}],"ring-offset-w":[{"ring-offset":[C,te]}],"ring-offset-color":[{"ring-offset":A()}],"inset-ring-w":[{"inset-ring":k()}],"inset-ring-color":[{"inset-ring":A()}],opacity:[{opacity:[C,x,y]}],"mix-blend":[{"mix-blend":[...Y(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Y()}],filter:[{filter:["","none",x,y]}],blur:[{blur:fe()}],brightness:[{brightness:[C,x,y]}],contrast:[{contrast:[C,x,y]}],"drop-shadow":[{"drop-shadow":["","none",h,x,y]}],grayscale:[{grayscale:["",C,x,y]}],"hue-rotate":[{"hue-rotate":[C,x,y]}],invert:[{invert:["",C,x,y]}],saturate:[{saturate:[C,x,y]}],sepia:[{sepia:["",C,x,y]}],"backdrop-filter":[{"backdrop-filter":["","none",x,y]}],"backdrop-blur":[{"backdrop-blur":fe()}],"backdrop-brightness":[{"backdrop-brightness":[C,x,y]}],"backdrop-contrast":[{"backdrop-contrast":[C,x,y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,x,y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,x,y]}],"backdrop-invert":[{"backdrop-invert":["",C,x,y]}],"backdrop-opacity":[{"backdrop-opacity":[C,x,y]}],"backdrop-saturate":[{"backdrop-saturate":[C,x,y]}],"backdrop-sepia":[{"backdrop-sepia":["",C,x,y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",x,y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",x,y]}],ease:[{ease:["linear","initial",g,x,y]}],delay:[{delay:[C,x,y]}],animate:[{animate:["none",R,x,y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,x,y]}],"perspective-origin":[{"perspective-origin":he()}],rotate:[{rotate:Se()}],"rotate-x":[{"rotate-x":Se()}],"rotate-y":[{"rotate-y":Se()}],"rotate-z":[{"rotate-z":Se()}],scale:[{scale:Re()}],"scale-x":[{"scale-x":Re()}],"scale-y":[{"scale-y":Re()}],"scale-z":[{"scale-z":Re()}],"scale-3d":["scale-3d"],skew:[{skew:Ve()}],"skew-x":[{"skew-x":Ve()}],"skew-y":[{"skew-y":Ve()}],transform:[{transform:[x,y,"","none","gpu","cpu"]}],"transform-origin":[{origin:he()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Oe()}],"translate-x":[{"translate-x":Oe()}],"translate-y":[{"translate-y":Oe()}],"translate-z":[{"translate-z":Oe()}],"translate-none":["translate-none"],accent:[{accent:A()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:A()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",x,y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",x,y]}],fill:[{fill:["none",...A()]}],"stroke-w":[{stroke:[C,Ee,te,He]}],stroke:[{stroke:["none",...A()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}},ho=Vn(pr);function Yt(n,e){return function(){return n.apply(e,arguments)}}const{toString:gr}=Object.prototype,{getPrototypeOf:at}=Object,$e=(n=>e=>{const t=gr.call(e);return n[t]||(n[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),J=n=>(n=n.toLowerCase(),e=>$e(e)===n),Ie=n=>e=>typeof e===n,{isArray:ue}=Array,be=Ie("undefined");function mr(n){return n!==null&&!be(n)&&n.constructor!==null&&!be(n.constructor)&&H(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Zt=J("ArrayBuffer");function br(n){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(n):e=n&&n.buffer&&Zt(n.buffer),e}const yr=Ie("string"),H=Ie("function"),en=Ie("number"),De=n=>n!==null&&typeof n=="object",xr=n=>n===!0||n===!1,Ce=n=>{if($e(n)!=="object")return!1;const e=at(n);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)},wr=J("Date"),Sr=J("File"),Rr=J("Blob"),Or=J("FileList"),Er=n=>De(n)&&H(n.pipe),vr=n=>{let e;return n&&(typeof FormData=="function"&&n instanceof FormData||H(n.append)&&((e=$e(n))==="formdata"||e==="object"&&H(n.toString)&&n.toString()==="[object FormData]"))},Cr=J("URLSearchParams"),[Lr,Ar,Pr,Tr]=["ReadableStream","Request","Response","Headers"].map(J),kr=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function xe(n,e,{allOwnKeys:t=!1}={}){if(n===null||typeof n>"u")return;let r,s;if(typeof n!="object"&&(n=[n]),ue(n))for(r=0,s=n.length;r<s;r++)e.call(null,n[r],r,n);else{const o=t?Object.getOwnPropertyNames(n):Object.keys(n),i=o.length;let a;for(r=0;r<i;r++)a=o[r],e.call(null,n[a],a,n)}}function tn(n,e){e=e.toLowerCase();const t=Object.keys(n);let r=t.length,s;for(;r-- >0;)if(s=t[r],e===s.toLowerCase())return s;return null}const ne=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,nn=n=>!be(n)&&n!==ne;function Ye(){const{caseless:n}=nn(this)&&this||{},e={},t=(r,s)=>{const o=n&&tn(e,s)||s;Ce(e[o])&&Ce(r)?e[o]=Ye(e[o],r):Ce(r)?e[o]=Ye({},r):ue(r)?e[o]=r.slice():e[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&xe(arguments[r],t);return e}const Nr=(n,e,t,{allOwnKeys:r}={})=>(xe(e,(s,o)=>{t&&H(s)?n[o]=Yt(s,t):n[o]=s},{allOwnKeys:r}),n),Fr=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),jr=(n,e,t,r)=>{n.prototype=Object.create(e.prototype,r),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:e.prototype}),t&&Object.assign(n.prototype,t)},$r=(n,e,t,r)=>{let s,o,i;const a={};if(e=e||{},n==null)return e;do{for(s=Object.getOwnPropertyNames(n),o=s.length;o-- >0;)i=s[o],(!r||r(i,n,e))&&!a[i]&&(e[i]=n[i],a[i]=!0);n=t!==!1&&at(n)}while(n&&(!t||t(n,e))&&n!==Object.prototype);return e},Ir=(n,e,t)=>{n=String(n),(t===void 0||t>n.length)&&(t=n.length),t-=e.length;const r=n.indexOf(e,t);return r!==-1&&r===t},Dr=n=>{if(!n)return null;if(ue(n))return n;let e=n.length;if(!en(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=n[e];return t},Mr=(n=>e=>n&&e instanceof n)(typeof Uint8Array<"u"&&at(Uint8Array)),Ur=(n,e)=>{const r=(n&&n[Symbol.iterator]).call(n);let s;for(;(s=r.next())&&!s.done;){const o=s.value;e.call(n,o[0],o[1])}},zr=(n,e)=>{let t;const r=[];for(;(t=n.exec(e))!==null;)r.push(t);return r},Br=J("HTMLFormElement"),Vr=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,r,s){return r.toUpperCase()+s}),mt=(({hasOwnProperty:n})=>(e,t)=>n.call(e,t))(Object.prototype),Hr=J("RegExp"),rn=(n,e)=>{const t=Object.getOwnPropertyDescriptors(n),r={};xe(t,(s,o)=>{let i;(i=e(s,o,n))!==!1&&(r[o]=i||s)}),Object.defineProperties(n,r)},Kr=n=>{rn(n,(e,t)=>{if(H(n)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const r=n[t];if(H(r)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},_r=(n,e)=>{const t={},r=s=>{s.forEach(o=>{t[o]=!0})};return ue(n)?r(n):r(String(n).split(e)),t},qr=()=>{},Jr=(n,e)=>n!=null&&Number.isFinite(n=+n)?n:e,Ke="abcdefghijklmnopqrstuvwxyz",bt="0123456789",sn={DIGIT:bt,ALPHA:Ke,ALPHA_DIGIT:Ke+Ke.toUpperCase()+bt},Gr=(n=16,e=sn.ALPHA_DIGIT)=>{let t="";const{length:r}=e;for(;n--;)t+=e[Math.random()*r|0];return t};function Wr(n){return!!(n&&H(n.append)&&n[Symbol.toStringTag]==="FormData"&&n[Symbol.iterator])}const Xr=n=>{const e=new Array(10),t=(r,s)=>{if(De(r)){if(e.indexOf(r)>=0)return;if(!("toJSON"in r)){e[s]=r;const o=ue(r)?[]:{};return xe(r,(i,a)=>{const l=t(i,s+1);!be(l)&&(o[a]=l)}),e[s]=void 0,o}}return r};return t(n,0)},Qr=J("AsyncFunction"),Yr=n=>n&&(De(n)||H(n))&&H(n.then)&&H(n.catch),on=((n,e)=>n?setImmediate:e?((t,r)=>(ne.addEventListener("message",({source:s,data:o})=>{s===ne&&o===t&&r.length&&r.shift()()},!1),s=>{r.push(s),ne.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",H(ne.postMessage)),Zr=typeof queueMicrotask<"u"?queueMicrotask.bind(ne):typeof process<"u"&&process.nextTick||on,f={isArray:ue,isArrayBuffer:Zt,isBuffer:mr,isFormData:vr,isArrayBufferView:br,isString:yr,isNumber:en,isBoolean:xr,isObject:De,isPlainObject:Ce,isReadableStream:Lr,isRequest:Ar,isResponse:Pr,isHeaders:Tr,isUndefined:be,isDate:wr,isFile:Sr,isBlob:Rr,isRegExp:Hr,isFunction:H,isStream:Er,isURLSearchParams:Cr,isTypedArray:Mr,isFileList:Or,forEach:xe,merge:Ye,extend:Nr,trim:kr,stripBOM:Fr,inherits:jr,toFlatObject:$r,kindOf:$e,kindOfTest:J,endsWith:Ir,toArray:Dr,forEachEntry:Ur,matchAll:zr,isHTMLForm:Br,hasOwnProperty:mt,hasOwnProp:mt,reduceDescriptors:rn,freezeMethods:Kr,toObjectSet:_r,toCamelCase:Vr,noop:qr,toFiniteNumber:Jr,findKey:tn,global:ne,isContextDefined:nn,ALPHABET:sn,generateString:Gr,isSpecCompliantForm:Wr,toJSONObject:Xr,isAsyncFn:Qr,isThenable:Yr,setImmediate:on,asap:Zr};function O(n,e,t,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}f.inherits(O,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const an=O.prototype,ln={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{ln[n]={value:n}});Object.defineProperties(O,ln);Object.defineProperty(an,"isAxiosError",{value:!0});O.from=(n,e,t,r,s,o)=>{const i=Object.create(an);return f.toFlatObject(n,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),O.call(i,n.message,e,t,r,s),i.cause=n,i.name=n.name,o&&Object.assign(i,o),i};const es=null;function Ze(n){return f.isPlainObject(n)||f.isArray(n)}function cn(n){return f.endsWith(n,"[]")?n.slice(0,-2):n}function yt(n,e,t){return n?n.concat(e).map(function(s,o){return s=cn(s),!t&&o?"["+s+"]":s}).join(t?".":""):e}function ts(n){return f.isArray(n)&&!n.some(Ze)}const ns=f.toFlatObject(f,{},null,function(e){return/^is[A-Z]/.test(e)});function Me(n,e,t){if(!f.isObject(n))throw new TypeError("target must be an object");e=e||new FormData,t=f.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,g){return!f.isUndefined(g[m])});const r=t.metaTokens,s=t.visitor||u,o=t.dots,i=t.indexes,l=(t.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(e);if(!f.isFunction(s))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(f.isDate(p))return p.toISOString();if(!l&&f.isBlob(p))throw new O("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(p)||f.isTypedArray(p)?l&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,m,g){let R=p;if(p&&!g&&typeof p=="object"){if(f.endsWith(m,"{}"))m=r?m:m.slice(0,-2),p=JSON.stringify(p);else if(f.isArray(p)&&ts(p)||(f.isFileList(p)||f.endsWith(m,"[]"))&&(R=f.toArray(p)))return m=cn(m),R.forEach(function(L,P){!(f.isUndefined(L)||L===null)&&e.append(i===!0?yt([m],P,o):i===null?m:m+"[]",c(L))}),!1}return Ze(p)?!0:(e.append(yt(g,m,o),c(p)),!1)}const d=[],h=Object.assign(ns,{defaultVisitor:u,convertValue:c,isVisitable:Ze});function b(p,m){if(!f.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+m.join("."));d.push(p),f.forEach(p,function(R,E){(!(f.isUndefined(R)||R===null)&&s.call(e,R,f.isString(E)?E.trim():E,m,h))===!0&&b(R,m?m.concat(E):[E])}),d.pop()}}if(!f.isObject(n))throw new TypeError("data must be an object");return b(n),e}function xt(n){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(r){return e[r]})}function lt(n,e){this._pairs=[],n&&Me(n,this,e)}const un=lt.prototype;un.append=function(e,t){this._pairs.push([e,t])};un.toString=function(e){const t=e?function(r){return e.call(this,r,xt)}:xt;return this._pairs.map(function(s){return t(s[0])+"="+t(s[1])},"").join("&")};function rs(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function dn(n,e,t){if(!e)return n;const r=t&&t.encode||rs;f.isFunction(t)&&(t={serialize:t});const s=t&&t.serialize;let o;if(s?o=s(e,t):o=f.isURLSearchParams(e)?e.toString():new lt(e,t).toString(r),o){const i=n.indexOf("#");i!==-1&&(n=n.slice(0,i)),n+=(n.indexOf("?")===-1?"?":"&")+o}return n}class wt{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){f.forEach(this.handlers,function(r){r!==null&&e(r)})}}const fn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ss=typeof URLSearchParams<"u"?URLSearchParams:lt,os=typeof FormData<"u"?FormData:null,is=typeof Blob<"u"?Blob:null,as={isBrowser:!0,classes:{URLSearchParams:ss,FormData:os,Blob:is},protocols:["http","https","file","blob","url","data"]},ct=typeof window<"u"&&typeof document<"u",et=typeof navigator=="object"&&navigator||void 0,ls=ct&&(!et||["ReactNative","NativeScript","NS"].indexOf(et.product)<0),cs=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",us=ct&&window.location.href||"http://localhost",ds=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ct,hasStandardBrowserEnv:ls,hasStandardBrowserWebWorkerEnv:cs,navigator:et,origin:us},Symbol.toStringTag,{value:"Module"})),U={...ds,...as};function fs(n,e){return Me(n,new U.classes.URLSearchParams,Object.assign({visitor:function(t,r,s,o){return U.isNode&&f.isBuffer(t)?(this.append(r,t.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},e))}function hs(n){return f.matchAll(/\w+|\[(\w*)]/g,n).map(e=>e[0]==="[]"?"":e[1]||e[0])}function ps(n){const e={},t=Object.keys(n);let r;const s=t.length;let o;for(r=0;r<s;r++)o=t[r],e[o]=n[o];return e}function hn(n){function e(t,r,s,o){let i=t[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=o>=t.length;return i=!i&&f.isArray(s)?s.length:i,l?(f.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!a):((!s[i]||!f.isObject(s[i]))&&(s[i]=[]),e(t,r,s[i],o)&&f.isArray(s[i])&&(s[i]=ps(s[i])),!a)}if(f.isFormData(n)&&f.isFunction(n.entries)){const t={};return f.forEachEntry(n,(r,s)=>{e(hs(r),s,t,0)}),t}return null}function gs(n,e,t){if(f.isString(n))try{return(e||JSON.parse)(n),f.trim(n)}catch(r){if(r.name!=="SyntaxError")throw r}return(t||JSON.stringify)(n)}const we={transitional:fn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const r=t.getContentType()||"",s=r.indexOf("application/json")>-1,o=f.isObject(e);if(o&&f.isHTMLForm(e)&&(e=new FormData(e)),f.isFormData(e))return s?JSON.stringify(hn(e)):e;if(f.isArrayBuffer(e)||f.isBuffer(e)||f.isStream(e)||f.isFile(e)||f.isBlob(e)||f.isReadableStream(e))return e;if(f.isArrayBufferView(e))return e.buffer;if(f.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return fs(e,this.formSerializer).toString();if((a=f.isFileList(e))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Me(a?{"files[]":e}:e,l&&new l,this.formSerializer)}}return o||s?(t.setContentType("application/json",!1),gs(e)):e}],transformResponse:[function(e){const t=this.transitional||we.transitional,r=t&&t.forcedJSONParsing,s=this.responseType==="json";if(f.isResponse(e)||f.isReadableStream(e))return e;if(e&&f.isString(e)&&(r&&!this.responseType||s)){const i=!(t&&t.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(a){if(i)throw a.name==="SyntaxError"?O.from(a,O.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:U.classes.FormData,Blob:U.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],n=>{we.headers[n]={}});const ms=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),bs=n=>{const e={};let t,r,s;return n&&n.split(`
`).forEach(function(i){s=i.indexOf(":"),t=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!t||e[t]&&ms[t])&&(t==="set-cookie"?e[t]?e[t].push(r):e[t]=[r]:e[t]=e[t]?e[t]+", "+r:r)}),e},St=Symbol("internals");function pe(n){return n&&String(n).trim().toLowerCase()}function Le(n){return n===!1||n==null?n:f.isArray(n)?n.map(Le):String(n)}function ys(n){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=t.exec(n);)e[r[1]]=r[2];return e}const xs=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function _e(n,e,t,r,s){if(f.isFunction(r))return r.call(this,e,t);if(s&&(e=t),!!f.isString(e)){if(f.isString(r))return e.indexOf(r)!==-1;if(f.isRegExp(r))return r.test(e)}}function ws(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r)}function Ss(n,e){const t=f.toCamelCase(" "+e);["get","set","has"].forEach(r=>{Object.defineProperty(n,r+t,{value:function(s,o,i){return this[r].call(this,e,s,o,i)},configurable:!0})})}let V=class{constructor(e){e&&this.set(e)}set(e,t,r){const s=this;function o(a,l,c){const u=pe(l);if(!u)throw new Error("header name must be a non-empty string");const d=f.findKey(s,u);(!d||s[d]===void 0||c===!0||c===void 0&&s[d]!==!1)&&(s[d||l]=Le(a))}const i=(a,l)=>f.forEach(a,(c,u)=>o(c,u,l));if(f.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(f.isString(e)&&(e=e.trim())&&!xs(e))i(bs(e),t);else if(f.isHeaders(e))for(const[a,l]of e.entries())o(l,a,r);else e!=null&&o(t,e,r);return this}get(e,t){if(e=pe(e),e){const r=f.findKey(this,e);if(r){const s=this[r];if(!t)return s;if(t===!0)return ys(s);if(f.isFunction(t))return t.call(this,s,r);if(f.isRegExp(t))return t.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=pe(e),e){const r=f.findKey(this,e);return!!(r&&this[r]!==void 0&&(!t||_e(this,this[r],r,t)))}return!1}delete(e,t){const r=this;let s=!1;function o(i){if(i=pe(i),i){const a=f.findKey(r,i);a&&(!t||_e(r,r[a],a,t))&&(delete r[a],s=!0)}}return f.isArray(e)?e.forEach(o):o(e),s}clear(e){const t=Object.keys(this);let r=t.length,s=!1;for(;r--;){const o=t[r];(!e||_e(this,this[o],o,e,!0))&&(delete this[o],s=!0)}return s}normalize(e){const t=this,r={};return f.forEach(this,(s,o)=>{const i=f.findKey(r,o);if(i){t[i]=Le(s),delete t[o];return}const a=e?ws(o):String(o).trim();a!==o&&delete t[o],t[a]=Le(s),r[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return f.forEach(this,(r,s)=>{r!=null&&r!==!1&&(t[s]=e&&f.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const r=new this(e);return t.forEach(s=>r.set(s)),r}static accessor(e){const r=(this[St]=this[St]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=pe(i);r[a]||(Ss(s,i),r[a]=!0)}return f.isArray(e)?e.forEach(o):o(e),this}};V.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(V.prototype,({value:n},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>n,set(r){this[t]=r}}});f.freezeMethods(V);function qe(n,e){const t=this||we,r=e||t,s=V.from(r.headers);let o=r.data;return f.forEach(n,function(a){o=a.call(t,o,s.normalize(),e?e.status:void 0)}),s.normalize(),o}function pn(n){return!!(n&&n.__CANCEL__)}function de(n,e,t){O.call(this,n??"canceled",O.ERR_CANCELED,e,t),this.name="CanceledError"}f.inherits(de,O,{__CANCEL__:!0});function gn(n,e,t){const r=t.config.validateStatus;!t.status||!r||r(t.status)?n(t):e(new O("Request failed with status code "+t.status,[O.ERR_BAD_REQUEST,O.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function Rs(n){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return e&&e[1]||""}function Os(n,e){n=n||10;const t=new Array(n),r=new Array(n);let s=0,o=0,i;return e=e!==void 0?e:1e3,function(l){const c=Date.now(),u=r[o];i||(i=c),t[s]=l,r[s]=c;let d=o,h=0;for(;d!==s;)h+=t[d++],d=d%n;if(s=(s+1)%n,s===o&&(o=(o+1)%n),c-i<e)return;const b=u&&c-u;return b?Math.round(h*1e3/b):void 0}}function Es(n,e){let t=0,r=1e3/e,s,o;const i=(c,u=Date.now())=>{t=u,s=null,o&&(clearTimeout(o),o=null),n.apply(null,c)};return[(...c)=>{const u=Date.now(),d=u-t;d>=r?i(c,u):(s=c,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const Pe=(n,e,t=3)=>{let r=0;const s=Os(50,250);return Es(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-r,c=s(l),u=i<=a;r=i;const d={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&u?(a-i)/c:void 0,event:o,lengthComputable:a!=null,[e?"download":"upload"]:!0};n(d)},t)},Rt=(n,e)=>{const t=n!=null;return[r=>e[0]({lengthComputable:t,total:n,loaded:r}),e[1]]},Ot=n=>(...e)=>f.asap(()=>n(...e)),vs=U.hasStandardBrowserEnv?((n,e)=>t=>(t=new URL(t,U.origin),n.protocol===t.protocol&&n.host===t.host&&(e||n.port===t.port)))(new URL(U.origin),U.navigator&&/(msie|trident)/i.test(U.navigator.userAgent)):()=>!0,Cs=U.hasStandardBrowserEnv?{write(n,e,t,r,s,o){const i=[n+"="+encodeURIComponent(e)];f.isNumber(t)&&i.push("expires="+new Date(t).toGMTString()),f.isString(r)&&i.push("path="+r),f.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(n){const e=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ls(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function As(n,e){return e?n.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):n}function mn(n,e){return n&&!Ls(e)?As(n,e):e}const Et=n=>n instanceof V?{...n}:n;function se(n,e){e=e||{};const t={};function r(c,u,d,h){return f.isPlainObject(c)&&f.isPlainObject(u)?f.merge.call({caseless:h},c,u):f.isPlainObject(u)?f.merge({},u):f.isArray(u)?u.slice():u}function s(c,u,d,h){if(f.isUndefined(u)){if(!f.isUndefined(c))return r(void 0,c,d,h)}else return r(c,u,d,h)}function o(c,u){if(!f.isUndefined(u))return r(void 0,u)}function i(c,u){if(f.isUndefined(u)){if(!f.isUndefined(c))return r(void 0,c)}else return r(void 0,u)}function a(c,u,d){if(d in e)return r(c,u);if(d in n)return r(void 0,c)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,u,d)=>s(Et(c),Et(u),d,!0)};return f.forEach(Object.keys(Object.assign({},n,e)),function(u){const d=l[u]||s,h=d(n[u],e[u],u);f.isUndefined(h)&&d!==a||(t[u]=h)}),t}const bn=n=>{const e=se({},n);let{data:t,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=e;e.headers=i=V.from(i),e.url=dn(mn(e.baseURL,e.url),n.params,n.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(f.isFormData(t)){if(U.hasStandardBrowserEnv||U.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[c,...u]=l?l.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...u].join("; "))}}if(U.hasStandardBrowserEnv&&(r&&f.isFunction(r)&&(r=r(e)),r||r!==!1&&vs(e.url))){const c=s&&o&&Cs.read(o);c&&i.set(s,c)}return e},Ps=typeof XMLHttpRequest<"u",Ts=Ps&&function(n){return new Promise(function(t,r){const s=bn(n);let o=s.data;const i=V.from(s.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=s,u,d,h,b,p;function m(){b&&b(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let g=new XMLHttpRequest;g.open(s.method.toUpperCase(),s.url,!0),g.timeout=s.timeout;function R(){if(!g)return;const L=V.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),T={data:!a||a==="text"||a==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:L,config:n,request:g};gn(function(v){t(v),m()},function(v){r(v),m()},T),g=null}"onloadend"in g?g.onloadend=R:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(R)},g.onabort=function(){g&&(r(new O("Request aborted",O.ECONNABORTED,n,g)),g=null)},g.onerror=function(){r(new O("Network Error",O.ERR_NETWORK,n,g)),g=null},g.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const T=s.transitional||fn;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),r(new O(P,T.clarifyTimeoutError?O.ETIMEDOUT:O.ECONNABORTED,n,g)),g=null},o===void 0&&i.setContentType(null),"setRequestHeader"in g&&f.forEach(i.toJSON(),function(P,T){g.setRequestHeader(T,P)}),f.isUndefined(s.withCredentials)||(g.withCredentials=!!s.withCredentials),a&&a!=="json"&&(g.responseType=s.responseType),c&&([h,p]=Pe(c,!0),g.addEventListener("progress",h)),l&&g.upload&&([d,b]=Pe(l),g.upload.addEventListener("progress",d),g.upload.addEventListener("loadend",b)),(s.cancelToken||s.signal)&&(u=L=>{g&&(r(!L||L.type?new de(null,n,g):L),g.abort(),g=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const E=Rs(s.url);if(E&&U.protocols.indexOf(E)===-1){r(new O("Unsupported protocol "+E+":",O.ERR_BAD_REQUEST,n));return}g.send(o||null)})},ks=(n,e)=>{const{length:t}=n=n?n.filter(Boolean):[];if(e||t){let r=new AbortController,s;const o=function(c){if(!s){s=!0,a();const u=c instanceof Error?c:this.reason;r.abort(u instanceof O?u:new de(u instanceof Error?u.message:u))}};let i=e&&setTimeout(()=>{i=null,o(new O(`timeout ${e} of ms exceeded`,O.ETIMEDOUT))},e);const a=()=>{n&&(i&&clearTimeout(i),i=null,n.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),n=null)};n.forEach(c=>c.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>f.asap(a),l}},Ns=function*(n,e){let t=n.byteLength;if(t<e){yield n;return}let r=0,s;for(;r<t;)s=r+e,yield n.slice(r,s),r=s},Fs=async function*(n,e){for await(const t of js(n))yield*Ns(t,e)},js=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const e=n.getReader();try{for(;;){const{done:t,value:r}=await e.read();if(t)break;yield r}}finally{await e.cancel()}},vt=(n,e,t,r)=>{const s=Fs(n,e);let o=0,i,a=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:c,value:u}=await s.next();if(c){a(),l.close();return}let d=u.byteLength;if(t){let h=o+=d;t(h)}l.enqueue(new Uint8Array(u))}catch(c){throw a(c),c}},cancel(l){return a(l),s.return()}},{highWaterMark:2})},Ue=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",yn=Ue&&typeof ReadableStream=="function",$s=Ue&&(typeof TextEncoder=="function"?(n=>e=>n.encode(e))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),xn=(n,...e)=>{try{return!!n(...e)}catch{return!1}},Is=yn&&xn(()=>{let n=!1;const e=new Request(U.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!e}),Ct=64*1024,tt=yn&&xn(()=>f.isReadableStream(new Response("").body)),Te={stream:tt&&(n=>n.body)};Ue&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Te[e]&&(Te[e]=f.isFunction(n[e])?t=>t[e]():(t,r)=>{throw new O(`Response type '${e}' is not supported`,O.ERR_NOT_SUPPORT,r)})})})(new Response);const Ds=async n=>{if(n==null)return 0;if(f.isBlob(n))return n.size;if(f.isSpecCompliantForm(n))return(await new Request(U.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(f.isArrayBufferView(n)||f.isArrayBuffer(n))return n.byteLength;if(f.isURLSearchParams(n)&&(n=n+""),f.isString(n))return(await $s(n)).byteLength},Ms=async(n,e)=>{const t=f.toFiniteNumber(n.getContentLength());return t??Ds(e)},Us=Ue&&(async n=>{let{url:e,method:t,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:h}=bn(n);c=c?(c+"").toLowerCase():"text";let b=ks([s,o&&o.toAbortSignal()],i),p;const m=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let g;try{if(l&&Is&&t!=="get"&&t!=="head"&&(g=await Ms(u,r))!==0){let T=new Request(e,{method:"POST",body:r,duplex:"half"}),w;if(f.isFormData(r)&&(w=T.headers.get("content-type"))&&u.setContentType(w),T.body){const[v,N]=Rt(g,Pe(Ot(l)));r=vt(T.body,Ct,v,N)}}f.isString(d)||(d=d?"include":"omit");const R="credentials"in Request.prototype;p=new Request(e,{...h,signal:b,method:t.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:R?d:void 0});let E=await fetch(p);const L=tt&&(c==="stream"||c==="response");if(tt&&(a||L&&m)){const T={};["status","statusText","headers"].forEach(K=>{T[K]=E[K]});const w=f.toFiniteNumber(E.headers.get("content-length")),[v,N]=a&&Rt(w,Pe(Ot(a),!0))||[];E=new Response(vt(E.body,Ct,v,()=>{N&&N(),m&&m()}),T)}c=c||"text";let P=await Te[f.findKey(Te,c)||"text"](E,n);return!L&&m&&m(),await new Promise((T,w)=>{gn(T,w,{data:P,headers:V.from(E.headers),status:E.status,statusText:E.statusText,config:n,request:p})})}catch(R){throw m&&m(),R&&R.name==="TypeError"&&/fetch/i.test(R.message)?Object.assign(new O("Network Error",O.ERR_NETWORK,n,p),{cause:R.cause||R}):O.from(R,R&&R.code,n,p)}}),nt={http:es,xhr:Ts,fetch:Us};f.forEach(nt,(n,e)=>{if(n){try{Object.defineProperty(n,"name",{value:e})}catch{}Object.defineProperty(n,"adapterName",{value:e})}});const Lt=n=>`- ${n}`,zs=n=>f.isFunction(n)||n===null||n===!1,wn={getAdapter:n=>{n=f.isArray(n)?n:[n];const{length:e}=n;let t,r;const s={};for(let o=0;o<e;o++){t=n[o];let i;if(r=t,!zs(t)&&(r=nt[(i=String(t)).toLowerCase()],r===void 0))throw new O(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=e?o.length>1?`since :
`+o.map(Lt).join(`
`):" "+Lt(o[0]):"as no adapter specified";throw new O("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:nt};function Je(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new de(null,n)}function At(n){return Je(n),n.headers=V.from(n.headers),n.data=qe.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),wn.getAdapter(n.adapter||we.adapter)(n).then(function(r){return Je(n),r.data=qe.call(n,n.transformResponse,r),r.headers=V.from(r.headers),r},function(r){return pn(r)||(Je(n),r&&r.response&&(r.response.data=qe.call(n,n.transformResponse,r.response),r.response.headers=V.from(r.response.headers))),Promise.reject(r)})}const Sn="1.7.9",ze={};["object","boolean","number","function","string","symbol"].forEach((n,e)=>{ze[n]=function(r){return typeof r===n||"a"+(e<1?"n ":" ")+n}});const Pt={};ze.transitional=function(e,t,r){function s(o,i){return"[Axios v"+Sn+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(e===!1)throw new O(s(i," has been removed"+(t?" in "+t:"")),O.ERR_DEPRECATED);return t&&!Pt[i]&&(Pt[i]=!0,console.warn(s(i," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(o,i,a):!0}};ze.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};function Bs(n,e,t){if(typeof n!="object")throw new O("options must be an object",O.ERR_BAD_OPTION_VALUE);const r=Object.keys(n);let s=r.length;for(;s-- >0;){const o=r[s],i=e[o];if(i){const a=n[o],l=a===void 0||i(a,o,n);if(l!==!0)throw new O("option "+o+" must be "+l,O.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new O("Unknown option "+o,O.ERR_BAD_OPTION)}}const Ae={assertOptions:Bs,validators:ze},G=Ae.validators;let re=class{constructor(e){this.defaults=e,this.interceptors={request:new wt,response:new wt}}async request(e,t){try{return await this._request(e,t)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=se(this.defaults,t);const{transitional:r,paramsSerializer:s,headers:o}=t;r!==void 0&&Ae.assertOptions(r,{silentJSONParsing:G.transitional(G.boolean),forcedJSONParsing:G.transitional(G.boolean),clarifyTimeoutError:G.transitional(G.boolean)},!1),s!=null&&(f.isFunction(s)?t.paramsSerializer={serialize:s}:Ae.assertOptions(s,{encode:G.function,serialize:G.function},!0)),Ae.assertOptions(t,{baseUrl:G.spelling("baseURL"),withXsrfToken:G.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&f.merge(o.common,o[t.method]);o&&f.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),t.headers=V.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(t)===!1||(l=l&&m.synchronous,a.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let u,d=0,h;if(!l){const p=[At.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),h=p.length,u=Promise.resolve(t);d<h;)u=u.then(p[d++],p[d++]);return u}h=a.length;let b=t;for(d=0;d<h;){const p=a[d++],m=a[d++];try{b=p(b)}catch(g){m.call(this,g);break}}try{u=At.call(this,b)}catch(p){return Promise.reject(p)}for(d=0,h=c.length;d<h;)u=u.then(c[d++],c[d++]);return u}getUri(e){e=se(this.defaults,e);const t=mn(e.baseURL,e.url);return dn(t,e.params,e.paramsSerializer)}};f.forEach(["delete","get","head","options"],function(e){re.prototype[e]=function(t,r){return this.request(se(r||{},{method:e,url:t,data:(r||{}).data}))}});f.forEach(["post","put","patch"],function(e){function t(r){return function(o,i,a){return this.request(se(a||{},{method:e,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}re.prototype[e]=t(),re.prototype[e+"Form"]=t(!0)});let Vs=class Rn{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(o){t=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},e(function(o,i,a){r.reason||(r.reason=new de(o,i,a),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=r=>{e.abort(r)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Rn(function(s){e=s}),cancel:e}}};function Hs(n){return function(t){return n.apply(null,t)}}function Ks(n){return f.isObject(n)&&n.isAxiosError===!0}const rt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rt).forEach(([n,e])=>{rt[e]=n});function On(n){const e=new re(n),t=Yt(re.prototype.request,e);return f.extend(t,re.prototype,e,{allOwnKeys:!0}),f.extend(t,e,null,{allOwnKeys:!0}),t.create=function(s){return On(se(n,s))},t}const j=On(we);j.Axios=re;j.CanceledError=de;j.CancelToken=Vs;j.isCancel=pn;j.VERSION=Sn;j.toFormData=Me;j.AxiosError=O;j.Cancel=j.CanceledError;j.all=function(e){return Promise.all(e)};j.spread=Hs;j.isAxiosError=Ks;j.mergeConfig=se;j.AxiosHeaders=V;j.formToJSON=n=>hn(f.isHTMLForm(n)?new FormData(n):n);j.getAdapter=wn.getAdapter;j.HttpStatusCode=rt;j.default=j;const{Axios:mo,AxiosError:bo,CanceledError:yo,isCancel:xo,CancelToken:wo,VERSION:So,all:Ro,Cancel:Oo,isAxiosError:Eo,spread:vo,toFormData:Co,AxiosHeaders:Lo,HttpStatusCode:Ao,formToJSON:Po,getAdapter:To,mergeConfig:ko}=j,S=n=>typeof n=="string",ge=()=>{let n,e;const t=new Promise((r,s)=>{n=r,e=s});return t.resolve=n,t.reject=e,t},Tt=n=>n==null?"":""+n,_s=(n,e,t)=>{n.forEach(r=>{e[r]&&(t[r]=e[r])})},qs=/###/g,kt=n=>n&&n.indexOf("###")>-1?n.replace(qs,"."):n,Nt=n=>!n||S(n),me=(n,e,t)=>{const r=S(e)?e.split("."):e;let s=0;for(;s<r.length-1;){if(Nt(n))return{};const o=kt(r[s]);!n[o]&&t&&(n[o]=new t),Object.prototype.hasOwnProperty.call(n,o)?n=n[o]:n={},++s}return Nt(n)?{}:{obj:n,k:kt(r[s])}},Ft=(n,e,t)=>{const{obj:r,k:s}=me(n,e,Object);if(r!==void 0||e.length===1){r[s]=t;return}let o=e[e.length-1],i=e.slice(0,e.length-1),a=me(n,i,Object);for(;a.obj===void 0&&i.length;)o=`${i[i.length-1]}.${o}`,i=i.slice(0,i.length-1),a=me(n,i,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${o}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${o}`]=t},Js=(n,e,t,r)=>{const{obj:s,k:o}=me(n,e,Object);s[o]=s[o]||[],s[o].push(t)},ke=(n,e)=>{const{obj:t,k:r}=me(n,e);if(t&&Object.prototype.hasOwnProperty.call(t,r))return t[r]},Gs=(n,e,t)=>{const r=ke(n,t);return r!==void 0?r:ke(e,t)},En=(n,e,t)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in n?S(n[r])||n[r]instanceof String||S(e[r])||e[r]instanceof String?t&&(n[r]=e[r]):En(n[r],e[r],t):n[r]=e[r]);return n},ie=n=>n.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ws={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Xs=n=>S(n)?n.replace(/[&<>"'\/]/g,e=>Ws[e]):n;class Qs{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const Ys=[" ",",","?","!",";"],Zs=new Qs(20),eo=(n,e,t)=>{e=e||"",t=t||"";const r=Ys.filter(i=>e.indexOf(i)<0&&t.indexOf(i)<0);if(r.length===0)return!0;const s=Zs.getRegExp(`(${r.map(i=>i==="?"?"\\?":i).join("|")})`);let o=!s.test(n);if(!o){const i=n.indexOf(t);i>0&&!s.test(n.substring(0,i))&&(o=!0)}return o},st=function(n,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!n)return;if(n[e])return Object.prototype.hasOwnProperty.call(n,e)?n[e]:void 0;const r=e.split(t);let s=n;for(let o=0;o<r.length;){if(!s||typeof s!="object")return;let i,a="";for(let l=o;l<r.length;++l)if(l!==o&&(a+=t),a+=r[l],i=s[a],i!==void 0){if(["string","number","boolean"].indexOf(typeof i)>-1&&l<r.length-1)continue;o+=l-o+1;break}s=i}return s},Ne=n=>n==null?void 0:n.replace("_","-"),to={type:"logger",log(n){this.output("log",n)},warn(n){this.output("warn",n)},error(n){this.output("error",n)},output(n,e){var t,r;(r=(t=console==null?void 0:console[n])==null?void 0:t.apply)==null||r.call(t,console,e)}};class Fe{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||to,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,s){return s&&!this.debug?null:(S(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Fe(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Fe(this.logger,e)}}var W=new Fe;class Be{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const s=this.observers[r].get(t)||0;this.observers[r].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(i=>{let[a,l]=i;for(let c=0;c<l;c++)a(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(i=>{let[a,l]=i;for(let c=0;c<l;c++)a.apply(a,[e,...r])})}}class jt extends Be{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r){var c,u;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,i=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],r&&(Array.isArray(r)?a.push(...r):S(r)&&o?a.push(...r.split(o)):a.push(r)));const l=ke(this.data,a);return!l&&!t&&!r&&e.indexOf(".")>-1&&(e=a[0],t=a[1],r=a.slice(2).join(".")),l||!i||!S(r)?l:st((u=(c=this.data)==null?void 0:c[e])==null?void 0:u[t],r,o)}addResource(e,t,r,s){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const i=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let a=[e,t];r&&(a=a.concat(i?r.split(i):r)),e.indexOf(".")>-1&&(a=e.split("."),s=t,t=a[1]),this.addNamespaces(t),Ft(this.data,a,s),o.silent||this.emit("added",e,t,r,s)}addResources(e,t,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const o in r)(S(r[o])||Array.isArray(r[o]))&&this.addResource(e,t,o,r[o],{silent:!0});s.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,s,o){let i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),s=r,r=t,t=a[1]),this.addNamespaces(t);let l=ke(this.data,a)||{};i.skipCopy||(r=JSON.parse(JSON.stringify(r))),s?En(l,r,o):l={...l,...r},Ft(this.data,a,l),i.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(s=>t[s]&&Object.keys(t[s]).length>0)}toJSON(){return this.data}}var vn={processors:{},addPostProcessor(n){this.processors[n.name]=n},handle(n,e,t,r,s){return n.forEach(o=>{var i;e=((i=this.processors[o])==null?void 0:i.process(e,t,r,s))??e}),e}};const $t={},It=n=>!S(n)&&typeof n!="boolean"&&typeof n!="number";class je extends Be{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),_s(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=W.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const r=this.resolve(e,t);return(r==null?void 0:r.res)!==void 0}extractFromKey(e,t){let r=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const s=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const i=r&&e.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!eo(e,r,s);if(i&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:S(o)?[o]:o};const c=e.split(r);(r!==s||r===s&&this.options.ns.indexOf(c[0])>-1)&&(o=c.shift()),e=c.join(s)}return{key:e,namespaces:S(o)?[o]:o}}translate(e,t,r){if(typeof t!="object"&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),typeof t=="object"&&(t={...t}),t||(t={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const s=t.returnDetails!==void 0?t.returnDetails:this.options.returnDetails,o=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,{key:i,namespaces:a}=this.extractFromKey(e[e.length-1],t),l=a[a.length-1],c=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((c==null?void 0:c.toLowerCase())==="cimode"){if(u){const M=t.nsSeparator||this.options.nsSeparator;return s?{res:`${l}${M}${i}`,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${M}${i}`}return s?{res:i,usedKey:i,exactUsedKey:i,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:i}const d=this.resolve(e,t);let h=d==null?void 0:d.res;const b=(d==null?void 0:d.usedKey)||i,p=(d==null?void 0:d.exactUsedKey)||i,m=["[object Number]","[object Function]","[object RegExp]"],g=t.joinArrays!==void 0?t.joinArrays:this.options.joinArrays,R=!this.i18nFormat||this.i18nFormat.handleAsObject,E=t.count!==void 0&&!S(t.count),L=je.hasDefaultValue(t),P=E?this.pluralResolver.getSuffix(c,t.count,t):"",T=t.ordinal&&E?this.pluralResolver.getSuffix(c,t.count,{ordinal:!1}):"",w=E&&!t.ordinal&&t.count===0,v=w&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${P}`]||t[`defaultValue${T}`]||t.defaultValue;let N=h;R&&!h&&L&&(N=v);const K=It(N),q=Object.prototype.toString.apply(N);if(R&&N&&K&&m.indexOf(q)<0&&!(S(g)&&Array.isArray(N))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const M=this.options.returnedObjectHandler?this.options.returnedObjectHandler(b,N,{...t,ns:a}):`key '${i} (${this.language})' returned an object instead of string.`;return s?(d.res=M,d.usedParams=this.getUsedParamsDetails(t),d):M}if(o){const M=Array.isArray(N),B=M?[]:{},X=M?p:b;for(const F in N)if(Object.prototype.hasOwnProperty.call(N,F)){const D=`${X}${o}${F}`;L&&!h?B[F]=this.translate(D,{...t,defaultValue:It(v)?v[F]:void 0,joinArrays:!1,ns:a}):B[F]=this.translate(D,{...t,joinArrays:!1,ns:a}),B[F]===D&&(B[F]=N[F])}h=B}}else if(R&&S(g)&&Array.isArray(h))h=h.join(g),h&&(h=this.extendTranslation(h,e,t,r));else{let M=!1,B=!1;!this.isValidLookup(h)&&L&&(M=!0,h=v),this.isValidLookup(h)||(B=!0,h=i);const F=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&B?void 0:h,D=L&&v!==h&&this.options.updateMissing;if(B||M||D){if(this.logger.log(D?"updateKey":"missingKey",c,l,i,D?v:h),o){const k=this.resolve(i,{...t,keySeparator:!1});k&&k.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let A=[];const Q=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Q&&Q[0])for(let k=0;k<Q.length;k++)A.push(Q[k]);else this.options.saveMissingTo==="all"?A=this.languageUtils.toResolveHierarchy(t.lng||this.language):A.push(t.lng||this.language);const $=(k,_,Y)=>{var he;const fe=L&&Y!==h?Y:F;this.options.missingKeyHandler?this.options.missingKeyHandler(k,l,_,fe,D,t):(he=this.backendConnector)!=null&&he.saveMissing&&this.backendConnector.saveMissing(k,l,_,fe,D,t),this.emit("missingKey",k,l,_,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&E?A.forEach(k=>{const _=this.pluralResolver.getSuffixes(k,t);w&&t[`defaultValue${this.options.pluralSeparator}zero`]&&_.indexOf(`${this.options.pluralSeparator}zero`)<0&&_.push(`${this.options.pluralSeparator}zero`),_.forEach(Y=>{$([k],i+Y,t[`defaultValue${Y}`]||v)})}):$(A,i,v))}h=this.extendTranslation(h,e,t,d,r),B&&h===i&&this.options.appendNamespaceToMissingKey&&(h=`${l}:${i}`),(B||M)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${i}`:i,M?h:void 0))}return s?(d.res=h,d.usedParams=this.getUsedParamsDetails(t),d):h}extendTranslation(e,t,r,s,o){var c,u;var i=this;if((c=this.i18nFormat)!=null&&c.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const d=S(e)&&(((u=r==null?void 0:r.interpolation)==null?void 0:u.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let h;if(d){const p=e.match(this.interpolator.nestingRegexp);h=p&&p.length}let b=r.replace&&!S(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(b={...this.options.interpolation.defaultVariables,...b}),e=this.interpolator.interpolate(e,b,r.lng||this.language||s.usedLng,r),d){const p=e.match(this.interpolator.nestingRegexp),m=p&&p.length;h<m&&(r.nest=!1)}!r.lng&&s&&s.res&&(r.lng=this.language||s.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var p=arguments.length,m=new Array(p),g=0;g<p;g++)m[g]=arguments[g];return(o==null?void 0:o[0])===m[0]&&!r.context?(i.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${t[0]}`),null):i.translate(...m,t)},r)),r.interpolation&&this.interpolator.reset()}const a=r.postProcess||this.options.postProcess,l=S(a)?[a]:a;return e!=null&&(l!=null&&l.length)&&r.applyPostProcessor!==!1&&(e=vn.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,s,o,i,a;return S(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(r))return;const c=this.extractFromKey(l,t),u=c.key;s=u;let d=c.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));const h=t.count!==void 0&&!S(t.count),b=h&&!t.ordinal&&t.count===0,p=t.context!==void 0&&(S(t.context)||typeof t.context=="number")&&t.context!=="",m=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);d.forEach(g=>{var R,E;this.isValidLookup(r)||(a=g,!$t[`${m[0]}-${g}`]&&((R=this.utils)!=null&&R.hasLoadedNamespace)&&!((E=this.utils)!=null&&E.hasLoadedNamespace(a))&&($t[`${m[0]}-${g}`]=!0,this.logger.warn(`key "${s}" for languages "${m.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),m.forEach(L=>{var w;if(this.isValidLookup(r))return;i=L;const P=[u];if((w=this.i18nFormat)!=null&&w.addLookupKeys)this.i18nFormat.addLookupKeys(P,u,L,g,t);else{let v;h&&(v=this.pluralResolver.getSuffix(L,t.count,t));const N=`${this.options.pluralSeparator}zero`,K=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(h&&(P.push(u+v),t.ordinal&&v.indexOf(K)===0&&P.push(u+v.replace(K,this.options.pluralSeparator)),b&&P.push(u+N)),p){const q=`${u}${this.options.contextSeparator}${t.context}`;P.push(q),h&&(P.push(q+v),t.ordinal&&v.indexOf(K)===0&&P.push(q+v.replace(K,this.options.pluralSeparator)),b&&P.push(q+N))}}let T;for(;T=P.pop();)this.isValidLookup(r)||(o=T,r=this.getResource(L,g,T,t))}))})}),{res:r,usedKey:s,exactUsedKey:o,usedLng:i,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,r){var o;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return(o=this.i18nFormat)!=null&&o.getResource?this.i18nFormat.getResource(e,t,r,s):this.resourceStore.getResource(e,t,r,s)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!S(e.replace);let s=r?e.replace:e;if(r&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!r){s={...s};for(const o of t)delete s[o]}return s}static hasDefaultValue(e){const t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&e[r]!==void 0)return!0;return!1}}class Dt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=W.create("languageUtils")}getScriptPartFromCode(e){if(e=Ne(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=Ne(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(S(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(r=>{if(t)return;const s=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(r=>{if(t)return;const s=this.getLanguagePartFromCode(r);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find(o=>{if(o===s)return o;if(!(o.indexOf("-")<0&&s.indexOf("-")<0)&&(o.indexOf("-")>0&&s.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===s||o.indexOf(s)===0&&s.length>1))return o})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),S(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){const r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),s=[],o=i=>{i&&(this.isSupportedCode(i)?s.push(i):this.logger.warn(`rejecting language code not found in supportedLngs: ${i}`))};return S(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(e))):S(e)&&o(this.formatLanguageCode(e)),r.forEach(i=>{s.indexOf(i)<0&&o(this.formatLanguageCode(i))}),s}}const Mt={zero:0,one:1,two:2,few:3,many:4,other:5},Ut={select:n=>n===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class no{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=W.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=Ne(e==="dev"?"en":e),s=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:r,type:s});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let i;try{i=new Intl.PluralRules(r,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Ut;if(!e.match(/-|_/))return Ut;const l=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(l,t)}return this.pluralRulesCache[o]=i,i}needsPlural(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,r).map(s=>`${t}${s}`)}getSuffixes(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),r?r.resolvedOptions().pluralCategories.sort((s,o)=>Mt[s]-Mt[o]).map(s=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=this.getRule(e,r);return s?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${s.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,r))}}const zt=function(n,e,t){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=Gs(n,e,t);return!o&&s&&S(t)&&(o=st(n,t,r),o===void 0&&(o=st(e,t,r))),o},Ge=n=>n.replace(/\$/g,"$$$$");class ro{constructor(){var t;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=W.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(r=>r),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:r,useRawValueToEscape:s,prefix:o,prefixEscaped:i,suffix:a,suffixEscaped:l,formatSeparator:c,unescapeSuffix:u,unescapePrefix:d,nestingPrefix:h,nestingPrefixEscaped:b,nestingSuffix:p,nestingSuffixEscaped:m,nestingOptionsSeparator:g,maxReplaces:R,alwaysFormat:E}=e.interpolation;this.escape=t!==void 0?t:Xs,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=o?ie(o):i||"{{",this.suffix=a?ie(a):l||"}}",this.formatSeparator=c||",",this.unescapePrefix=u?"":d||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=h?ie(h):b||ie("$t("),this.nestingSuffix=p?ie(p):m||ie(")"),this.nestingOptionsSeparator=g||",",this.maxReplaces=R||1e3,this.alwaysFormat=E!==void 0?E:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,r)=>(t==null?void 0:t.source)===r?(t.lastIndex=0,t):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,s){var b;let o,i,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=p=>{if(p.indexOf(this.formatSeparator)<0){const E=zt(t,l,p,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(E,void 0,r,{...s,...t,interpolationkey:p}):E}const m=p.split(this.formatSeparator),g=m.shift().trim(),R=m.join(this.formatSeparator).trim();return this.format(zt(t,l,g,this.options.keySeparator,this.options.ignoreJSONStructure),R,r,{...s,...t,interpolationkey:g})};this.resetRegExp();const u=(s==null?void 0:s.missingInterpolationHandler)||this.options.missingInterpolationHandler,d=((b=s==null?void 0:s.interpolation)==null?void 0:b.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:p=>Ge(p)},{regex:this.regexp,safeValue:p=>this.escapeValue?Ge(this.escape(p)):Ge(p)}].forEach(p=>{for(a=0;o=p.regex.exec(e);){const m=o[1].trim();if(i=c(m),i===void 0)if(typeof u=="function"){const R=u(e,o,s);i=S(R)?R:""}else if(s&&Object.prototype.hasOwnProperty.call(s,m))i="";else if(d){i=o[0];continue}else this.logger.warn(`missed to pass in variable ${m} for interpolating ${e}`),i="";else!S(i)&&!this.useRawValueToEscape&&(i=Tt(i));const g=p.safeValue(i);if(e=e.replace(o[0],g),d?(p.regex.lastIndex+=i.length,p.regex.lastIndex-=o[0].length):p.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s,o,i;const a=(l,c)=>{const u=this.nestingOptionsSeparator;if(l.indexOf(u)<0)return l;const d=l.split(new RegExp(`${u}[ ]*{`));let h=`{${d[1]}`;l=d[0],h=this.interpolate(h,i);const b=h.match(/'/g),p=h.match(/"/g);(((b==null?void 0:b.length)??0)%2===0&&!p||p.length%2!==0)&&(h=h.replace(/'/g,'"'));try{i=JSON.parse(h),c&&(i={...c,...i})}catch(m){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,m),`${l}${u}${h}`}return i.defaultValue&&i.defaultValue.indexOf(this.prefix)>-1&&delete i.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];i={...r},i=i.replace&&!S(i.replace)?i.replace:i,i.applyPostProcessor=!1,delete i.defaultValue;let c=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const u=s[1].split(this.formatSeparator).map(d=>d.trim());s[1]=u.shift(),l=u,c=!0}if(o=t(a.call(this,s[1].trim(),i),i),o&&s[0]===e&&!S(o))return o;S(o)||(o=Tt(o)),o||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),o=""),c&&(o=l.reduce((u,d)=>this.format(u,d,r.lng,{...r,interpolationkey:s[1].trim()}),o.trim())),e=e.replace(s[0],o),this.regexp.lastIndex=0}return e}}const so=n=>{let e=n.toLowerCase().trim();const t={};if(n.indexOf("(")>-1){const r=n.split("(");e=r[0].toLowerCase().trim();const s=r[1].substring(0,r[1].length-1);e==="currency"&&s.indexOf(":")<0?t.currency||(t.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?t.range||(t.range=s.trim()):s.split(";").forEach(i=>{if(i){const[a,...l]=i.split(":"),c=l.join(":").trim().replace(/^'+|'+$/g,""),u=a.trim();t[u]||(t[u]=c),c==="false"&&(t[u]=!1),c==="true"&&(t[u]=!0),isNaN(c)||(t[u]=parseInt(c,10))}})}return{formatName:e,formatOptions:t}},ae=n=>{const e={};return(t,r,s)=>{let o=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(o={...o,[s.interpolationkey]:void 0});const i=r+JSON.stringify(o);let a=e[i];return a||(a=n(Ne(r),s),e[i]=a),a(t)}};class oo{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=W.create("formatter"),this.options=e,this.formats={number:ae((t,r)=>{const s=new Intl.NumberFormat(t,{...r});return o=>s.format(o)}),currency:ae((t,r)=>{const s=new Intl.NumberFormat(t,{...r,style:"currency"});return o=>s.format(o)}),datetime:ae((t,r)=>{const s=new Intl.DateTimeFormat(t,{...r});return o=>s.format(o)}),relativetime:ae((t,r)=>{const s=new Intl.RelativeTimeFormat(t,{...r});return o=>s.format(o,r.range||"day")}),list:ae((t,r)=>{const s=new Intl.ListFormat(t,{...r});return o=>s.format(o)})},this.init(e)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=ae(t)}format(e,t,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(a=>a.indexOf(")")>-1)){const a=o.findIndex(l=>l.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,a)].join(this.formatSeparator)}return o.reduce((a,l)=>{var d;const{formatName:c,formatOptions:u}=so(l);if(this.formats[c]){let h=a;try{const b=((d=s==null?void 0:s.formatParams)==null?void 0:d[s.interpolationkey])||{},p=b.locale||b.lng||s.locale||s.lng||r;h=this.formats[c](a,p,{...u,...s,...b})}catch(b){this.logger.warn(b)}return h}else this.logger.warn(`there was no format function for ${c}`);return a},e)}}const io=(n,e)=>{n.pending[e]!==void 0&&(delete n.pending[e],n.pendingCount--)};class ao extends Be{constructor(e,t,r){var o,i;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=s,this.logger=W.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],(i=(o=this.backend)==null?void 0:o.init)==null||i.call(o,r,s.backend,s)}queueLoad(e,t,r,s){const o={},i={},a={},l={};return e.forEach(c=>{let u=!0;t.forEach(d=>{const h=`${c}|${d}`;!r.reload&&this.store.hasResourceBundle(c,d)?this.state[h]=2:this.state[h]<0||(this.state[h]===1?i[h]===void 0&&(i[h]=!0):(this.state[h]=1,u=!1,i[h]===void 0&&(i[h]=!0),o[h]===void 0&&(o[h]=!0),l[d]===void 0&&(l[d]=!0)))}),u||(a[c]=!0)}),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,t,r){const s=e.split("|"),o=s[0],i=s[1];t&&this.emit("failedLoading",o,i,t),!t&&r&&this.store.addResourceBundle(o,i,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);const a={};this.queue.forEach(l=>{Js(l.loaded,[o],i),io(l,e),t&&l.errors.push(t),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(c=>{a[c]||(a[c]={});const u=l.loaded[c];u.length&&u.forEach(d=>{a[c][d]===void 0&&(a[c][d]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,t,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,i=arguments.length>5?arguments[5]:void 0;if(!e.length)return i(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:s,wait:o,callback:i});return}this.readingCalls++;const a=(c,u)=>{if(this.readingCalls--,this.waitingReads.length>0){const d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(c&&u&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,s+1,o*2,i)},o);return}i(c,u)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const c=l(e,t);c&&typeof c.then=="function"?c.then(u=>a(null,u)).catch(a):a(null,c)}catch(c){a(c)}return}return l(e,t,a)}prepareLoading(e,t){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();S(e)&&(e=this.languageUtils.toResolveHierarchy(e)),S(t)&&(t=[t]);const o=this.queueLoad(e,t,r,s);if(!o.toLoad.length)return o.pending.length||s(),null;o.toLoad.forEach(i=>{this.loadOne(i)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=e.split("|"),s=r[0],o=r[1];this.read(s,o,"read",void 0,void 0,(i,a)=>{i&&this.logger.warn(`${t}loading namespace ${o} for language ${s} failed`,i),!i&&a&&this.logger.log(`${t}loaded namespace ${o} for language ${s}`,a),this.loaded(e,i,a)})}saveMissing(e,t,r,s,o){var l,c,u,d,h;let i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},a=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if((c=(l=this.services)==null?void 0:l.utils)!=null&&c.hasLoadedNamespace&&!((d=(u=this.services)==null?void 0:u.utils)!=null&&d.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((h=this.backend)!=null&&h.create){const b={...i,isUpdate:o},p=this.backend.create.bind(this.backend);if(p.length<6)try{let m;p.length===5?m=p(e,t,r,s,b):m=p(e,t,r,s),m&&typeof m.then=="function"?m.then(g=>a(null,g)).catch(a):a(null,m)}catch(m){a(m)}else p(e,t,r,s,a,b)}!e||!e[0]||this.store.addResource(e[0],t,r,s)}}}const Bt=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:n=>{let e={};if(typeof n[1]=="object"&&(e=n[1]),S(n[1])&&(e.defaultValue=n[1]),S(n[2])&&(e.tDescription=n[2]),typeof n[2]=="object"||typeof n[3]=="object"){const t=n[3]||n[2];Object.keys(t).forEach(r=>{e[r]=t[r]})}return e},interpolation:{escapeValue:!0,format:n=>n,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Vt=n=>{var e,t;return S(n.ns)&&(n.ns=[n.ns]),S(n.fallbackLng)&&(n.fallbackLng=[n.fallbackLng]),S(n.fallbackNS)&&(n.fallbackNS=[n.fallbackNS]),((t=(e=n.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(n.supportedLngs=n.supportedLngs.concat(["cimode"])),typeof n.initImmediate=="boolean"&&(n.initAsync=n.initImmediate),n},ve=()=>{},lo=n=>{Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach(t=>{typeof n[t]=="function"&&(n[t]=n[t].bind(n))})};class ye extends Be{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Vt(e),this.services={},this.logger=W,this.modules={external:[]},lo(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof t=="function"&&(r=t,t={}),t.defaultNS==null&&t.ns&&(S(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const s=Bt();this.options={...s,...this.options,...Vt(t)},this.options.interpolation={...s.interpolation,...this.options.interpolation},t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const o=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?W.init(o(this.modules.logger),this.options):W.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=oo;const d=new Dt(this.options);this.store=new jt(this.options.resources,this.options);const h=this.services;h.logger=W,h.resourceStore=this.store,h.languageUtils=d,h.pluralResolver=new no(d,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),u&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(h.formatter=o(u),h.formatter.init(h,this.options),this.options.interpolation.format=h.formatter.format.bind(h.formatter)),h.interpolator=new ro(this.options),h.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},h.backendConnector=new ao(o(this.modules.backend),h.resourceStore,h,this.options),h.backendConnector.on("*",function(b){for(var p=arguments.length,m=new Array(p>1?p-1:0),g=1;g<p;g++)m[g-1]=arguments[g];e.emit(b,...m)}),this.modules.languageDetector&&(h.languageDetector=o(this.modules.languageDetector),h.languageDetector.init&&h.languageDetector.init(h,this.options.detection,this.options)),this.modules.i18nFormat&&(h.i18nFormat=o(this.modules.i18nFormat),h.i18nFormat.init&&h.i18nFormat.init(this)),this.translator=new je(this.services,this.options),this.translator.on("*",function(b){for(var p=arguments.length,m=new Array(p>1?p-1:0),g=1;g<p;g++)m[g-1]=arguments[g];e.emit(b,...m)}),this.modules.external.forEach(b=>{b.init&&b.init(this)})}if(this.format=this.options.interpolation.format,r||(r=ve),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=function(){return e.store[u](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=function(){return e.store[u](...arguments),e}});const l=ge(),c=()=>{const u=(d,h)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(h),r(d,h)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?c():setTimeout(c,0),l}loadResources(e){var o,i;let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ve;const s=S(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if((s==null?void 0:s.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const a=[],l=c=>{if(!c||c==="cimode")return;this.services.languageUtils.toResolveHierarchy(c).forEach(d=>{d!=="cimode"&&a.indexOf(d)<0&&a.push(d)})};s?l(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>l(u)),(i=(o=this.options.preload)==null?void 0:o.forEach)==null||i.call(o,c=>l(c)),this.services.backendConnector.load(a,this.options.ns,c=>{!c&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(c)})}else r(null)}reloadResources(e,t,r){const s=ge();return typeof e=="function"&&(r=e,e=void 0),typeof t=="function"&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=ve),this.services.backendConnector.reload(e,t,o=>{s.resolve(),r(o)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&vn.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const r=this.languages[t];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(e,t){var r=this;this.isLanguageChangingTo=e;const s=ge();this.emit("languageChanging",e);const o=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},i=(l,c)=>{c?(o(c),this.translator.changeLanguage(c),this.isLanguageChangingTo=void 0,this.emit("languageChanged",c),this.logger.log("languageChanged",c)):this.isLanguageChangingTo=void 0,s.resolve(function(){return r.t(...arguments)}),t&&t(l,function(){return r.t(...arguments)})},a=l=>{var u,d;!e&&!l&&this.services.languageDetector&&(l=[]);const c=S(l)?l:this.services.languageUtils.getBestMatchFromCodes(l);c&&(this.language||o(c),this.translator.language||this.translator.changeLanguage(c),(d=(u=this.services.languageDetector)==null?void 0:u.cacheUserLanguage)==null||d.call(u,c)),this.loadResources(c,h=>{i(h,c)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e),s}getFixedT(e,t,r){var s=this;const o=function(i,a){let l;if(typeof a!="object"){for(var c=arguments.length,u=new Array(c>2?c-2:0),d=2;d<c;d++)u[d-2]=arguments[d];l=s.options.overloadTranslationOptionHandler([i,a].concat(u))}else l={...a};l.lng=l.lng||o.lng,l.lngs=l.lngs||o.lngs,l.ns=l.ns||o.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||r||o.keyPrefix);const h=s.options.keySeparator||".";let b;return l.keyPrefix&&Array.isArray(i)?b=i.map(p=>`${l.keyPrefix}${h}${p}`):b=l.keyPrefix?`${l.keyPrefix}${h}${i}`:i,s.t(b,l)};return S(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=r,o}t(){var s;for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(s=this.translator)==null?void 0:s.translate(...t)}exists(){var s;for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return(s=this.translator)==null?void 0:s.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=t.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const i=(a,l)=>{const c=this.services.backendConnector.state[`${a}|${l}`];return c===-1||c===0||c===2};if(t.precheck){const a=t.precheck(this,i);if(a!==void 0)return a}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||i(r,e)&&(!s||i(o,e)))}loadNamespaces(e,t){const r=ge();return this.options.ns?(S(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{r.resolve(),t&&t(s)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=ge();S(e)&&(e=[e]);const s=this.options.preload||[],o=e.filter(i=>s.indexOf(i)<0&&this.services.languageUtils.isSupportedCode(i));return o.length?(this.options.preload=s.concat(o),this.loadResources(i=>{r.resolve(),t&&t(i)}),r):(t&&t(),Promise.resolve())}dir(e){var s,o;if(e||(e=this.resolvedLanguage||(((s=this.languages)==null?void 0:s.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((o=this.services)==null?void 0:o.languageUtils)||new Dt(Bt());return t.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ye(e,t)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ve;const r=e.forkResourceStore;r&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},o=new ye(s);if((e.debug!==void 0||e.prefix!==void 0)&&(o.logger=o.logger.clone(e)),["store","services","language"].forEach(a=>{o[a]=this[a]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},r){const a=Object.keys(this.store.data).reduce((l,c)=>(l[c]={...this.store.data[c]},Object.keys(l[c]).reduce((u,d)=>(u[d]={...l[c][d]},u),{})),{});o.store=new jt(a,s),o.services.resourceStore=o.store}return o.translator=new je(o.services,s),o.translator.on("*",function(a){for(var l=arguments.length,c=new Array(l>1?l-1:0),u=1;u<l;u++)c[u-1]=arguments[u];o.emit(a,...c)}),o.init(s,t),o.translator.options=s,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const z=ye.createInstance();z.createInstance=ye.createInstance;z.createInstance;z.dir;z.init;z.loadResources;z.reloadResources;z.use;z.changeLanguage;z.getFixedT;z.t;z.exists;z.setDefaultNamespace;z.hasLoadedNamespace;z.loadNamespaces;z.loadLanguages;const No=Object.freeze(Object.defineProperty({__proto__:null,default:z},Symbol.toStringTag,{value:"Module"}));export{uo as a,j as b,fo as c,No as d,z as i,ho as t};
