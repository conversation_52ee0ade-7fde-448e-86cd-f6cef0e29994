import{j as o,Y as td,O as fg,k as dg,u as ad,Z as mg,c as hg,l as gg,g as pg,S as yg,T as vg,n as bg,m as nd,o as Sg,p as Tg,$ as ud,a0 as id,a1 as cd,a2 as xg}from"./ui-vendor-CeCm8EER.js";import{d as Ag,h as Dg,r as E,u as sd,H as Ng,i as Eg,j as kf}from"./react-vendor-DEwriMA6.js";import{z as we,c as Ve,a8 as od,u as Bl,y as Gt,a9 as rd,aa as fd,I as us,B as Cn,D as Mg,i as zg,j as Cg,k as Og,l as jg,ab as Rg,ac as _g,ad as Ug,ae as Hg,af as Ll,ag as dd,ah as ss,ai as is,W as Lg,Y as Bg,Z as qg,_ as Gg,aj as Yg,ak as Xg,al as md,am as wg,an as Vg,ao as hd,ap as Qg,aq as gd,C as Zg,J as Kg,K as kg,d as En,ar as Jg,as as Fg,at as Pg}from"./feature-graph-NODQb6qW.js";import{S as Jf,a as Ff,b as Pf,c as $f,d as ot,R as $g}from"./feature-retrieval-DalFy9WB.js";import{D as Wg}from"./feature-documents-oks3sUnM.js";import{i as cs}from"./utils-vendor-BysuhMZA.js";import"./graph-vendor-B-X5JegA.js";import"./mermaid-vendor-D0f_SE0h.js";import"./markdown-vendor-DmIvJdn7.js";(function(){const y=document.createElement("link").relList;if(y&&y.supports&&y.supports("modulepreload"))return;for(const N of document.querySelectorAll('link[rel="modulepreload"]'))d(N);new MutationObserver(N=>{for(const j of N)if(j.type==="childList")for(const H of j.addedNodes)H.tagName==="LINK"&&H.rel==="modulepreload"&&d(H)}).observe(document,{childList:!0,subtree:!0});function x(N){const j={};return N.integrity&&(j.integrity=N.integrity),N.referrerPolicy&&(j.referrerPolicy=N.referrerPolicy),N.crossOrigin==="use-credentials"?j.credentials="include":N.crossOrigin==="anonymous"?j.credentials="omit":j.credentials="same-origin",j}function d(N){if(N.ep)return;N.ep=!0;const j=x(N);fetch(N.href,j)}})();var ts={exports:{}},Mn={},as={exports:{}},ns={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wf;function Ig(){return Wf||(Wf=1,function(h){function y(A,L){var _=A.length;A.push(L);e:for(;0<_;){var te=_-1>>>1,oe=A[te];if(0<N(oe,L))A[te]=L,A[_]=oe,_=te;else break e}}function x(A){return A.length===0?null:A[0]}function d(A){if(A.length===0)return null;var L=A[0],_=A.pop();if(_!==L){A[0]=_;e:for(var te=0,oe=A.length,Xt=oe>>>1;te<Xt;){var pt=2*(te+1)-1,vl=A[pt],Q=pt+1,Qe=A[Q];if(0>N(vl,_))Q<oe&&0>N(Qe,vl)?(A[te]=Qe,A[Q]=_,te=Q):(A[te]=vl,A[pt]=_,te=pt);else if(Q<oe&&0>N(Qe,_))A[te]=Qe,A[Q]=_,te=Q;else break e}}return L}function N(A,L){var _=A.sortIndex-L.sortIndex;return _!==0?_:A.id-L.id}if(h.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var j=performance;h.unstable_now=function(){return j.now()}}else{var H=Date,P=H.now();h.unstable_now=function(){return H.now()-P}}var Y=[],$=[],he=1,ge=null,w=3,pe=!1,le=!1,C=!1,pl=typeof setTimeout=="function"?setTimeout:null,rt=typeof clearTimeout=="function"?clearTimeout:null,je=typeof setImmediate<"u"?setImmediate:null;function ft(A){for(var L=x($);L!==null;){if(L.callback===null)d($);else if(L.startTime<=A)d($),L.sortIndex=L.expirationTime,y(Y,L);else break;L=x($)}}function Ea(A){if(C=!1,ft(A),!le)if(x(Y)!==null)le=!0,ht();else{var L=x($);L!==null&&gt(Ea,L.startTime-A)}}var dt=!1,al=-1,On=5,Yt=-1;function R(){return!(h.unstable_now()-Yt<On)}function k(){if(dt){var A=h.unstable_now();Yt=A;var L=!0;try{e:{le=!1,C&&(C=!1,rt(al),al=-1),pe=!0;var _=w;try{l:{for(ft(A),ge=x(Y);ge!==null&&!(ge.expirationTime>A&&R());){var te=ge.callback;if(typeof te=="function"){ge.callback=null,w=ge.priorityLevel;var oe=te(ge.expirationTime<=A);if(A=h.unstable_now(),typeof oe=="function"){ge.callback=oe,ft(A),L=!0;break l}ge===x(Y)&&d(Y),ft(A)}else d(Y);ge=x(Y)}if(ge!==null)L=!0;else{var Xt=x($);Xt!==null&&gt(Ea,Xt.startTime-A),L=!1}}break e}finally{ge=null,w=_,pe=!1}L=void 0}}finally{L?yl():dt=!1}}}var yl;if(typeof je=="function")yl=function(){je(k)};else if(typeof MessageChannel<"u"){var Ma=new MessageChannel,mt=Ma.port2;Ma.port1.onmessage=k,yl=function(){mt.postMessage(null)}}else yl=function(){pl(k,0)};function ht(){dt||(dt=!0,yl())}function gt(A,L){al=pl(function(){A(h.unstable_now())},L)}h.unstable_IdlePriority=5,h.unstable_ImmediatePriority=1,h.unstable_LowPriority=4,h.unstable_NormalPriority=3,h.unstable_Profiling=null,h.unstable_UserBlockingPriority=2,h.unstable_cancelCallback=function(A){A.callback=null},h.unstable_continueExecution=function(){le||pe||(le=!0,ht())},h.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):On=0<A?Math.floor(1e3/A):5},h.unstable_getCurrentPriorityLevel=function(){return w},h.unstable_getFirstCallbackNode=function(){return x(Y)},h.unstable_next=function(A){switch(w){case 1:case 2:case 3:var L=3;break;default:L=w}var _=w;w=L;try{return A()}finally{w=_}},h.unstable_pauseExecution=function(){},h.unstable_requestPaint=function(){},h.unstable_runWithPriority=function(A,L){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var _=w;w=A;try{return L()}finally{w=_}},h.unstable_scheduleCallback=function(A,L,_){var te=h.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?te+_:te):_=te,A){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=1073741823;break;case 4:oe=1e4;break;default:oe=5e3}return oe=_+oe,A={id:he++,callback:L,priorityLevel:A,startTime:_,expirationTime:oe,sortIndex:-1},_>te?(A.sortIndex=_,y($,A),x(Y)===null&&A===x($)&&(C?(rt(al),al=-1):C=!0,gt(Ea,_-te))):(A.sortIndex=oe,y(Y,A),le||pe||(le=!0,ht())),A},h.unstable_shouldYield=R,h.unstable_wrapCallback=function(A){var L=w;return function(){var _=w;w=L;try{return A.apply(this,arguments)}finally{w=_}}}}(ns)),ns}var If;function ep(){return If||(If=1,as.exports=Ig()),as.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ed;function lp(){if(ed)return Mn;ed=1;var h=ep(),y=Ag(),x=Dg();function d(e){var l="https://react.dev/errors/"+e;if(1<arguments.length){l+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)l+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+l+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function N(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}var j=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),P=Symbol.for("react.portal"),Y=Symbol.for("react.fragment"),$=Symbol.for("react.strict_mode"),he=Symbol.for("react.profiler"),ge=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),pe=Symbol.for("react.context"),le=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),pl=Symbol.for("react.suspense_list"),rt=Symbol.for("react.memo"),je=Symbol.for("react.lazy"),ft=Symbol.for("react.offscreen"),Ea=Symbol.for("react.memo_cache_sentinel"),dt=Symbol.iterator;function al(e){return e===null||typeof e!="object"?null:(e=dt&&e[dt]||e["@@iterator"],typeof e=="function"?e:null)}var On=Symbol.for("react.client.reference");function Yt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===On?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Y:return"Fragment";case P:return"Portal";case he:return"Profiler";case $:return"StrictMode";case C:return"Suspense";case pl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case le:var l=e.render;return e=e.displayName,e||(e=l.displayName||l.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case rt:return l=e.displayName||null,l!==null?l:Yt(e.type)||"Memo";case je:l=e._payload,e=e._init;try{return Yt(e(l))}catch{}}return null}var R=y.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=Object.assign,yl,Ma;function mt(e){if(yl===void 0)try{throw Error()}catch(t){var l=t.stack.trim().match(/\n( *(at )?)/);yl=l&&l[1]||"",Ma=-1<t.stack.indexOf(`
    at`)?" (<anonymous>)":-1<t.stack.indexOf("@")?"@unknown:0:0":""}return`
`+yl+e+Ma}var ht=!1;function gt(e,l){if(!e||ht)return"";ht=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(l){var T=function(){throw Error()};if(Object.defineProperty(T.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(T,[])}catch(v){var p=v}Reflect.construct(e,[],T)}else{try{T.call()}catch(v){p=v}e.call(T.prototype)}}else{try{throw Error()}catch(v){p=v}(T=e())&&typeof T.catch=="function"&&T.catch(function(){})}}catch(v){if(v&&p&&typeof v.stack=="string")return[v.stack,p.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],c=u[1];if(i&&c){var s=i.split(`
`),f=c.split(`
`);for(n=a=0;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;for(;n<f.length&&!f[n].includes("DetermineComponentFrameRoot");)n++;if(a===s.length||n===f.length)for(a=s.length-1,n=f.length-1;1<=a&&0<=n&&s[a]!==f[n];)n--;for(;1<=a&&0<=n;a--,n--)if(s[a]!==f[n]){if(a!==1||n!==1)do if(a--,n--,0>n||s[a]!==f[n]){var b=`
`+s[a].replace(" at new "," at ");return e.displayName&&b.includes("<anonymous>")&&(b=b.replace("<anonymous>",e.displayName)),b}while(1<=a&&0<=n);break}}}finally{ht=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?mt(t):""}function A(e){switch(e.tag){case 26:case 27:case 5:return mt(e.type);case 16:return mt("Lazy");case 13:return mt("Suspense");case 19:return mt("SuspenseList");case 0:case 15:return e=gt(e.type,!1),e;case 11:return e=gt(e.type.render,!1),e;case 1:return e=gt(e.type,!0),e;default:return""}}function L(e){try{var l="";do l+=A(e),e=e.return;while(e);return l}catch(t){return`
Error generating stack: `+t.message+`
`+t.stack}}function _(e){var l=e,t=e;if(e.alternate)for(;l.return;)l=l.return;else{e=l;do l=e,l.flags&4098&&(t=l.return),e=l.return;while(e)}return l.tag===3?t:null}function te(e){if(e.tag===13){var l=e.memoizedState;if(l===null&&(e=e.alternate,e!==null&&(l=e.memoizedState)),l!==null)return l.dehydrated}return null}function oe(e){if(_(e)!==e)throw Error(d(188))}function Xt(e){var l=e.alternate;if(!l){if(l=_(e),l===null)throw Error(d(188));return l!==e?null:e}for(var t=e,a=l;;){var n=t.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){t=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===t)return oe(n),e;if(u===a)return oe(n),l;u=u.sibling}throw Error(d(188))}if(t.return!==a.return)t=n,a=u;else{for(var i=!1,c=n.child;c;){if(c===t){i=!0,t=n,a=u;break}if(c===a){i=!0,a=n,t=u;break}c=c.sibling}if(!i){for(c=u.child;c;){if(c===t){i=!0,t=u,a=n;break}if(c===a){i=!0,a=u,t=n;break}c=c.sibling}if(!i)throw Error(d(189))}}if(t.alternate!==a)throw Error(d(190))}if(t.tag!==3)throw Error(d(188));return t.stateNode.current===t?e:l}function pt(e){var l=e.tag;if(l===5||l===26||l===27||l===6)return e;for(e=e.child;e!==null;){if(l=pt(e),l!==null)return l;e=e.sibling}return null}var vl=Array.isArray,Q=x.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Qe={pending:!1,data:null,method:null,action:null},Ku=[],wt=-1;function sl(e){return{current:e}}function be(e){0>wt||(e.current=Ku[wt],Ku[wt]=null,wt--)}function ae(e,l){wt++,Ku[wt]=e.current,e.current=l}var ol=sl(null),za=sl(null),Gl=sl(null),jn=sl(null);function Rn(e,l){switch(ae(Gl,l),ae(za,e),ae(ol,null),e=l.nodeType,e){case 9:case 11:l=(l=l.documentElement)&&(l=l.namespaceURI)?xf(l):0;break;default:if(e=e===8?l.parentNode:l,l=e.tagName,e=e.namespaceURI)e=xf(e),l=Af(e,l);else switch(l){case"svg":l=1;break;case"math":l=2;break;default:l=0}}be(ol),ae(ol,l)}function Vt(){be(ol),be(za),be(Gl)}function ku(e){e.memoizedState!==null&&ae(jn,e);var l=ol.current,t=Af(l,e.type);l!==t&&(ae(za,e),ae(ol,t))}function _n(e){za.current===e&&(be(ol),be(za)),jn.current===e&&(be(jn),Tn._currentValue=Qe)}var Ju=Object.prototype.hasOwnProperty,Fu=h.unstable_scheduleCallback,Pu=h.unstable_cancelCallback,Vd=h.unstable_shouldYield,Qd=h.unstable_requestPaint,rl=h.unstable_now,Zd=h.unstable_getCurrentPriorityLevel,os=h.unstable_ImmediatePriority,rs=h.unstable_UserBlockingPriority,Un=h.unstable_NormalPriority,Kd=h.unstable_LowPriority,fs=h.unstable_IdlePriority,kd=h.log,Jd=h.unstable_setDisableYieldValue,Ca=null,He=null;function Fd(e){if(He&&typeof He.onCommitFiberRoot=="function")try{He.onCommitFiberRoot(Ca,e,void 0,(e.current.flags&128)===128)}catch{}}function Yl(e){if(typeof kd=="function"&&Jd(e),He&&typeof He.setStrictMode=="function")try{He.setStrictMode(Ca,e)}catch{}}var Le=Math.clz32?Math.clz32:Wd,Pd=Math.log,$d=Math.LN2;function Wd(e){return e>>>=0,e===0?32:31-(Pd(e)/$d|0)|0}var Hn=128,Ln=4194304;function yt(e){var l=e&42;if(l!==0)return l;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Bn(e,l){var t=e.pendingLanes;if(t===0)return 0;var a=0,n=e.suspendedLanes,u=e.pingedLanes,i=e.warmLanes;e=e.finishedLanes!==0;var c=t&134217727;return c!==0?(t=c&~n,t!==0?a=yt(t):(u&=c,u!==0?a=yt(u):e||(i=c&~i,i!==0&&(a=yt(i))))):(c=t&~n,c!==0?a=yt(c):u!==0?a=yt(u):e||(i=t&~i,i!==0&&(a=yt(i)))),a===0?0:l!==0&&l!==a&&!(l&n)&&(n=a&-a,i=l&-l,n>=i||n===32&&(i&4194176)!==0)?l:a}function Oa(e,l){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&l)===0}function Id(e,l){switch(e){case 1:case 2:case 4:case 8:return l+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return l+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ds(){var e=Hn;return Hn<<=1,!(Hn&4194176)&&(Hn=128),e}function ms(){var e=Ln;return Ln<<=1,!(Ln&62914560)&&(Ln=4194304),e}function $u(e){for(var l=[],t=0;31>t;t++)l.push(e);return l}function ja(e,l){e.pendingLanes|=l,l!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function em(e,l,t,a,n,u){var i=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0;var c=e.entanglements,s=e.expirationTimes,f=e.hiddenUpdates;for(t=i&~t;0<t;){var b=31-Le(t),T=1<<b;c[b]=0,s[b]=-1;var p=f[b];if(p!==null)for(f[b]=null,b=0;b<p.length;b++){var v=p[b];v!==null&&(v.lane&=-536870913)}t&=~T}a!==0&&hs(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(i&~l))}function hs(e,l,t){e.pendingLanes|=l,e.suspendedLanes&=~l;var a=31-Le(l);e.entangledLanes|=l,e.entanglements[a]=e.entanglements[a]|1073741824|t&4194218}function gs(e,l){var t=e.entangledLanes|=l;for(e=e.entanglements;t;){var a=31-Le(t),n=1<<a;n&l|e[a]&l&&(e[a]|=l),t&=~n}}function ps(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function ys(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:Xf(e.type))}function lm(e,l){var t=Q.p;try{return Q.p=e,l()}finally{Q.p=t}}var Xl=Math.random().toString(36).slice(2),Me="__reactFiber$"+Xl,Re="__reactProps$"+Xl,Qt="__reactContainer$"+Xl,Wu="__reactEvents$"+Xl,tm="__reactListeners$"+Xl,am="__reactHandles$"+Xl,vs="__reactResources$"+Xl,Ra="__reactMarker$"+Xl;function Iu(e){delete e[Me],delete e[Re],delete e[Wu],delete e[tm],delete e[am]}function vt(e){var l=e[Me];if(l)return l;for(var t=e.parentNode;t;){if(l=t[Qt]||t[Me]){if(t=l.alternate,l.child!==null||t!==null&&t.child!==null)for(e=Ef(e);e!==null;){if(t=e[Me])return t;e=Ef(e)}return l}e=t,t=e.parentNode}return null}function Zt(e){if(e=e[Me]||e[Qt]){var l=e.tag;if(l===5||l===6||l===13||l===26||l===27||l===3)return e}return null}function _a(e){var l=e.tag;if(l===5||l===26||l===27||l===6)return e.stateNode;throw Error(d(33))}function Kt(e){var l=e[vs];return l||(l=e[vs]={hoistableStyles:new Map,hoistableScripts:new Map}),l}function Se(e){e[Ra]=!0}var bs=new Set,Ss={};function bt(e,l){kt(e,l),kt(e+"Capture",l)}function kt(e,l){for(Ss[e]=l,e=0;e<l.length;e++)bs.add(l[e])}var bl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ts={},xs={};function um(e){return Ju.call(xs,e)?!0:Ju.call(Ts,e)?!1:nm.test(e)?xs[e]=!0:(Ts[e]=!0,!1)}function qn(e,l,t){if(um(l))if(t===null)e.removeAttribute(l);else{switch(typeof t){case"undefined":case"function":case"symbol":e.removeAttribute(l);return;case"boolean":var a=l.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(l);return}}e.setAttribute(l,""+t)}}function Gn(e,l,t){if(t===null)e.removeAttribute(l);else{switch(typeof t){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttribute(l,""+t)}}function Sl(e,l,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttributeNS(l,t,""+a)}}function Ze(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function As(e){var l=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(l==="checkbox"||l==="radio")}function im(e){var l=As(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,l),a=""+e[l];if(!e.hasOwnProperty(l)&&typeof t<"u"&&typeof t.get=="function"&&typeof t.set=="function"){var n=t.get,u=t.set;return Object.defineProperty(e,l,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(e,l,{enumerable:t.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){e._valueTracker=null,delete e[l]}}}}function Yn(e){e._valueTracker||(e._valueTracker=im(e))}function Ds(e){if(!e)return!1;var l=e._valueTracker;if(!l)return!0;var t=l.getValue(),a="";return e&&(a=As(e)?e.checked?"true":"false":e.value),e=a,e!==t?(l.setValue(e),!0):!1}function Xn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var cm=/[\n"\\]/g;function Ke(e){return e.replace(cm,function(l){return"\\"+l.charCodeAt(0).toString(16)+" "})}function ei(e,l,t,a,n,u,i,c){e.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?e.type=i:e.removeAttribute("type"),l!=null?i==="number"?(l===0&&e.value===""||e.value!=l)&&(e.value=""+Ze(l)):e.value!==""+Ze(l)&&(e.value=""+Ze(l)):i!=="submit"&&i!=="reset"||e.removeAttribute("value"),l!=null?li(e,i,Ze(l)):t!=null?li(e,i,Ze(t)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?e.name=""+Ze(c):e.removeAttribute("name")}function Ns(e,l,t,a,n,u,i,c){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),l!=null||t!=null){if(!(u!=="submit"&&u!=="reset"||l!=null))return;t=t!=null?""+Ze(t):"",l=l!=null?""+Ze(l):t,c||l===e.value||(e.value=l),e.defaultValue=l}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=c?e.checked:!!a,e.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.name=i)}function li(e,l,t){l==="number"&&Xn(e.ownerDocument)===e||e.defaultValue===""+t||(e.defaultValue=""+t)}function Jt(e,l,t,a){if(e=e.options,l){l={};for(var n=0;n<t.length;n++)l["$"+t[n]]=!0;for(t=0;t<e.length;t++)n=l.hasOwnProperty("$"+e[t].value),e[t].selected!==n&&(e[t].selected=n),n&&a&&(e[t].defaultSelected=!0)}else{for(t=""+Ze(t),l=null,n=0;n<e.length;n++){if(e[n].value===t){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}l!==null||e[n].disabled||(l=e[n])}l!==null&&(l.selected=!0)}}function Es(e,l,t){if(l!=null&&(l=""+Ze(l),l!==e.value&&(e.value=l),t==null)){e.defaultValue!==l&&(e.defaultValue=l);return}e.defaultValue=t!=null?""+Ze(t):""}function Ms(e,l,t,a){if(l==null){if(a!=null){if(t!=null)throw Error(d(92));if(vl(a)){if(1<a.length)throw Error(d(93));a=a[0]}t=a}t==null&&(t=""),l=t}t=Ze(l),e.defaultValue=t,a=e.textContent,a===t&&a!==""&&a!==null&&(e.value=a)}function Ft(e,l){if(l){var t=e.firstChild;if(t&&t===e.lastChild&&t.nodeType===3){t.nodeValue=l;return}}e.textContent=l}var sm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function zs(e,l,t){var a=l.indexOf("--")===0;t==null||typeof t=="boolean"||t===""?a?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="":a?e.setProperty(l,t):typeof t!="number"||t===0||sm.has(l)?l==="float"?e.cssFloat=t:e[l]=(""+t).trim():e[l]=t+"px"}function Cs(e,l,t){if(l!=null&&typeof l!="object")throw Error(d(62));if(e=e.style,t!=null){for(var a in t)!t.hasOwnProperty(a)||l!=null&&l.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in l)a=l[n],l.hasOwnProperty(n)&&t[n]!==a&&zs(e,n,a)}else for(var u in l)l.hasOwnProperty(u)&&zs(e,u,l[u])}function ti(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var om=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),rm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function wn(e){return rm.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ai=null;function ni(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Pt=null,$t=null;function Os(e){var l=Zt(e);if(l&&(e=l.stateNode)){var t=e[Re]||null;e:switch(e=l.stateNode,l.type){case"input":if(ei(e,t.value,t.defaultValue,t.defaultValue,t.checked,t.defaultChecked,t.type,t.name),l=t.name,t.type==="radio"&&l!=null){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll('input[name="'+Ke(""+l)+'"][type="radio"]'),l=0;l<t.length;l++){var a=t[l];if(a!==e&&a.form===e.form){var n=a[Re]||null;if(!n)throw Error(d(90));ei(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(l=0;l<t.length;l++)a=t[l],a.form===e.form&&Ds(a)}break e;case"textarea":Es(e,t.value,t.defaultValue);break e;case"select":l=t.value,l!=null&&Jt(e,!!t.multiple,l,!1)}}}var ui=!1;function js(e,l,t){if(ui)return e(l,t);ui=!0;try{var a=e(l);return a}finally{if(ui=!1,(Pt!==null||$t!==null)&&(Nu(),Pt&&(l=Pt,e=$t,$t=Pt=null,Os(l),e)))for(l=0;l<e.length;l++)Os(e[l])}}function Ua(e,l){var t=e.stateNode;if(t===null)return null;var a=t[Re]||null;if(a===null)return null;t=a[l];e:switch(l){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(t&&typeof t!="function")throw Error(d(231,l,typeof t));return t}var ii=!1;if(bl)try{var Ha={};Object.defineProperty(Ha,"passive",{get:function(){ii=!0}}),window.addEventListener("test",Ha,Ha),window.removeEventListener("test",Ha,Ha)}catch{ii=!1}var wl=null,ci=null,Vn=null;function Rs(){if(Vn)return Vn;var e,l=ci,t=l.length,a,n="value"in wl?wl.value:wl.textContent,u=n.length;for(e=0;e<t&&l[e]===n[e];e++);var i=t-e;for(a=1;a<=i&&l[t-a]===n[u-a];a++);return Vn=n.slice(e,1<a?1-a:void 0)}function Qn(e){var l=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&l===13&&(e=13)):e=l,e===10&&(e=13),32<=e||e===13?e:0}function Zn(){return!0}function _s(){return!1}function _e(e){function l(t,a,n,u,i){this._reactName=t,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var c in e)e.hasOwnProperty(c)&&(t=e[c],this[c]=t?t(u):u[c]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Zn:_s,this.isPropagationStopped=_s,this}return k(l.prototype,{preventDefault:function(){this.defaultPrevented=!0;var t=this.nativeEvent;t&&(t.preventDefault?t.preventDefault():typeof t.returnValue!="unknown"&&(t.returnValue=!1),this.isDefaultPrevented=Zn)},stopPropagation:function(){var t=this.nativeEvent;t&&(t.stopPropagation?t.stopPropagation():typeof t.cancelBubble!="unknown"&&(t.cancelBubble=!0),this.isPropagationStopped=Zn)},persist:function(){},isPersistent:Zn}),l}var St={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Kn=_e(St),La=k({},St,{view:0,detail:0}),fm=_e(La),si,oi,Ba,kn=k({},La,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ba&&(Ba&&e.type==="mousemove"?(si=e.screenX-Ba.screenX,oi=e.screenY-Ba.screenY):oi=si=0,Ba=e),si)},movementY:function(e){return"movementY"in e?e.movementY:oi}}),Us=_e(kn),dm=k({},kn,{dataTransfer:0}),mm=_e(dm),hm=k({},La,{relatedTarget:0}),ri=_e(hm),gm=k({},St,{animationName:0,elapsedTime:0,pseudoElement:0}),pm=_e(gm),ym=k({},St,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vm=_e(ym),bm=k({},St,{data:0}),Hs=_e(bm),Sm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Am(e){var l=this.nativeEvent;return l.getModifierState?l.getModifierState(e):(e=xm[e])?!!l[e]:!1}function fi(){return Am}var Dm=k({},La,{key:function(e){if(e.key){var l=Sm[e.key]||e.key;if(l!=="Unidentified")return l}return e.type==="keypress"?(e=Qn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Tm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fi,charCode:function(e){return e.type==="keypress"?Qn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nm=_e(Dm),Em=k({},kn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ls=_e(Em),Mm=k({},La,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fi}),zm=_e(Mm),Cm=k({},St,{propertyName:0,elapsedTime:0,pseudoElement:0}),Om=_e(Cm),jm=k({},kn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rm=_e(jm),_m=k({},St,{newState:0,oldState:0}),Um=_e(_m),Hm=[9,13,27,32],di=bl&&"CompositionEvent"in window,qa=null;bl&&"documentMode"in document&&(qa=document.documentMode);var Lm=bl&&"TextEvent"in window&&!qa,Bs=bl&&(!di||qa&&8<qa&&11>=qa),qs=" ",Gs=!1;function Ys(e,l){switch(e){case"keyup":return Hm.indexOf(l.keyCode)!==-1;case"keydown":return l.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xs(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wt=!1;function Bm(e,l){switch(e){case"compositionend":return Xs(l);case"keypress":return l.which!==32?null:(Gs=!0,qs);case"textInput":return e=l.data,e===qs&&Gs?null:e;default:return null}}function qm(e,l){if(Wt)return e==="compositionend"||!di&&Ys(e,l)?(e=Rs(),Vn=ci=wl=null,Wt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(l.ctrlKey||l.altKey||l.metaKey)||l.ctrlKey&&l.altKey){if(l.char&&1<l.char.length)return l.char;if(l.which)return String.fromCharCode(l.which)}return null;case"compositionend":return Bs&&l.locale!=="ko"?null:l.data;default:return null}}var Gm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ws(e){var l=e&&e.nodeName&&e.nodeName.toLowerCase();return l==="input"?!!Gm[e.type]:l==="textarea"}function Vs(e,l,t,a){Pt?$t?$t.push(a):$t=[a]:Pt=a,l=Ou(l,"onChange"),0<l.length&&(t=new Kn("onChange","change",null,t,a),e.push({event:t,listeners:l}))}var Ga=null,Ya=null;function Ym(e){yf(e,0)}function Jn(e){var l=_a(e);if(Ds(l))return e}function Qs(e,l){if(e==="change")return l}var Zs=!1;if(bl){var mi;if(bl){var hi="oninput"in document;if(!hi){var Ks=document.createElement("div");Ks.setAttribute("oninput","return;"),hi=typeof Ks.oninput=="function"}mi=hi}else mi=!1;Zs=mi&&(!document.documentMode||9<document.documentMode)}function ks(){Ga&&(Ga.detachEvent("onpropertychange",Js),Ya=Ga=null)}function Js(e){if(e.propertyName==="value"&&Jn(Ya)){var l=[];Vs(l,Ya,e,ni(e)),js(Ym,l)}}function Xm(e,l,t){e==="focusin"?(ks(),Ga=l,Ya=t,Ga.attachEvent("onpropertychange",Js)):e==="focusout"&&ks()}function wm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Jn(Ya)}function Vm(e,l){if(e==="click")return Jn(l)}function Qm(e,l){if(e==="input"||e==="change")return Jn(l)}function Zm(e,l){return e===l&&(e!==0||1/e===1/l)||e!==e&&l!==l}var Be=typeof Object.is=="function"?Object.is:Zm;function Xa(e,l){if(Be(e,l))return!0;if(typeof e!="object"||e===null||typeof l!="object"||l===null)return!1;var t=Object.keys(e),a=Object.keys(l);if(t.length!==a.length)return!1;for(a=0;a<t.length;a++){var n=t[a];if(!Ju.call(l,n)||!Be(e[n],l[n]))return!1}return!0}function Fs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ps(e,l){var t=Fs(e);e=0;for(var a;t;){if(t.nodeType===3){if(a=e+t.textContent.length,e<=l&&a>=l)return{node:t,offset:l-e};e=a}e:{for(;t;){if(t.nextSibling){t=t.nextSibling;break e}t=t.parentNode}t=void 0}t=Fs(t)}}function $s(e,l){return e&&l?e===l?!0:e&&e.nodeType===3?!1:l&&l.nodeType===3?$s(e,l.parentNode):"contains"in e?e.contains(l):e.compareDocumentPosition?!!(e.compareDocumentPosition(l)&16):!1:!1}function Ws(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var l=Xn(e.document);l instanceof e.HTMLIFrameElement;){try{var t=typeof l.contentWindow.location.href=="string"}catch{t=!1}if(t)e=l.contentWindow;else break;l=Xn(e.document)}return l}function gi(e){var l=e&&e.nodeName&&e.nodeName.toLowerCase();return l&&(l==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||l==="textarea"||e.contentEditable==="true")}function Km(e,l){var t=Ws(l);l=e.focusedElem;var a=e.selectionRange;if(t!==l&&l&&l.ownerDocument&&$s(l.ownerDocument.documentElement,l)){if(a!==null&&gi(l)){if(e=a.start,t=a.end,t===void 0&&(t=e),"selectionStart"in l)l.selectionStart=e,l.selectionEnd=Math.min(t,l.value.length);else if(t=(e=l.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var n=l.textContent.length,u=Math.min(a.start,n);a=a.end===void 0?u:Math.min(a.end,n),!t.extend&&u>a&&(n=a,a=u,u=n),n=Ps(l,u);var i=Ps(l,a);n&&i&&(t.rangeCount!==1||t.anchorNode!==n.node||t.anchorOffset!==n.offset||t.focusNode!==i.node||t.focusOffset!==i.offset)&&(e=e.createRange(),e.setStart(n.node,n.offset),t.removeAllRanges(),u>a?(t.addRange(e),t.extend(i.node,i.offset)):(e.setEnd(i.node,i.offset),t.addRange(e)))}}for(e=[],t=l;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof l.focus=="function"&&l.focus(),l=0;l<e.length;l++)t=e[l],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var km=bl&&"documentMode"in document&&11>=document.documentMode,It=null,pi=null,wa=null,yi=!1;function Is(e,l,t){var a=t.window===t?t.document:t.nodeType===9?t:t.ownerDocument;yi||It==null||It!==Xn(a)||(a=It,"selectionStart"in a&&gi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),wa&&Xa(wa,a)||(wa=a,a=Ou(pi,"onSelect"),0<a.length&&(l=new Kn("onSelect","select",null,l,t),e.push({event:l,listeners:a}),l.target=It)))}function Tt(e,l){var t={};return t[e.toLowerCase()]=l.toLowerCase(),t["Webkit"+e]="webkit"+l,t["Moz"+e]="moz"+l,t}var ea={animationend:Tt("Animation","AnimationEnd"),animationiteration:Tt("Animation","AnimationIteration"),animationstart:Tt("Animation","AnimationStart"),transitionrun:Tt("Transition","TransitionRun"),transitionstart:Tt("Transition","TransitionStart"),transitioncancel:Tt("Transition","TransitionCancel"),transitionend:Tt("Transition","TransitionEnd")},vi={},eo={};bl&&(eo=document.createElement("div").style,"AnimationEvent"in window||(delete ea.animationend.animation,delete ea.animationiteration.animation,delete ea.animationstart.animation),"TransitionEvent"in window||delete ea.transitionend.transition);function xt(e){if(vi[e])return vi[e];if(!ea[e])return e;var l=ea[e],t;for(t in l)if(l.hasOwnProperty(t)&&t in eo)return vi[e]=l[t];return e}var lo=xt("animationend"),to=xt("animationiteration"),ao=xt("animationstart"),Jm=xt("transitionrun"),Fm=xt("transitionstart"),Pm=xt("transitioncancel"),no=xt("transitionend"),uo=new Map,io="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function nl(e,l){uo.set(e,l),bt(l,[e])}var ke=[],la=0,bi=0;function Fn(){for(var e=la,l=bi=la=0;l<e;){var t=ke[l];ke[l++]=null;var a=ke[l];ke[l++]=null;var n=ke[l];ke[l++]=null;var u=ke[l];if(ke[l++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&co(t,n,u)}}function Pn(e,l,t,a){ke[la++]=e,ke[la++]=l,ke[la++]=t,ke[la++]=a,bi|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Si(e,l,t,a){return Pn(e,l,t,a),$n(e)}function Vl(e,l){return Pn(e,null,null,l),$n(e)}function co(e,l,t){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t);for(var n=!1,u=e.return;u!==null;)u.childLanes|=t,a=u.alternate,a!==null&&(a.childLanes|=t),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;n&&l!==null&&e.tag===3&&(u=e.stateNode,n=31-Le(t),u=u.hiddenUpdates,e=u[n],e===null?u[n]=[l]:e.push(l),l.lane=t|536870912)}function $n(e){if(50<hn)throw hn=0,Ec=null,Error(d(185));for(var l=e.return;l!==null;)e=l,l=e.return;return e.tag===3?e.stateNode:null}var ta={},so=new WeakMap;function Je(e,l){if(typeof e=="object"&&e!==null){var t=so.get(e);return t!==void 0?t:(l={value:e,source:l,stack:L(l)},so.set(e,l),l)}return{value:e,source:l,stack:L(l)}}var aa=[],na=0,Wn=null,In=0,Fe=[],Pe=0,At=null,Tl=1,xl="";function Dt(e,l){aa[na++]=In,aa[na++]=Wn,Wn=e,In=l}function oo(e,l,t){Fe[Pe++]=Tl,Fe[Pe++]=xl,Fe[Pe++]=At,At=e;var a=Tl;e=xl;var n=32-Le(a)-1;a&=~(1<<n),t+=1;var u=32-Le(l)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Tl=1<<32-Le(l)+n|t<<n|a,xl=u+e}else Tl=1<<u|t<<n|a,xl=e}function Ti(e){e.return!==null&&(Dt(e,1),oo(e,1,0))}function xi(e){for(;e===Wn;)Wn=aa[--na],aa[na]=null,In=aa[--na],aa[na]=null;for(;e===At;)At=Fe[--Pe],Fe[Pe]=null,xl=Fe[--Pe],Fe[Pe]=null,Tl=Fe[--Pe],Fe[Pe]=null}var Ce=null,De=null,Z=!1,ul=null,fl=!1,Ai=Error(d(519));function Nt(e){var l=Error(d(418,""));throw Za(Je(l,e)),Ai}function ro(e){var l=e.stateNode,t=e.type,a=e.memoizedProps;switch(l[Me]=e,l[Re]=a,t){case"dialog":X("cancel",l),X("close",l);break;case"iframe":case"object":case"embed":X("load",l);break;case"video":case"audio":for(t=0;t<pn.length;t++)X(pn[t],l);break;case"source":X("error",l);break;case"img":case"image":case"link":X("error",l),X("load",l);break;case"details":X("toggle",l);break;case"input":X("invalid",l),Ns(l,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Yn(l);break;case"select":X("invalid",l);break;case"textarea":X("invalid",l),Ms(l,a.value,a.defaultValue,a.children),Yn(l)}t=a.children,typeof t!="string"&&typeof t!="number"&&typeof t!="bigint"||l.textContent===""+t||a.suppressHydrationWarning===!0||Tf(l.textContent,t)?(a.popover!=null&&(X("beforetoggle",l),X("toggle",l)),a.onScroll!=null&&X("scroll",l),a.onScrollEnd!=null&&X("scrollend",l),a.onClick!=null&&(l.onclick=ju),l=!0):l=!1,l||Nt(e)}function fo(e){for(Ce=e.return;Ce;)switch(Ce.tag){case 3:case 27:fl=!0;return;case 5:case 13:fl=!1;return;default:Ce=Ce.return}}function Va(e){if(e!==Ce)return!1;if(!Z)return fo(e),Z=!0,!1;var l=!1,t;if((t=e.tag!==3&&e.tag!==27)&&((t=e.tag===5)&&(t=e.type,t=!(t!=="form"&&t!=="button")||Vc(e.type,e.memoizedProps)),t=!t),t&&(l=!0),l&&De&&Nt(e),fo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(d(317));e:{for(e=e.nextSibling,l=0;e;){if(e.nodeType===8)if(t=e.data,t==="/$"){if(l===0){De=cl(e.nextSibling);break e}l--}else t!=="$"&&t!=="$!"&&t!=="$?"||l++;e=e.nextSibling}De=null}}else De=Ce?cl(e.stateNode.nextSibling):null;return!0}function Qa(){De=Ce=null,Z=!1}function Za(e){ul===null?ul=[e]:ul.push(e)}var Ka=Error(d(460)),mo=Error(d(474)),Di={then:function(){}};function ho(e){return e=e.status,e==="fulfilled"||e==="rejected"}function eu(){}function go(e,l,t){switch(t=e[t],t===void 0?e.push(l):t!==l&&(l.then(eu,eu),l=t),l.status){case"fulfilled":return l.value;case"rejected":throw e=l.reason,e===Ka?Error(d(483)):e;default:if(typeof l.status=="string")l.then(eu,eu);else{if(e=I,e!==null&&100<e.shellSuspendCounter)throw Error(d(482));e=l,e.status="pending",e.then(function(a){if(l.status==="pending"){var n=l;n.status="fulfilled",n.value=a}},function(a){if(l.status==="pending"){var n=l;n.status="rejected",n.reason=a}})}switch(l.status){case"fulfilled":return l.value;case"rejected":throw e=l.reason,e===Ka?Error(d(483)):e}throw ka=l,Ka}}var ka=null;function po(){if(ka===null)throw Error(d(459));var e=ka;return ka=null,e}var ua=null,Ja=0;function lu(e){var l=Ja;return Ja+=1,ua===null&&(ua=[]),go(ua,e,l)}function Fa(e,l){l=l.props.ref,e.ref=l!==void 0?l:null}function tu(e,l){throw l.$$typeof===j?Error(d(525)):(e=Object.prototype.toString.call(l),Error(d(31,e==="[object Object]"?"object with keys {"+Object.keys(l).join(", ")+"}":e)))}function yo(e){var l=e._init;return l(e._payload)}function vo(e){function l(m,r){if(e){var g=m.deletions;g===null?(m.deletions=[r],m.flags|=16):g.push(r)}}function t(m,r){if(!e)return null;for(;r!==null;)l(m,r),r=r.sibling;return null}function a(m){for(var r=new Map;m!==null;)m.key!==null?r.set(m.key,m):r.set(m.index,m),m=m.sibling;return r}function n(m,r){return m=lt(m,r),m.index=0,m.sibling=null,m}function u(m,r,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<r?(m.flags|=33554434,r):g):(m.flags|=33554434,r)):(m.flags|=1048576,r)}function i(m){return e&&m.alternate===null&&(m.flags|=33554434),m}function c(m,r,g,S){return r===null||r.tag!==6?(r=vc(g,m.mode,S),r.return=m,r):(r=n(r,g),r.return=m,r)}function s(m,r,g,S){var D=g.type;return D===Y?b(m,r,g.props.children,S,g.key):r!==null&&(r.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===je&&yo(D)===r.type)?(r=n(r,g.props),Fa(r,g),r.return=m,r):(r=Su(g.type,g.key,g.props,null,m.mode,S),Fa(r,g),r.return=m,r)}function f(m,r,g,S){return r===null||r.tag!==4||r.stateNode.containerInfo!==g.containerInfo||r.stateNode.implementation!==g.implementation?(r=bc(g,m.mode,S),r.return=m,r):(r=n(r,g.children||[]),r.return=m,r)}function b(m,r,g,S,D){return r===null||r.tag!==7?(r=Ht(g,m.mode,S,D),r.return=m,r):(r=n(r,g),r.return=m,r)}function T(m,r,g){if(typeof r=="string"&&r!==""||typeof r=="number"||typeof r=="bigint")return r=vc(""+r,m.mode,g),r.return=m,r;if(typeof r=="object"&&r!==null){switch(r.$$typeof){case H:return g=Su(r.type,r.key,r.props,null,m.mode,g),Fa(g,r),g.return=m,g;case P:return r=bc(r,m.mode,g),r.return=m,r;case je:var S=r._init;return r=S(r._payload),T(m,r,g)}if(vl(r)||al(r))return r=Ht(r,m.mode,g,null),r.return=m,r;if(typeof r.then=="function")return T(m,lu(r),g);if(r.$$typeof===pe)return T(m,yu(m,r),g);tu(m,r)}return null}function p(m,r,g,S){var D=r!==null?r.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return D!==null?null:c(m,r,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case H:return g.key===D?s(m,r,g,S):null;case P:return g.key===D?f(m,r,g,S):null;case je:return D=g._init,g=D(g._payload),p(m,r,g,S)}if(vl(g)||al(g))return D!==null?null:b(m,r,g,S,null);if(typeof g.then=="function")return p(m,r,lu(g),S);if(g.$$typeof===pe)return p(m,r,yu(m,g),S);tu(m,g)}return null}function v(m,r,g,S,D){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return m=m.get(g)||null,c(r,m,""+S,D);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case H:return m=m.get(S.key===null?g:S.key)||null,s(r,m,S,D);case P:return m=m.get(S.key===null?g:S.key)||null,f(r,m,S,D);case je:var q=S._init;return S=q(S._payload),v(m,r,g,S,D)}if(vl(S)||al(S))return m=m.get(g)||null,b(r,m,S,D,null);if(typeof S.then=="function")return v(m,r,g,lu(S),D);if(S.$$typeof===pe)return v(m,r,g,yu(r,S),D);tu(r,S)}return null}function M(m,r,g,S){for(var D=null,q=null,z=r,O=r=0,Ae=null;z!==null&&O<g.length;O++){z.index>O?(Ae=z,z=null):Ae=z.sibling;var K=p(m,z,g[O],S);if(K===null){z===null&&(z=Ae);break}e&&z&&K.alternate===null&&l(m,z),r=u(K,r,O),q===null?D=K:q.sibling=K,q=K,z=Ae}if(O===g.length)return t(m,z),Z&&Dt(m,O),D;if(z===null){for(;O<g.length;O++)z=T(m,g[O],S),z!==null&&(r=u(z,r,O),q===null?D=z:q.sibling=z,q=z);return Z&&Dt(m,O),D}for(z=a(z);O<g.length;O++)Ae=v(z,m,O,g[O],S),Ae!==null&&(e&&Ae.alternate!==null&&z.delete(Ae.key===null?O:Ae.key),r=u(Ae,r,O),q===null?D=Ae:q.sibling=Ae,q=Ae);return e&&z.forEach(function(st){return l(m,st)}),Z&&Dt(m,O),D}function U(m,r,g,S){if(g==null)throw Error(d(151));for(var D=null,q=null,z=r,O=r=0,Ae=null,K=g.next();z!==null&&!K.done;O++,K=g.next()){z.index>O?(Ae=z,z=null):Ae=z.sibling;var st=p(m,z,K.value,S);if(st===null){z===null&&(z=Ae);break}e&&z&&st.alternate===null&&l(m,z),r=u(st,r,O),q===null?D=st:q.sibling=st,q=st,z=Ae}if(K.done)return t(m,z),Z&&Dt(m,O),D;if(z===null){for(;!K.done;O++,K=g.next())K=T(m,K.value,S),K!==null&&(r=u(K,r,O),q===null?D=K:q.sibling=K,q=K);return Z&&Dt(m,O),D}for(z=a(z);!K.done;O++,K=g.next())K=v(z,m,O,K.value,S),K!==null&&(e&&K.alternate!==null&&z.delete(K.key===null?O:K.key),r=u(K,r,O),q===null?D=K:q.sibling=K,q=K);return e&&z.forEach(function(rg){return l(m,rg)}),Z&&Dt(m,O),D}function se(m,r,g,S){if(typeof g=="object"&&g!==null&&g.type===Y&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case H:e:{for(var D=g.key;r!==null;){if(r.key===D){if(D=g.type,D===Y){if(r.tag===7){t(m,r.sibling),S=n(r,g.props.children),S.return=m,m=S;break e}}else if(r.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===je&&yo(D)===r.type){t(m,r.sibling),S=n(r,g.props),Fa(S,g),S.return=m,m=S;break e}t(m,r);break}else l(m,r);r=r.sibling}g.type===Y?(S=Ht(g.props.children,m.mode,S,g.key),S.return=m,m=S):(S=Su(g.type,g.key,g.props,null,m.mode,S),Fa(S,g),S.return=m,m=S)}return i(m);case P:e:{for(D=g.key;r!==null;){if(r.key===D)if(r.tag===4&&r.stateNode.containerInfo===g.containerInfo&&r.stateNode.implementation===g.implementation){t(m,r.sibling),S=n(r,g.children||[]),S.return=m,m=S;break e}else{t(m,r);break}else l(m,r);r=r.sibling}S=bc(g,m.mode,S),S.return=m,m=S}return i(m);case je:return D=g._init,g=D(g._payload),se(m,r,g,S)}if(vl(g))return M(m,r,g,S);if(al(g)){if(D=al(g),typeof D!="function")throw Error(d(150));return g=D.call(g),U(m,r,g,S)}if(typeof g.then=="function")return se(m,r,lu(g),S);if(g.$$typeof===pe)return se(m,r,yu(m,g),S);tu(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,r!==null&&r.tag===6?(t(m,r.sibling),S=n(r,g),S.return=m,m=S):(t(m,r),S=vc(g,m.mode,S),S.return=m,m=S),i(m)):t(m,r)}return function(m,r,g,S){try{Ja=0;var D=se(m,r,g,S);return ua=null,D}catch(z){if(z===Ka)throw z;var q=el(29,z,null,m.mode);return q.lanes=S,q.return=m,q}finally{}}}var Et=vo(!0),bo=vo(!1),ia=sl(null),au=sl(0);function So(e,l){e=_l,ae(au,e),ae(ia,l),_l=e|l.baseLanes}function Ni(){ae(au,_l),ae(ia,ia.current)}function Ei(){_l=au.current,be(ia),be(au)}var $e=sl(null),dl=null;function Ql(e){var l=e.alternate;ae(ye,ye.current&1),ae($e,e),dl===null&&(l===null||ia.current!==null||l.memoizedState!==null)&&(dl=e)}function To(e){if(e.tag===22){if(ae(ye,ye.current),ae($e,e),dl===null){var l=e.alternate;l!==null&&l.memoizedState!==null&&(dl=e)}}else Zl()}function Zl(){ae(ye,ye.current),ae($e,$e.current)}function Al(e){be($e),dl===e&&(dl=null),be(ye)}var ye=sl(0);function nu(e){for(var l=e;l!==null;){if(l.tag===13){var t=l.memoizedState;if(t!==null&&(t=t.dehydrated,t===null||t.data==="$?"||t.data==="$!"))return l}else if(l.tag===19&&l.memoizedProps.revealOrder!==void 0){if(l.flags&128)return l}else if(l.child!==null){l.child.return=l,l=l.child;continue}if(l===e)break;for(;l.sibling===null;){if(l.return===null||l.return===e)return null;l=l.return}l.sibling.return=l.return,l=l.sibling}return null}var $m=typeof AbortController<"u"?AbortController:function(){var e=[],l=this.signal={aborted:!1,addEventListener:function(t,a){e.push(a)}};this.abort=function(){l.aborted=!0,e.forEach(function(t){return t()})}},Wm=h.unstable_scheduleCallback,Im=h.unstable_NormalPriority,ve={$$typeof:pe,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mi(){return{controller:new $m,data:new Map,refCount:0}}function Pa(e){e.refCount--,e.refCount===0&&Wm(Im,function(){e.controller.abort()})}var $a=null,zi=0,ca=0,sa=null;function eh(e,l){if($a===null){var t=$a=[];zi=0,ca=Uc(),sa={status:"pending",value:void 0,then:function(a){t.push(a)}}}return zi++,l.then(xo,xo),l}function xo(){if(--zi===0&&$a!==null){sa!==null&&(sa.status="fulfilled");var e=$a;$a=null,ca=0,sa=null;for(var l=0;l<e.length;l++)(0,e[l])()}}function lh(e,l){var t=[],a={status:"pending",value:null,reason:null,then:function(n){t.push(n)}};return e.then(function(){a.status="fulfilled",a.value=l;for(var n=0;n<t.length;n++)(0,t[n])(l)},function(n){for(a.status="rejected",a.reason=n,n=0;n<t.length;n++)(0,t[n])(void 0)}),a}var Ao=R.S;R.S=function(e,l){typeof l=="object"&&l!==null&&typeof l.then=="function"&&eh(e,l),Ao!==null&&Ao(e,l)};var Mt=sl(null);function Ci(){var e=Mt.current;return e!==null?e:I.pooledCache}function uu(e,l){l===null?ae(Mt,Mt.current):ae(Mt,l.pool)}function Do(){var e=Ci();return e===null?null:{parent:ve._currentValue,pool:e}}var Kl=0,B=null,J=null,fe=null,iu=!1,oa=!1,zt=!1,cu=0,Wa=0,ra=null,th=0;function re(){throw Error(d(321))}function Oi(e,l){if(l===null)return!1;for(var t=0;t<l.length&&t<e.length;t++)if(!Be(e[t],l[t]))return!1;return!0}function ji(e,l,t,a,n,u){return Kl=u,B=l,l.memoizedState=null,l.updateQueue=null,l.lanes=0,R.H=e===null||e.memoizedState===null?Ct:kl,zt=!1,u=t(a,n),zt=!1,oa&&(u=Eo(l,t,a,n)),No(e),u}function No(e){R.H=ml;var l=J!==null&&J.next!==null;if(Kl=0,fe=J=B=null,iu=!1,Wa=0,ra=null,l)throw Error(d(300));e===null||Te||(e=e.dependencies,e!==null&&pu(e)&&(Te=!0))}function Eo(e,l,t,a){B=e;var n=0;do{if(oa&&(ra=null),Wa=0,oa=!1,25<=n)throw Error(d(301));if(n+=1,fe=J=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}R.H=Ot,u=l(t,a)}while(oa);return u}function ah(){var e=R.H,l=e.useState()[0];return l=typeof l.then=="function"?Ia(l):l,e=e.useState()[0],(J!==null?J.memoizedState:null)!==e&&(B.flags|=1024),l}function Ri(){var e=cu!==0;return cu=0,e}function _i(e,l,t){l.updateQueue=e.updateQueue,l.flags&=-2053,e.lanes&=~t}function Ui(e){if(iu){for(e=e.memoizedState;e!==null;){var l=e.queue;l!==null&&(l.pending=null),e=e.next}iu=!1}Kl=0,fe=J=B=null,oa=!1,Wa=cu=0,ra=null}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?B.memoizedState=fe=e:fe=fe.next=e,fe}function de(){if(J===null){var e=B.alternate;e=e!==null?e.memoizedState:null}else e=J.next;var l=fe===null?B.memoizedState:fe.next;if(l!==null)fe=l,J=e;else{if(e===null)throw B.alternate===null?Error(d(467)):Error(d(310));J=e,e={memoizedState:J.memoizedState,baseState:J.baseState,baseQueue:J.baseQueue,queue:J.queue,next:null},fe===null?B.memoizedState=fe=e:fe=fe.next=e}return fe}var su;su=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Ia(e){var l=Wa;return Wa+=1,ra===null&&(ra=[]),e=go(ra,e,l),l=B,(fe===null?l.memoizedState:fe.next)===null&&(l=l.alternate,R.H=l===null||l.memoizedState===null?Ct:kl),e}function ou(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ia(e);if(e.$$typeof===pe)return ze(e)}throw Error(d(438,String(e)))}function Hi(e){var l=null,t=B.updateQueue;if(t!==null&&(l=t.memoCache),l==null){var a=B.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(l={data:a.data.map(function(n){return n.slice()}),index:0})))}if(l==null&&(l={data:[],index:0}),t===null&&(t=su(),B.updateQueue=t),t.memoCache=l,t=l.data[l.index],t===void 0)for(t=l.data[l.index]=Array(e),a=0;a<e;a++)t[a]=Ea;return l.index++,t}function Dl(e,l){return typeof l=="function"?l(e):l}function ru(e){var l=de();return Li(l,J,e)}function Li(e,l,t){var a=e.queue;if(a===null)throw Error(d(311));a.lastRenderedReducer=t;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}l.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{l=n.next;var c=i=null,s=null,f=l,b=!1;do{var T=f.lane&-536870913;if(T!==f.lane?(V&T)===T:(Kl&T)===T){var p=f.revertLane;if(p===0)s!==null&&(s=s.next={lane:0,revertLane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),T===ca&&(b=!0);else if((Kl&p)===p){f=f.next,p===ca&&(b=!0);continue}else T={lane:0,revertLane:f.revertLane,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null},s===null?(c=s=T,i=u):s=s.next=T,B.lanes|=p,tt|=p;T=f.action,zt&&t(u,T),u=f.hasEagerState?f.eagerState:t(u,T)}else p={lane:T,revertLane:f.revertLane,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null},s===null?(c=s=p,i=u):s=s.next=p,B.lanes|=T,tt|=T;f=f.next}while(f!==null&&f!==l);if(s===null?i=u:s.next=c,!Be(u,e.memoizedState)&&(Te=!0,b&&(t=sa,t!==null)))throw t;e.memoizedState=u,e.baseState=i,e.baseQueue=s,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Bi(e){var l=de(),t=l.queue;if(t===null)throw Error(d(311));t.lastRenderedReducer=e;var a=t.dispatch,n=t.pending,u=l.memoizedState;if(n!==null){t.pending=null;var i=n=n.next;do u=e(u,i.action),i=i.next;while(i!==n);Be(u,l.memoizedState)||(Te=!0),l.memoizedState=u,l.baseQueue===null&&(l.baseState=u),t.lastRenderedState=u}return[u,a]}function Mo(e,l,t){var a=B,n=de(),u=Z;if(u){if(t===void 0)throw Error(d(407));t=t()}else t=l();var i=!Be((J||n).memoizedState,t);if(i&&(n.memoizedState=t,Te=!0),n=n.queue,Yi(Oo.bind(null,a,n,e),[e]),n.getSnapshot!==l||i||fe!==null&&fe.memoizedState.tag&1){if(a.flags|=2048,fa(9,Co.bind(null,a,n,t,l),{destroy:void 0},null),I===null)throw Error(d(349));u||Kl&60||zo(a,l,t)}return t}function zo(e,l,t){e.flags|=16384,e={getSnapshot:l,value:t},l=B.updateQueue,l===null?(l=su(),B.updateQueue=l,l.stores=[e]):(t=l.stores,t===null?l.stores=[e]:t.push(e))}function Co(e,l,t,a){l.value=t,l.getSnapshot=a,jo(l)&&Ro(e)}function Oo(e,l,t){return t(function(){jo(l)&&Ro(e)})}function jo(e){var l=e.getSnapshot;e=e.value;try{var t=l();return!Be(e,t)}catch{return!0}}function Ro(e){var l=Vl(e,2);l!==null&&Oe(l,e,2)}function qi(e){var l=Ue();if(typeof e=="function"){var t=e;if(e=t(),zt){Yl(!0);try{t()}finally{Yl(!1)}}}return l.memoizedState=l.baseState=e,l.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dl,lastRenderedState:e},l}function _o(e,l,t,a){return e.baseState=t,Li(e,J,typeof a=="function"?a:Dl)}function nh(e,l,t,a,n){if(mu(e))throw Error(d(485));if(e=l.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};R.T!==null?t(!0):u.isTransition=!1,a(u),t=l.pending,t===null?(u.next=l.pending=u,Uo(l,u)):(u.next=t.next,l.pending=t.next=u)}}function Uo(e,l){var t=l.action,a=l.payload,n=e.state;if(l.isTransition){var u=R.T,i={};R.T=i;try{var c=t(n,a),s=R.S;s!==null&&s(i,c),Ho(e,l,c)}catch(f){Gi(e,l,f)}finally{R.T=u}}else try{u=t(n,a),Ho(e,l,u)}catch(f){Gi(e,l,f)}}function Ho(e,l,t){t!==null&&typeof t=="object"&&typeof t.then=="function"?t.then(function(a){Lo(e,l,a)},function(a){return Gi(e,l,a)}):Lo(e,l,t)}function Lo(e,l,t){l.status="fulfilled",l.value=t,Bo(l),e.state=t,l=e.pending,l!==null&&(t=l.next,t===l?e.pending=null:(t=t.next,l.next=t,Uo(e,t)))}function Gi(e,l,t){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do l.status="rejected",l.reason=t,Bo(l),l=l.next;while(l!==a)}e.action=null}function Bo(e){e=e.listeners;for(var l=0;l<e.length;l++)(0,e[l])()}function qo(e,l){return l}function Go(e,l){if(Z){var t=I.formState;if(t!==null){e:{var a=B;if(Z){if(De){l:{for(var n=De,u=fl;n.nodeType!==8;){if(!u){n=null;break l}if(n=cl(n.nextSibling),n===null){n=null;break l}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){De=cl(n.nextSibling),a=n.data==="F!";break e}}Nt(a)}a=!1}a&&(l=t[0])}}return t=Ue(),t.memoizedState=t.baseState=l,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qo,lastRenderedState:l},t.queue=a,t=ar.bind(null,B,a),a.dispatch=t,a=qi(!1),u=Zi.bind(null,B,!1,a.queue),a=Ue(),n={state:l,dispatch:null,action:e,pending:null},a.queue=n,t=nh.bind(null,B,n,u,t),n.dispatch=t,a.memoizedState=e,[l,t,!1]}function Yo(e){var l=de();return Xo(l,J,e)}function Xo(e,l,t){l=Li(e,l,qo)[0],e=ru(Dl)[0],l=typeof l=="object"&&l!==null&&typeof l.then=="function"?Ia(l):l;var a=de(),n=a.queue,u=n.dispatch;return t!==a.memoizedState&&(B.flags|=2048,fa(9,uh.bind(null,n,t),{destroy:void 0},null)),[l,u,e]}function uh(e,l){e.action=l}function wo(e){var l=de(),t=J;if(t!==null)return Xo(l,t,e);de(),l=l.memoizedState,t=de();var a=t.queue.dispatch;return t.memoizedState=e,[l,a,!1]}function fa(e,l,t,a){return e={tag:e,create:l,inst:t,deps:a,next:null},l=B.updateQueue,l===null&&(l=su(),B.updateQueue=l),t=l.lastEffect,t===null?l.lastEffect=e.next=e:(a=t.next,t.next=e,e.next=a,l.lastEffect=e),e}function Vo(){return de().memoizedState}function fu(e,l,t,a){var n=Ue();B.flags|=e,n.memoizedState=fa(1|l,t,{destroy:void 0},a===void 0?null:a)}function du(e,l,t,a){var n=de();a=a===void 0?null:a;var u=n.memoizedState.inst;J!==null&&a!==null&&Oi(a,J.memoizedState.deps)?n.memoizedState=fa(l,t,u,a):(B.flags|=e,n.memoizedState=fa(1|l,t,u,a))}function Qo(e,l){fu(8390656,8,e,l)}function Yi(e,l){du(2048,8,e,l)}function Zo(e,l){return du(4,2,e,l)}function Ko(e,l){return du(4,4,e,l)}function ko(e,l){if(typeof l=="function"){e=e();var t=l(e);return function(){typeof t=="function"?t():l(null)}}if(l!=null)return e=e(),l.current=e,function(){l.current=null}}function Jo(e,l,t){t=t!=null?t.concat([e]):null,du(4,4,ko.bind(null,l,e),t)}function Xi(){}function Fo(e,l){var t=de();l=l===void 0?null:l;var a=t.memoizedState;return l!==null&&Oi(l,a[1])?a[0]:(t.memoizedState=[e,l],e)}function Po(e,l){var t=de();l=l===void 0?null:l;var a=t.memoizedState;if(l!==null&&Oi(l,a[1]))return a[0];if(a=e(),zt){Yl(!0);try{e()}finally{Yl(!1)}}return t.memoizedState=[a,l],a}function wi(e,l,t){return t===void 0||Kl&1073741824?e.memoizedState=l:(e.memoizedState=t,e=Wr(),B.lanes|=e,tt|=e,t)}function $o(e,l,t,a){return Be(t,l)?t:ia.current!==null?(e=wi(e,t,a),Be(e,l)||(Te=!0),e):Kl&42?(e=Wr(),B.lanes|=e,tt|=e,l):(Te=!0,e.memoizedState=t)}function Wo(e,l,t,a,n){var u=Q.p;Q.p=u!==0&&8>u?u:8;var i=R.T,c={};R.T=c,Zi(e,!1,l,t);try{var s=n(),f=R.S;if(f!==null&&f(c,s),s!==null&&typeof s=="object"&&typeof s.then=="function"){var b=lh(s,a);en(e,l,b,Xe(e))}else en(e,l,a,Xe(e))}catch(T){en(e,l,{then:function(){},status:"rejected",reason:T},Xe())}finally{Q.p=u,R.T=i}}function ih(){}function Vi(e,l,t,a){if(e.tag!==5)throw Error(d(476));var n=Io(e).queue;Wo(e,n,l,Qe,t===null?ih:function(){return er(e),t(a)})}function Io(e){var l=e.memoizedState;if(l!==null)return l;l={memoizedState:Qe,baseState:Qe,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dl,lastRenderedState:Qe},next:null};var t={};return l.next={memoizedState:t,baseState:t,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dl,lastRenderedState:t},next:null},e.memoizedState=l,e=e.alternate,e!==null&&(e.memoizedState=l),l}function er(e){var l=Io(e).next.queue;en(e,l,{},Xe())}function Qi(){return ze(Tn)}function lr(){return de().memoizedState}function tr(){return de().memoizedState}function ch(e){for(var l=e.return;l!==null;){switch(l.tag){case 24:case 3:var t=Xe();e=Pl(t);var a=$l(l,e,t);a!==null&&(Oe(a,l,t),an(a,l,t)),l={cache:Mi()},e.payload=l;return}l=l.return}}function sh(e,l,t){var a=Xe();t={lane:a,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},mu(e)?nr(l,t):(t=Si(e,l,t,a),t!==null&&(Oe(t,e,a),ur(t,l,a)))}function ar(e,l,t){var a=Xe();en(e,l,t,a)}function en(e,l,t,a){var n={lane:a,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(mu(e))nr(l,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=l.lastRenderedReducer,u!==null))try{var i=l.lastRenderedState,c=u(i,t);if(n.hasEagerState=!0,n.eagerState=c,Be(c,i))return Pn(e,l,n,0),I===null&&Fn(),!1}catch{}finally{}if(t=Si(e,l,n,a),t!==null)return Oe(t,e,a),ur(t,l,a),!0}return!1}function Zi(e,l,t,a){if(a={lane:2,revertLane:Uc(),action:a,hasEagerState:!1,eagerState:null,next:null},mu(e)){if(l)throw Error(d(479))}else l=Si(e,t,a,2),l!==null&&Oe(l,e,2)}function mu(e){var l=e.alternate;return e===B||l!==null&&l===B}function nr(e,l){oa=iu=!0;var t=e.pending;t===null?l.next=l:(l.next=t.next,t.next=l),e.pending=l}function ur(e,l,t){if(t&4194176){var a=l.lanes;a&=e.pendingLanes,t|=a,l.lanes=t,gs(e,t)}}var ml={readContext:ze,use:ou,useCallback:re,useContext:re,useEffect:re,useImperativeHandle:re,useLayoutEffect:re,useInsertionEffect:re,useMemo:re,useReducer:re,useRef:re,useState:re,useDebugValue:re,useDeferredValue:re,useTransition:re,useSyncExternalStore:re,useId:re};ml.useCacheRefresh=re,ml.useMemoCache=re,ml.useHostTransitionStatus=re,ml.useFormState=re,ml.useActionState=re,ml.useOptimistic=re;var Ct={readContext:ze,use:ou,useCallback:function(e,l){return Ue().memoizedState=[e,l===void 0?null:l],e},useContext:ze,useEffect:Qo,useImperativeHandle:function(e,l,t){t=t!=null?t.concat([e]):null,fu(4194308,4,ko.bind(null,l,e),t)},useLayoutEffect:function(e,l){return fu(4194308,4,e,l)},useInsertionEffect:function(e,l){fu(4,2,e,l)},useMemo:function(e,l){var t=Ue();l=l===void 0?null:l;var a=e();if(zt){Yl(!0);try{e()}finally{Yl(!1)}}return t.memoizedState=[a,l],a},useReducer:function(e,l,t){var a=Ue();if(t!==void 0){var n=t(l);if(zt){Yl(!0);try{t(l)}finally{Yl(!1)}}}else n=l;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=sh.bind(null,B,e),[a.memoizedState,e]},useRef:function(e){var l=Ue();return e={current:e},l.memoizedState=e},useState:function(e){e=qi(e);var l=e.queue,t=ar.bind(null,B,l);return l.dispatch=t,[e.memoizedState,t]},useDebugValue:Xi,useDeferredValue:function(e,l){var t=Ue();return wi(t,e,l)},useTransition:function(){var e=qi(!1);return e=Wo.bind(null,B,e.queue,!0,!1),Ue().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,l,t){var a=B,n=Ue();if(Z){if(t===void 0)throw Error(d(407));t=t()}else{if(t=l(),I===null)throw Error(d(349));V&60||zo(a,l,t)}n.memoizedState=t;var u={value:t,getSnapshot:l};return n.queue=u,Qo(Oo.bind(null,a,u,e),[e]),a.flags|=2048,fa(9,Co.bind(null,a,u,t,l),{destroy:void 0},null),t},useId:function(){var e=Ue(),l=I.identifierPrefix;if(Z){var t=xl,a=Tl;t=(a&~(1<<32-Le(a)-1)).toString(32)+t,l=":"+l+"R"+t,t=cu++,0<t&&(l+="H"+t.toString(32)),l+=":"}else t=th++,l=":"+l+"r"+t.toString(32)+":";return e.memoizedState=l},useCacheRefresh:function(){return Ue().memoizedState=ch.bind(null,B)}};Ct.useMemoCache=Hi,Ct.useHostTransitionStatus=Qi,Ct.useFormState=Go,Ct.useActionState=Go,Ct.useOptimistic=function(e){var l=Ue();l.memoizedState=l.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return l.queue=t,l=Zi.bind(null,B,!0,t),t.dispatch=l,[e,l]};var kl={readContext:ze,use:ou,useCallback:Fo,useContext:ze,useEffect:Yi,useImperativeHandle:Jo,useInsertionEffect:Zo,useLayoutEffect:Ko,useMemo:Po,useReducer:ru,useRef:Vo,useState:function(){return ru(Dl)},useDebugValue:Xi,useDeferredValue:function(e,l){var t=de();return $o(t,J.memoizedState,e,l)},useTransition:function(){var e=ru(Dl)[0],l=de().memoizedState;return[typeof e=="boolean"?e:Ia(e),l]},useSyncExternalStore:Mo,useId:lr};kl.useCacheRefresh=tr,kl.useMemoCache=Hi,kl.useHostTransitionStatus=Qi,kl.useFormState=Yo,kl.useActionState=Yo,kl.useOptimistic=function(e,l){var t=de();return _o(t,J,e,l)};var Ot={readContext:ze,use:ou,useCallback:Fo,useContext:ze,useEffect:Yi,useImperativeHandle:Jo,useInsertionEffect:Zo,useLayoutEffect:Ko,useMemo:Po,useReducer:Bi,useRef:Vo,useState:function(){return Bi(Dl)},useDebugValue:Xi,useDeferredValue:function(e,l){var t=de();return J===null?wi(t,e,l):$o(t,J.memoizedState,e,l)},useTransition:function(){var e=Bi(Dl)[0],l=de().memoizedState;return[typeof e=="boolean"?e:Ia(e),l]},useSyncExternalStore:Mo,useId:lr};Ot.useCacheRefresh=tr,Ot.useMemoCache=Hi,Ot.useHostTransitionStatus=Qi,Ot.useFormState=wo,Ot.useActionState=wo,Ot.useOptimistic=function(e,l){var t=de();return J!==null?_o(t,J,e,l):(t.baseState=e,[e,t.queue.dispatch])};function Ki(e,l,t,a){l=e.memoizedState,t=t(a,l),t=t==null?l:k({},l,t),e.memoizedState=t,e.lanes===0&&(e.updateQueue.baseState=t)}var ki={isMounted:function(e){return(e=e._reactInternals)?_(e)===e:!1},enqueueSetState:function(e,l,t){e=e._reactInternals;var a=Xe(),n=Pl(a);n.payload=l,t!=null&&(n.callback=t),l=$l(e,n,a),l!==null&&(Oe(l,e,a),an(l,e,a))},enqueueReplaceState:function(e,l,t){e=e._reactInternals;var a=Xe(),n=Pl(a);n.tag=1,n.payload=l,t!=null&&(n.callback=t),l=$l(e,n,a),l!==null&&(Oe(l,e,a),an(l,e,a))},enqueueForceUpdate:function(e,l){e=e._reactInternals;var t=Xe(),a=Pl(t);a.tag=2,l!=null&&(a.callback=l),l=$l(e,a,t),l!==null&&(Oe(l,e,t),an(l,e,t))}};function ir(e,l,t,a,n,u,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,i):l.prototype&&l.prototype.isPureReactComponent?!Xa(t,a)||!Xa(n,u):!0}function cr(e,l,t,a){e=l.state,typeof l.componentWillReceiveProps=="function"&&l.componentWillReceiveProps(t,a),typeof l.UNSAFE_componentWillReceiveProps=="function"&&l.UNSAFE_componentWillReceiveProps(t,a),l.state!==e&&ki.enqueueReplaceState(l,l.state,null)}function jt(e,l){var t=l;if("ref"in l){t={};for(var a in l)a!=="ref"&&(t[a]=l[a])}if(e=e.defaultProps){t===l&&(t=k({},t));for(var n in e)t[n]===void 0&&(t[n]=e[n])}return t}var hu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var l=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(l))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function sr(e){hu(e)}function or(e){console.error(e)}function rr(e){hu(e)}function gu(e,l){try{var t=e.onUncaughtError;t(l.value,{componentStack:l.stack})}catch(a){setTimeout(function(){throw a})}}function fr(e,l,t){try{var a=e.onCaughtError;a(t.value,{componentStack:t.stack,errorBoundary:l.tag===1?l.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Ji(e,l,t){return t=Pl(t),t.tag=3,t.payload={element:null},t.callback=function(){gu(e,l)},t}function dr(e){return e=Pl(e),e.tag=3,e}function mr(e,l,t,a){var n=t.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){fr(l,t,a)}}var i=t.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(e.callback=function(){fr(l,t,a),typeof n!="function"&&(at===null?at=new Set([this]):at.add(this));var c=a.stack;this.componentDidCatch(a.value,{componentStack:c!==null?c:""})})}function oh(e,l,t,a,n){if(t.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(l=t.alternate,l!==null&&tn(l,t,n,!0),t=$e.current,t!==null){switch(t.tag){case 13:return dl===null?Cc():t.alternate===null&&ce===0&&(ce=3),t.flags&=-257,t.flags|=65536,t.lanes=n,a===Di?t.flags|=16384:(l=t.updateQueue,l===null?t.updateQueue=new Set([a]):l.add(a),jc(e,a,n)),!1;case 22:return t.flags|=65536,a===Di?t.flags|=16384:(l=t.updateQueue,l===null?(l={transitions:null,markerInstances:null,retryQueue:new Set([a])},t.updateQueue=l):(t=l.retryQueue,t===null?l.retryQueue=new Set([a]):t.add(a)),jc(e,a,n)),!1}throw Error(d(435,t.tag))}return jc(e,a,n),Cc(),!1}if(Z)return l=$e.current,l!==null?(!(l.flags&65536)&&(l.flags|=256),l.flags|=65536,l.lanes=n,a!==Ai&&(e=Error(d(422),{cause:a}),Za(Je(e,t)))):(a!==Ai&&(l=Error(d(423),{cause:a}),Za(Je(l,t))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=Je(a,t),n=Ji(e.stateNode,a,n),oc(e,n),ce!==4&&(ce=2)),!1;var u=Error(d(520),{cause:a});if(u=Je(u,t),dn===null?dn=[u]:dn.push(u),ce!==4&&(ce=2),l===null)return!0;a=Je(a,t),t=l;do{switch(t.tag){case 3:return t.flags|=65536,e=n&-n,t.lanes|=e,e=Ji(t.stateNode,a,e),oc(t,e),!1;case 1:if(l=t.type,u=t.stateNode,(t.flags&128)===0&&(typeof l.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(at===null||!at.has(u))))return t.flags|=65536,n&=-n,t.lanes|=n,n=dr(n),mr(n,e,t,a),oc(t,n),!1}t=t.return}while(t!==null);return!1}var hr=Error(d(461)),Te=!1;function Ne(e,l,t,a){l.child=e===null?bo(l,null,t,a):Et(l,e.child,t,a)}function gr(e,l,t,a,n){t=t.render;var u=l.ref;if("ref"in a){var i={};for(var c in a)c!=="ref"&&(i[c]=a[c])}else i=a;return _t(l),a=ji(e,l,t,i,u,n),c=Ri(),e!==null&&!Te?(_i(e,l,n),Nl(e,l,n)):(Z&&c&&Ti(l),l.flags|=1,Ne(e,l,a,n),l.child)}function pr(e,l,t,a,n){if(e===null){var u=t.type;return typeof u=="function"&&!yc(u)&&u.defaultProps===void 0&&t.compare===null?(l.tag=15,l.type=u,yr(e,l,u,a,n)):(e=Su(t.type,null,a,l,l.mode,n),e.ref=l.ref,e.return=l,l.child=e)}if(u=e.child,!ac(e,n)){var i=u.memoizedProps;if(t=t.compare,t=t!==null?t:Xa,t(i,a)&&e.ref===l.ref)return Nl(e,l,n)}return l.flags|=1,e=lt(u,a),e.ref=l.ref,e.return=l,l.child=e}function yr(e,l,t,a,n){if(e!==null){var u=e.memoizedProps;if(Xa(u,a)&&e.ref===l.ref)if(Te=!1,l.pendingProps=a=u,ac(e,n))e.flags&131072&&(Te=!0);else return l.lanes=e.lanes,Nl(e,l,n)}return Fi(e,l,t,a,n)}function vr(e,l,t){var a=l.pendingProps,n=a.children,u=(l.stateNode._pendingVisibility&2)!==0,i=e!==null?e.memoizedState:null;if(ln(e,l),a.mode==="hidden"||u){if(l.flags&128){if(a=i!==null?i.baseLanes|t:t,e!==null){for(n=l.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;l.childLanes=u&~a}else l.childLanes=0,l.child=null;return br(e,l,a,t)}if(t&536870912)l.memoizedState={baseLanes:0,cachePool:null},e!==null&&uu(l,i!==null?i.cachePool:null),i!==null?So(l,i):Ni(),To(l);else return l.lanes=l.childLanes=536870912,br(e,l,i!==null?i.baseLanes|t:t,t)}else i!==null?(uu(l,i.cachePool),So(l,i),Zl(),l.memoizedState=null):(e!==null&&uu(l,null),Ni(),Zl());return Ne(e,l,n,t),l.child}function br(e,l,t,a){var n=Ci();return n=n===null?null:{parent:ve._currentValue,pool:n},l.memoizedState={baseLanes:t,cachePool:n},e!==null&&uu(l,null),Ni(),To(l),e!==null&&tn(e,l,a,!0),null}function ln(e,l){var t=l.ref;if(t===null)e!==null&&e.ref!==null&&(l.flags|=2097664);else{if(typeof t!="function"&&typeof t!="object")throw Error(d(284));(e===null||e.ref!==t)&&(l.flags|=2097664)}}function Fi(e,l,t,a,n){return _t(l),t=ji(e,l,t,a,void 0,n),a=Ri(),e!==null&&!Te?(_i(e,l,n),Nl(e,l,n)):(Z&&a&&Ti(l),l.flags|=1,Ne(e,l,t,n),l.child)}function Sr(e,l,t,a,n,u){return _t(l),l.updateQueue=null,t=Eo(l,a,t,n),No(e),a=Ri(),e!==null&&!Te?(_i(e,l,u),Nl(e,l,u)):(Z&&a&&Ti(l),l.flags|=1,Ne(e,l,t,u),l.child)}function Tr(e,l,t,a,n){if(_t(l),l.stateNode===null){var u=ta,i=t.contextType;typeof i=="object"&&i!==null&&(u=ze(i)),u=new t(a,u),l.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=ki,l.stateNode=u,u._reactInternals=l,u=l.stateNode,u.props=a,u.state=l.memoizedState,u.refs={},cc(l),i=t.contextType,u.context=typeof i=="object"&&i!==null?ze(i):ta,u.state=l.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ki(l,t,i,a),u.state=l.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&ki.enqueueReplaceState(u,u.state,null),un(l,a,u,n),nn(),u.state=l.memoizedState),typeof u.componentDidMount=="function"&&(l.flags|=4194308),a=!0}else if(e===null){u=l.stateNode;var c=l.memoizedProps,s=jt(t,c);u.props=s;var f=u.context,b=t.contextType;i=ta,typeof b=="object"&&b!==null&&(i=ze(b));var T=t.getDerivedStateFromProps;b=typeof T=="function"||typeof u.getSnapshotBeforeUpdate=="function",c=l.pendingProps!==c,b||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c||f!==i)&&cr(l,u,a,i),Fl=!1;var p=l.memoizedState;u.state=p,un(l,a,u,n),nn(),f=l.memoizedState,c||p!==f||Fl?(typeof T=="function"&&(Ki(l,t,T,a),f=l.memoizedState),(s=Fl||ir(l,t,s,a,p,f,i))?(b||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(l.flags|=4194308)):(typeof u.componentDidMount=="function"&&(l.flags|=4194308),l.memoizedProps=a,l.memoizedState=f),u.props=a,u.state=f,u.context=i,a=s):(typeof u.componentDidMount=="function"&&(l.flags|=4194308),a=!1)}else{u=l.stateNode,sc(e,l),i=l.memoizedProps,b=jt(t,i),u.props=b,T=l.pendingProps,p=u.context,f=t.contextType,s=ta,typeof f=="object"&&f!==null&&(s=ze(f)),c=t.getDerivedStateFromProps,(f=typeof c=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==T||p!==s)&&cr(l,u,a,s),Fl=!1,p=l.memoizedState,u.state=p,un(l,a,u,n),nn();var v=l.memoizedState;i!==T||p!==v||Fl||e!==null&&e.dependencies!==null&&pu(e.dependencies)?(typeof c=="function"&&(Ki(l,t,c,a),v=l.memoizedState),(b=Fl||ir(l,t,b,a,p,v,s)||e!==null&&e.dependencies!==null&&pu(e.dependencies))?(f||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,v,s),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,v,s)),typeof u.componentDidUpdate=="function"&&(l.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(l.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(l.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(l.flags|=1024),l.memoizedProps=a,l.memoizedState=v),u.props=a,u.state=v,u.context=s,a=b):(typeof u.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(l.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(l.flags|=1024),a=!1)}return u=a,ln(e,l),a=(l.flags&128)!==0,u||a?(u=l.stateNode,t=a&&typeof t.getDerivedStateFromError!="function"?null:u.render(),l.flags|=1,e!==null&&a?(l.child=Et(l,e.child,null,n),l.child=Et(l,null,t,n)):Ne(e,l,t,n),l.memoizedState=u.state,e=l.child):e=Nl(e,l,n),e}function xr(e,l,t,a){return Qa(),l.flags|=256,Ne(e,l,t,a),l.child}var Pi={dehydrated:null,treeContext:null,retryLane:0};function $i(e){return{baseLanes:e,cachePool:Do()}}function Wi(e,l,t){return e=e!==null?e.childLanes&~t:0,l&&(e|=ll),e}function Ar(e,l,t){var a=l.pendingProps,n=!1,u=(l.flags&128)!==0,i;if((i=u)||(i=e!==null&&e.memoizedState===null?!1:(ye.current&2)!==0),i&&(n=!0,l.flags&=-129),i=(l.flags&32)!==0,l.flags&=-33,e===null){if(Z){if(n?Ql(l):Zl(),Z){var c=De,s;if(s=c){e:{for(s=c,c=fl;s.nodeType!==8;){if(!c){c=null;break e}if(s=cl(s.nextSibling),s===null){c=null;break e}}c=s}c!==null?(l.memoizedState={dehydrated:c,treeContext:At!==null?{id:Tl,overflow:xl}:null,retryLane:536870912},s=el(18,null,null,0),s.stateNode=c,s.return=l,l.child=s,Ce=l,De=null,s=!0):s=!1}s||Nt(l)}if(c=l.memoizedState,c!==null&&(c=c.dehydrated,c!==null))return c.data==="$!"?l.lanes=16:l.lanes=536870912,null;Al(l)}return c=a.children,a=a.fallback,n?(Zl(),n=l.mode,c=ec({mode:"hidden",children:c},n),a=Ht(a,n,t,null),c.return=l,a.return=l,c.sibling=a,l.child=c,n=l.child,n.memoizedState=$i(t),n.childLanes=Wi(e,i,t),l.memoizedState=Pi,a):(Ql(l),Ii(l,c))}if(s=e.memoizedState,s!==null&&(c=s.dehydrated,c!==null)){if(u)l.flags&256?(Ql(l),l.flags&=-257,l=lc(e,l,t)):l.memoizedState!==null?(Zl(),l.child=e.child,l.flags|=128,l=null):(Zl(),n=a.fallback,c=l.mode,a=ec({mode:"visible",children:a.children},c),n=Ht(n,c,t,null),n.flags|=2,a.return=l,n.return=l,a.sibling=n,l.child=a,Et(l,e.child,null,t),a=l.child,a.memoizedState=$i(t),a.childLanes=Wi(e,i,t),l.memoizedState=Pi,l=n);else if(Ql(l),c.data==="$!"){if(i=c.nextSibling&&c.nextSibling.dataset,i)var f=i.dgst;i=f,a=Error(d(419)),a.stack="",a.digest=i,Za({value:a,source:null,stack:null}),l=lc(e,l,t)}else if(Te||tn(e,l,t,!1),i=(t&e.childLanes)!==0,Te||i){if(i=I,i!==null){if(a=t&-t,a&42)a=1;else switch(a){case 2:a=1;break;case 8:a=4;break;case 32:a=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:a=64;break;case 268435456:a=134217728;break;default:a=0}if(a=a&(i.suspendedLanes|t)?0:a,a!==0&&a!==s.retryLane)throw s.retryLane=a,Vl(e,a),Oe(i,e,a),hr}c.data==="$?"||Cc(),l=lc(e,l,t)}else c.data==="$?"?(l.flags|=128,l.child=e.child,l=Dh.bind(null,e),c._reactRetry=l,l=null):(e=s.treeContext,De=cl(c.nextSibling),Ce=l,Z=!0,ul=null,fl=!1,e!==null&&(Fe[Pe++]=Tl,Fe[Pe++]=xl,Fe[Pe++]=At,Tl=e.id,xl=e.overflow,At=l),l=Ii(l,a.children),l.flags|=4096);return l}return n?(Zl(),n=a.fallback,c=l.mode,s=e.child,f=s.sibling,a=lt(s,{mode:"hidden",children:a.children}),a.subtreeFlags=s.subtreeFlags&31457280,f!==null?n=lt(f,n):(n=Ht(n,c,t,null),n.flags|=2),n.return=l,a.return=l,a.sibling=n,l.child=a,a=n,n=l.child,c=e.child.memoizedState,c===null?c=$i(t):(s=c.cachePool,s!==null?(f=ve._currentValue,s=s.parent!==f?{parent:f,pool:f}:s):s=Do(),c={baseLanes:c.baseLanes|t,cachePool:s}),n.memoizedState=c,n.childLanes=Wi(e,i,t),l.memoizedState=Pi,a):(Ql(l),t=e.child,e=t.sibling,t=lt(t,{mode:"visible",children:a.children}),t.return=l,t.sibling=null,e!==null&&(i=l.deletions,i===null?(l.deletions=[e],l.flags|=16):i.push(e)),l.child=t,l.memoizedState=null,t)}function Ii(e,l){return l=ec({mode:"visible",children:l},e.mode),l.return=e,e.child=l}function ec(e,l){return Fr(e,l,0,null)}function lc(e,l,t){return Et(l,e.child,null,t),e=Ii(l,l.pendingProps.children),e.flags|=2,l.memoizedState=null,e}function Dr(e,l,t){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l),uc(e.return,l,t)}function tc(e,l,t,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:l,rendering:null,renderingStartTime:0,last:a,tail:t,tailMode:n}:(u.isBackwards=l,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=t,u.tailMode=n)}function Nr(e,l,t){var a=l.pendingProps,n=a.revealOrder,u=a.tail;if(Ne(e,l,a.children,t),a=ye.current,a&2)a=a&1|2,l.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=l.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Dr(e,t,l);else if(e.tag===19)Dr(e,t,l);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===l)break e;for(;e.sibling===null;){if(e.return===null||e.return===l)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(ae(ye,a),n){case"forwards":for(t=l.child,n=null;t!==null;)e=t.alternate,e!==null&&nu(e)===null&&(n=t),t=t.sibling;t=n,t===null?(n=l.child,l.child=null):(n=t.sibling,t.sibling=null),tc(l,!1,n,t,u);break;case"backwards":for(t=null,n=l.child,l.child=null;n!==null;){if(e=n.alternate,e!==null&&nu(e)===null){l.child=n;break}e=n.sibling,n.sibling=t,t=n,n=e}tc(l,!0,t,null,u);break;case"together":tc(l,!1,null,null,void 0);break;default:l.memoizedState=null}return l.child}function Nl(e,l,t){if(e!==null&&(l.dependencies=e.dependencies),tt|=l.lanes,!(t&l.childLanes))if(e!==null){if(tn(e,l,t,!1),(t&l.childLanes)===0)return null}else return null;if(e!==null&&l.child!==e.child)throw Error(d(153));if(l.child!==null){for(e=l.child,t=lt(e,e.pendingProps),l.child=t,t.return=l;e.sibling!==null;)e=e.sibling,t=t.sibling=lt(e,e.pendingProps),t.return=l;t.sibling=null}return l.child}function ac(e,l){return e.lanes&l?!0:(e=e.dependencies,!!(e!==null&&pu(e)))}function rh(e,l,t){switch(l.tag){case 3:Rn(l,l.stateNode.containerInfo),Jl(l,ve,e.memoizedState.cache),Qa();break;case 27:case 5:ku(l);break;case 4:Rn(l,l.stateNode.containerInfo);break;case 10:Jl(l,l.type,l.memoizedProps.value);break;case 13:var a=l.memoizedState;if(a!==null)return a.dehydrated!==null?(Ql(l),l.flags|=128,null):t&l.child.childLanes?Ar(e,l,t):(Ql(l),e=Nl(e,l,t),e!==null?e.sibling:null);Ql(l);break;case 19:var n=(e.flags&128)!==0;if(a=(t&l.childLanes)!==0,a||(tn(e,l,t,!1),a=(t&l.childLanes)!==0),n){if(a)return Nr(e,l,t);l.flags|=128}if(n=l.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),ae(ye,ye.current),a)break;return null;case 22:case 23:return l.lanes=0,vr(e,l,t);case 24:Jl(l,ve,e.memoizedState.cache)}return Nl(e,l,t)}function Er(e,l,t){if(e!==null)if(e.memoizedProps!==l.pendingProps)Te=!0;else{if(!ac(e,t)&&!(l.flags&128))return Te=!1,rh(e,l,t);Te=!!(e.flags&131072)}else Te=!1,Z&&l.flags&1048576&&oo(l,In,l.index);switch(l.lanes=0,l.tag){case 16:e:{e=l.pendingProps;var a=l.elementType,n=a._init;if(a=n(a._payload),l.type=a,typeof a=="function")yc(a)?(e=jt(a,e),l.tag=1,l=Tr(null,l,a,e,t)):(l.tag=0,l=Fi(null,l,a,e,t));else{if(a!=null){if(n=a.$$typeof,n===le){l.tag=11,l=gr(null,l,a,e,t);break e}else if(n===rt){l.tag=14,l=pr(null,l,a,e,t);break e}}throw l=Yt(a)||a,Error(d(306,l,""))}}return l;case 0:return Fi(e,l,l.type,l.pendingProps,t);case 1:return a=l.type,n=jt(a,l.pendingProps),Tr(e,l,a,n,t);case 3:e:{if(Rn(l,l.stateNode.containerInfo),e===null)throw Error(d(387));var u=l.pendingProps;n=l.memoizedState,a=n.element,sc(e,l),un(l,u,null,t);var i=l.memoizedState;if(u=i.cache,Jl(l,ve,u),u!==n.cache&&ic(l,[ve],t,!0),nn(),u=i.element,n.isDehydrated)if(n={element:u,isDehydrated:!1,cache:i.cache},l.updateQueue.baseState=n,l.memoizedState=n,l.flags&256){l=xr(e,l,u,t);break e}else if(u!==a){a=Je(Error(d(424)),l),Za(a),l=xr(e,l,u,t);break e}else for(De=cl(l.stateNode.containerInfo.firstChild),Ce=l,Z=!0,ul=null,fl=!0,t=bo(l,null,u,t),l.child=t;t;)t.flags=t.flags&-3|4096,t=t.sibling;else{if(Qa(),u===a){l=Nl(e,l,t);break e}Ne(e,l,u,t)}l=l.child}return l;case 26:return ln(e,l),e===null?(t=Of(l.type,null,l.pendingProps,null))?l.memoizedState=t:Z||(t=l.type,e=l.pendingProps,a=Ru(Gl.current).createElement(t),a[Me]=l,a[Re]=e,Ee(a,t,e),Se(a),l.stateNode=a):l.memoizedState=Of(l.type,e.memoizedProps,l.pendingProps,e.memoizedState),null;case 27:return ku(l),e===null&&Z&&(a=l.stateNode=Mf(l.type,l.pendingProps,Gl.current),Ce=l,fl=!0,De=cl(a.firstChild)),a=l.pendingProps.children,e!==null||Z?Ne(e,l,a,t):l.child=Et(l,null,a,t),ln(e,l),l.child;case 5:return e===null&&Z&&((n=a=De)&&(a=Yh(a,l.type,l.pendingProps,fl),a!==null?(l.stateNode=a,Ce=l,De=cl(a.firstChild),fl=!1,n=!0):n=!1),n||Nt(l)),ku(l),n=l.type,u=l.pendingProps,i=e!==null?e.memoizedProps:null,a=u.children,Vc(n,u)?a=null:i!==null&&Vc(n,i)&&(l.flags|=32),l.memoizedState!==null&&(n=ji(e,l,ah,null,null,t),Tn._currentValue=n),ln(e,l),Ne(e,l,a,t),l.child;case 6:return e===null&&Z&&((e=t=De)&&(t=Xh(t,l.pendingProps,fl),t!==null?(l.stateNode=t,Ce=l,De=null,e=!0):e=!1),e||Nt(l)),null;case 13:return Ar(e,l,t);case 4:return Rn(l,l.stateNode.containerInfo),a=l.pendingProps,e===null?l.child=Et(l,null,a,t):Ne(e,l,a,t),l.child;case 11:return gr(e,l,l.type,l.pendingProps,t);case 7:return Ne(e,l,l.pendingProps,t),l.child;case 8:return Ne(e,l,l.pendingProps.children,t),l.child;case 12:return Ne(e,l,l.pendingProps.children,t),l.child;case 10:return a=l.pendingProps,Jl(l,l.type,a.value),Ne(e,l,a.children,t),l.child;case 9:return n=l.type._context,a=l.pendingProps.children,_t(l),n=ze(n),a=a(n),l.flags|=1,Ne(e,l,a,t),l.child;case 14:return pr(e,l,l.type,l.pendingProps,t);case 15:return yr(e,l,l.type,l.pendingProps,t);case 19:return Nr(e,l,t);case 22:return vr(e,l,t);case 24:return _t(l),a=ze(ve),e===null?(n=Ci(),n===null&&(n=I,u=Mi(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=t),n=u),l.memoizedState={parent:a,cache:n},cc(l),Jl(l,ve,n)):(e.lanes&t&&(sc(e,l),un(l,null,null,t),nn()),n=e.memoizedState,u=l.memoizedState,n.parent!==a?(n={parent:a,cache:a},l.memoizedState=n,l.lanes===0&&(l.memoizedState=l.updateQueue.baseState=n),Jl(l,ve,a)):(a=u.cache,Jl(l,ve,a),a!==n.cache&&ic(l,[ve],t,!0))),Ne(e,l,l.pendingProps.children,t),l.child;case 29:throw l.pendingProps}throw Error(d(156,l.tag))}var nc=sl(null),Rt=null,El=null;function Jl(e,l,t){ae(nc,l._currentValue),l._currentValue=t}function Ml(e){e._currentValue=nc.current,be(nc)}function uc(e,l,t){for(;e!==null;){var a=e.alternate;if((e.childLanes&l)!==l?(e.childLanes|=l,a!==null&&(a.childLanes|=l)):a!==null&&(a.childLanes&l)!==l&&(a.childLanes|=l),e===t)break;e=e.return}}function ic(e,l,t,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;e:for(;u!==null;){var c=u;u=n;for(var s=0;s<l.length;s++)if(c.context===l[s]){u.lanes|=t,c=u.alternate,c!==null&&(c.lanes|=t),uc(u.return,t,e),a||(i=null);break e}u=c.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(d(341));i.lanes|=t,u=i.alternate,u!==null&&(u.lanes|=t),uc(i,t,e),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===e){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function tn(e,l,t,a){e=null;for(var n=l,u=!1;n!==null;){if(!u){if(n.flags&524288)u=!0;else if(n.flags&262144)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(d(387));if(i=i.memoizedProps,i!==null){var c=n.type;Be(n.pendingProps.value,i.value)||(e!==null?e.push(c):e=[c])}}else if(n===jn.current){if(i=n.alternate,i===null)throw Error(d(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Tn):e=[Tn])}n=n.return}e!==null&&ic(l,e,t,a),l.flags|=262144}function pu(e){for(e=e.firstContext;e!==null;){if(!Be(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function _t(e){Rt=e,El=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ze(e){return Mr(Rt,e)}function yu(e,l){return Rt===null&&_t(e),Mr(e,l)}function Mr(e,l){var t=l._currentValue;if(l={context:l,memoizedValue:t,next:null},El===null){if(e===null)throw Error(d(308));El=l,e.dependencies={lanes:0,firstContext:l},e.flags|=524288}else El=El.next=l;return t}var Fl=!1;function cc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function sc(e,l){e=e.updateQueue,l.updateQueue===e&&(l.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Pl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function $l(e,l,t){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,ue&2){var n=a.pending;return n===null?l.next=l:(l.next=n.next,n.next=l),a.pending=l,l=$n(e),co(e,null,t),l}return Pn(e,a,l,t),$n(e)}function an(e,l,t){if(l=l.updateQueue,l!==null&&(l=l.shared,(t&4194176)!==0)){var a=l.lanes;a&=e.pendingLanes,t|=a,l.lanes=t,gs(e,t)}}function oc(e,l){var t=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,t===a)){var n=null,u=null;if(t=t.firstBaseUpdate,t!==null){do{var i={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,t=t.next}while(t!==null);u===null?n=u=l:u=u.next=l}else n=u=l;t={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=t;return}e=t.lastBaseUpdate,e===null?t.firstBaseUpdate=l:e.next=l,t.lastBaseUpdate=l}var rc=!1;function nn(){if(rc){var e=sa;if(e!==null)throw e}}function un(e,l,t,a){rc=!1;var n=e.updateQueue;Fl=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,c=n.shared.pending;if(c!==null){n.shared.pending=null;var s=c,f=s.next;s.next=null,i===null?u=f:i.next=f,i=s;var b=e.alternate;b!==null&&(b=b.updateQueue,c=b.lastBaseUpdate,c!==i&&(c===null?b.firstBaseUpdate=f:c.next=f,b.lastBaseUpdate=s))}if(u!==null){var T=n.baseState;i=0,b=f=s=null,c=u;do{var p=c.lane&-536870913,v=p!==c.lane;if(v?(V&p)===p:(a&p)===p){p!==0&&p===ca&&(rc=!0),b!==null&&(b=b.next={lane:0,tag:c.tag,payload:c.payload,callback:null,next:null});e:{var M=e,U=c;p=l;var se=t;switch(U.tag){case 1:if(M=U.payload,typeof M=="function"){T=M.call(se,T,p);break e}T=M;break e;case 3:M.flags=M.flags&-65537|128;case 0:if(M=U.payload,p=typeof M=="function"?M.call(se,T,p):M,p==null)break e;T=k({},T,p);break e;case 2:Fl=!0}}p=c.callback,p!==null&&(e.flags|=64,v&&(e.flags|=8192),v=n.callbacks,v===null?n.callbacks=[p]:v.push(p))}else v={lane:p,tag:c.tag,payload:c.payload,callback:c.callback,next:null},b===null?(f=b=v,s=T):b=b.next=v,i|=p;if(c=c.next,c===null){if(c=n.shared.pending,c===null)break;v=c,c=v.next,v.next=null,n.lastBaseUpdate=v,n.shared.pending=null}}while(!0);b===null&&(s=T),n.baseState=s,n.firstBaseUpdate=f,n.lastBaseUpdate=b,u===null&&(n.shared.lanes=0),tt|=i,e.lanes=i,e.memoizedState=T}}function zr(e,l){if(typeof e!="function")throw Error(d(191,e));e.call(l)}function Cr(e,l){var t=e.callbacks;if(t!==null)for(e.callbacks=null,e=0;e<t.length;e++)zr(t[e],l)}function cn(e,l){try{var t=l.updateQueue,a=t!==null?t.lastEffect:null;if(a!==null){var n=a.next;t=n;do{if((t.tag&e)===e){a=void 0;var u=t.create,i=t.inst;a=u(),i.destroy=a}t=t.next}while(t!==n)}}catch(c){W(l,l.return,c)}}function Wl(e,l,t){try{var a=l.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var i=a.inst,c=i.destroy;if(c!==void 0){i.destroy=void 0,n=l;var s=t;try{c()}catch(f){W(n,s,f)}}}a=a.next}while(a!==u)}}catch(f){W(l,l.return,f)}}function Or(e){var l=e.updateQueue;if(l!==null){var t=e.stateNode;try{Cr(l,t)}catch(a){W(e,e.return,a)}}}function jr(e,l,t){t.props=jt(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(a){W(e,l,a)}}function Ut(e,l){try{var t=e.ref;if(t!==null){var a=e.stateNode;switch(e.tag){case 26:case 27:case 5:var n=a;break;default:n=a}typeof t=="function"?e.refCleanup=t(n):t.current=n}}catch(u){W(e,l,u)}}function qe(e,l){var t=e.ref,a=e.refCleanup;if(t!==null)if(typeof a=="function")try{a()}catch(n){W(e,l,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof t=="function")try{t(null)}catch(n){W(e,l,n)}else t.current=null}function Rr(e){var l=e.type,t=e.memoizedProps,a=e.stateNode;try{e:switch(l){case"button":case"input":case"select":case"textarea":t.autoFocus&&a.focus();break e;case"img":t.src?a.src=t.src:t.srcSet&&(a.srcset=t.srcSet)}}catch(n){W(e,e.return,n)}}function _r(e,l,t){try{var a=e.stateNode;Hh(a,e.type,t,l),a[Re]=l}catch(n){W(e,e.return,n)}}function Ur(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27||e.tag===4}function fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ur(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==27&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function dc(e,l,t){var a=e.tag;if(a===5||a===6)e=e.stateNode,l?t.nodeType===8?t.parentNode.insertBefore(e,l):t.insertBefore(e,l):(t.nodeType===8?(l=t.parentNode,l.insertBefore(e,t)):(l=t,l.appendChild(e)),t=t._reactRootContainer,t!=null||l.onclick!==null||(l.onclick=ju));else if(a!==4&&a!==27&&(e=e.child,e!==null))for(dc(e,l,t),e=e.sibling;e!==null;)dc(e,l,t),e=e.sibling}function vu(e,l,t){var a=e.tag;if(a===5||a===6)e=e.stateNode,l?t.insertBefore(e,l):t.appendChild(e);else if(a!==4&&a!==27&&(e=e.child,e!==null))for(vu(e,l,t),e=e.sibling;e!==null;)vu(e,l,t),e=e.sibling}var zl=!1,ie=!1,mc=!1,Hr=typeof WeakSet=="function"?WeakSet:Set,xe=null,Lr=!1;function fh(e,l){if(e=e.containerInfo,Xc=qu,e=Ws(e),gi(e)){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{t=(t=e.ownerDocument)&&t.defaultView||window;var a=t.getSelection&&t.getSelection();if(a&&a.rangeCount!==0){t=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{t.nodeType,u.nodeType}catch{t=null;break e}var i=0,c=-1,s=-1,f=0,b=0,T=e,p=null;l:for(;;){for(var v;T!==t||n!==0&&T.nodeType!==3||(c=i+n),T!==u||a!==0&&T.nodeType!==3||(s=i+a),T.nodeType===3&&(i+=T.nodeValue.length),(v=T.firstChild)!==null;)p=T,T=v;for(;;){if(T===e)break l;if(p===t&&++f===n&&(c=i),p===u&&++b===a&&(s=i),(v=T.nextSibling)!==null)break;T=p,p=T.parentNode}T=v}t=c===-1||s===-1?null:{start:c,end:s}}else t=null}t=t||{start:0,end:0}}else t=null;for(wc={focusedElem:e,selectionRange:t},qu=!1,xe=l;xe!==null;)if(l=xe,e=l.child,(l.subtreeFlags&1028)!==0&&e!==null)e.return=l,xe=e;else for(;xe!==null;){switch(l=xe,u=l.alternate,e=l.flags,l.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&u!==null){e=void 0,t=l,n=u.memoizedProps,u=u.memoizedState,a=t.stateNode;try{var M=jt(t.type,n,t.elementType===t.type);e=a.getSnapshotBeforeUpdate(M,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(U){W(t,t.return,U)}}break;case 3:if(e&1024){if(e=l.stateNode.containerInfo,t=e.nodeType,t===9)Kc(e);else if(t===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Kc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(d(163))}if(e=l.sibling,e!==null){e.return=l.return,xe=e;break}xe=l.return}return M=Lr,Lr=!1,M}function Br(e,l,t){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Ol(e,t),a&4&&cn(5,t);break;case 1:if(Ol(e,t),a&4)if(e=t.stateNode,l===null)try{e.componentDidMount()}catch(c){W(t,t.return,c)}else{var n=jt(t.type,l.memoizedProps);l=l.memoizedState;try{e.componentDidUpdate(n,l,e.__reactInternalSnapshotBeforeUpdate)}catch(c){W(t,t.return,c)}}a&64&&Or(t),a&512&&Ut(t,t.return);break;case 3:if(Ol(e,t),a&64&&(a=t.updateQueue,a!==null)){if(e=null,t.child!==null)switch(t.child.tag){case 27:case 5:e=t.child.stateNode;break;case 1:e=t.child.stateNode}try{Cr(a,e)}catch(c){W(t,t.return,c)}}break;case 26:Ol(e,t),a&512&&Ut(t,t.return);break;case 27:case 5:Ol(e,t),l===null&&a&4&&Rr(t),a&512&&Ut(t,t.return);break;case 12:Ol(e,t);break;case 13:Ol(e,t),a&4&&Yr(e,t);break;case 22:if(n=t.memoizedState!==null||zl,!n){l=l!==null&&l.memoizedState!==null||ie;var u=zl,i=ie;zl=n,(ie=l)&&!i?Il(e,t,(t.subtreeFlags&8772)!==0):Ol(e,t),zl=u,ie=i}a&512&&(t.memoizedProps.mode==="manual"?Ut(t,t.return):qe(t,t.return));break;default:Ol(e,t)}}function qr(e){var l=e.alternate;l!==null&&(e.alternate=null,qr(l)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(l=e.stateNode,l!==null&&Iu(l)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var me=null,Ge=!1;function Cl(e,l,t){for(t=t.child;t!==null;)Gr(e,l,t),t=t.sibling}function Gr(e,l,t){if(He&&typeof He.onCommitFiberUnmount=="function")try{He.onCommitFiberUnmount(Ca,t)}catch{}switch(t.tag){case 26:ie||qe(t,l),Cl(e,l,t),t.memoizedState?t.memoizedState.count--:t.stateNode&&(t=t.stateNode,t.parentNode.removeChild(t));break;case 27:ie||qe(t,l);var a=me,n=Ge;for(me=t.stateNode,Cl(e,l,t),t=t.stateNode,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);Iu(t),me=a,Ge=n;break;case 5:ie||qe(t,l);case 6:n=me;var u=Ge;if(me=null,Cl(e,l,t),me=n,Ge=u,me!==null)if(Ge)try{e=me,a=t.stateNode,e.nodeType===8?e.parentNode.removeChild(a):e.removeChild(a)}catch(i){W(t,l,i)}else try{me.removeChild(t.stateNode)}catch(i){W(t,l,i)}break;case 18:me!==null&&(Ge?(l=me,t=t.stateNode,l.nodeType===8?Zc(l.parentNode,t):l.nodeType===1&&Zc(l,t),Nn(l)):Zc(me,t.stateNode));break;case 4:a=me,n=Ge,me=t.stateNode.containerInfo,Ge=!0,Cl(e,l,t),me=a,Ge=n;break;case 0:case 11:case 14:case 15:ie||Wl(2,t,l),ie||Wl(4,t,l),Cl(e,l,t);break;case 1:ie||(qe(t,l),a=t.stateNode,typeof a.componentWillUnmount=="function"&&jr(t,l,a)),Cl(e,l,t);break;case 21:Cl(e,l,t);break;case 22:ie||qe(t,l),ie=(a=ie)||t.memoizedState!==null,Cl(e,l,t),ie=a;break;default:Cl(e,l,t)}}function Yr(e,l){if(l.memoizedState===null&&(e=l.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Nn(e)}catch(t){W(l,l.return,t)}}function dh(e){switch(e.tag){case 13:case 19:var l=e.stateNode;return l===null&&(l=e.stateNode=new Hr),l;case 22:return e=e.stateNode,l=e._retryCache,l===null&&(l=e._retryCache=new Hr),l;default:throw Error(d(435,e.tag))}}function hc(e,l){var t=dh(e);l.forEach(function(a){var n=Nh.bind(null,e,a);t.has(a)||(t.add(a),a.then(n,n))})}function We(e,l){var t=l.deletions;if(t!==null)for(var a=0;a<t.length;a++){var n=t[a],u=e,i=l,c=i;e:for(;c!==null;){switch(c.tag){case 27:case 5:me=c.stateNode,Ge=!1;break e;case 3:me=c.stateNode.containerInfo,Ge=!0;break e;case 4:me=c.stateNode.containerInfo,Ge=!0;break e}c=c.return}if(me===null)throw Error(d(160));Gr(u,i,n),me=null,Ge=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(l.subtreeFlags&13878)for(l=l.child;l!==null;)Xr(l,e),l=l.sibling}var il=null;function Xr(e,l){var t=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:We(l,e),Ie(e),a&4&&(Wl(3,e,e.return),cn(3,e),Wl(5,e,e.return));break;case 1:We(l,e),Ie(e),a&512&&(ie||t===null||qe(t,t.return)),a&64&&zl&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=t===null?a:t.concat(a))));break;case 26:var n=il;if(We(l,e),Ie(e),a&512&&(ie||t===null||qe(t,t.return)),a&4){var u=t!==null?t.memoizedState:null;if(a=e.memoizedState,t===null)if(a===null)if(e.stateNode===null){e:{a=e.type,t=e.memoizedProps,n=n.ownerDocument||n;l:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ra]||u[Me]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Ee(u,a,t),u[Me]=e,Se(u),a=u;break e;case"link":var i=_f("link","href",n).get(a+(t.href||""));if(i){for(var c=0;c<i.length;c++)if(u=i[c],u.getAttribute("href")===(t.href==null?null:t.href)&&u.getAttribute("rel")===(t.rel==null?null:t.rel)&&u.getAttribute("title")===(t.title==null?null:t.title)&&u.getAttribute("crossorigin")===(t.crossOrigin==null?null:t.crossOrigin)){i.splice(c,1);break l}}u=n.createElement(a),Ee(u,a,t),n.head.appendChild(u);break;case"meta":if(i=_f("meta","content",n).get(a+(t.content||""))){for(c=0;c<i.length;c++)if(u=i[c],u.getAttribute("content")===(t.content==null?null:""+t.content)&&u.getAttribute("name")===(t.name==null?null:t.name)&&u.getAttribute("property")===(t.property==null?null:t.property)&&u.getAttribute("http-equiv")===(t.httpEquiv==null?null:t.httpEquiv)&&u.getAttribute("charset")===(t.charSet==null?null:t.charSet)){i.splice(c,1);break l}}u=n.createElement(a),Ee(u,a,t),n.head.appendChild(u);break;default:throw Error(d(468,a))}u[Me]=e,Se(u),a=u}e.stateNode=a}else Uf(n,e.type,e.stateNode);else e.stateNode=Rf(n,a,e.memoizedProps);else u!==a?(u===null?t.stateNode!==null&&(t=t.stateNode,t.parentNode.removeChild(t)):u.count--,a===null?Uf(n,e.type,e.stateNode):Rf(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&_r(e,e.memoizedProps,t.memoizedProps)}break;case 27:if(a&4&&e.alternate===null){n=e.stateNode,u=e.memoizedProps;try{for(var s=n.firstChild;s;){var f=s.nextSibling,b=s.nodeName;s[Ra]||b==="HEAD"||b==="BODY"||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&s.rel.toLowerCase()==="stylesheet"||n.removeChild(s),s=f}for(var T=e.type,p=n.attributes;p.length;)n.removeAttributeNode(p[0]);Ee(n,T,u),n[Me]=e,n[Re]=u}catch(M){W(e,e.return,M)}}case 5:if(We(l,e),Ie(e),a&512&&(ie||t===null||qe(t,t.return)),e.flags&32){n=e.stateNode;try{Ft(n,"")}catch(M){W(e,e.return,M)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,_r(e,n,t!==null?t.memoizedProps:n)),a&1024&&(mc=!0);break;case 6:if(We(l,e),Ie(e),a&4){if(e.stateNode===null)throw Error(d(162));a=e.memoizedProps,t=e.stateNode;try{t.nodeValue=a}catch(M){W(e,e.return,M)}}break;case 3:if(Hu=null,n=il,il=_u(l.containerInfo),We(l,e),il=n,Ie(e),a&4&&t!==null&&t.memoizedState.isDehydrated)try{Nn(l.containerInfo)}catch(M){W(e,e.return,M)}mc&&(mc=!1,wr(e));break;case 4:a=il,il=_u(e.stateNode.containerInfo),We(l,e),Ie(e),il=a;break;case 12:We(l,e),Ie(e);break;case 13:We(l,e),Ie(e),e.child.flags&8192&&e.memoizedState!==null!=(t!==null&&t.memoizedState!==null)&&(Ac=rl()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 22:if(a&512&&(ie||t===null||qe(t,t.return)),s=e.memoizedState!==null,f=t!==null&&t.memoizedState!==null,b=zl,T=ie,zl=b||s,ie=T||f,We(l,e),ie=T,zl=b,Ie(e),l=e.stateNode,l._current=e,l._visibility&=-3,l._visibility|=l._pendingVisibility&2,a&8192&&(l._visibility=s?l._visibility&-2:l._visibility|1,s&&(l=zl||ie,t===null||f||l||da(e)),e.memoizedProps===null||e.memoizedProps.mode!=="manual"))e:for(t=null,l=e;;){if(l.tag===5||l.tag===26||l.tag===27){if(t===null){f=t=l;try{if(n=f.stateNode,s)u=n.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none";else{i=f.stateNode,c=f.memoizedProps.style;var v=c!=null&&c.hasOwnProperty("display")?c.display:null;i.style.display=v==null||typeof v=="boolean"?"":(""+v).trim()}}catch(M){W(f,f.return,M)}}}else if(l.tag===6){if(t===null){f=l;try{f.stateNode.nodeValue=s?"":f.memoizedProps}catch(M){W(f,f.return,M)}}}else if((l.tag!==22&&l.tag!==23||l.memoizedState===null||l===e)&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===e)break e;for(;l.sibling===null;){if(l.return===null||l.return===e)break e;t===l&&(t=null),l=l.return}t===l&&(t=null),l.sibling.return=l.return,l=l.sibling}a&4&&(a=e.updateQueue,a!==null&&(t=a.retryQueue,t!==null&&(a.retryQueue=null,hc(e,t))));break;case 19:We(l,e),Ie(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 21:break;default:We(l,e),Ie(e)}}function Ie(e){var l=e.flags;if(l&2){try{if(e.tag!==27){e:{for(var t=e.return;t!==null;){if(Ur(t)){var a=t;break e}t=t.return}throw Error(d(160))}switch(a.tag){case 27:var n=a.stateNode,u=fc(e);vu(e,u,n);break;case 5:var i=a.stateNode;a.flags&32&&(Ft(i,""),a.flags&=-33);var c=fc(e);vu(e,c,i);break;case 3:case 4:var s=a.stateNode.containerInfo,f=fc(e);dc(e,f,s);break;default:throw Error(d(161))}}}catch(b){W(e,e.return,b)}e.flags&=-3}l&4096&&(e.flags&=-4097)}function wr(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var l=e;wr(l),l.tag===5&&l.flags&1024&&l.stateNode.reset(),e=e.sibling}}function Ol(e,l){if(l.subtreeFlags&8772)for(l=l.child;l!==null;)Br(e,l.alternate,l),l=l.sibling}function da(e){for(e=e.child;e!==null;){var l=e;switch(l.tag){case 0:case 11:case 14:case 15:Wl(4,l,l.return),da(l);break;case 1:qe(l,l.return);var t=l.stateNode;typeof t.componentWillUnmount=="function"&&jr(l,l.return,t),da(l);break;case 26:case 27:case 5:qe(l,l.return),da(l);break;case 22:qe(l,l.return),l.memoizedState===null&&da(l);break;default:da(l)}e=e.sibling}}function Il(e,l,t){for(t=t&&(l.subtreeFlags&8772)!==0,l=l.child;l!==null;){var a=l.alternate,n=e,u=l,i=u.flags;switch(u.tag){case 0:case 11:case 15:Il(n,u,t),cn(4,u);break;case 1:if(Il(n,u,t),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(f){W(a,a.return,f)}if(a=u,n=a.updateQueue,n!==null){var c=a.stateNode;try{var s=n.shared.hiddenCallbacks;if(s!==null)for(n.shared.hiddenCallbacks=null,n=0;n<s.length;n++)zr(s[n],c)}catch(f){W(a,a.return,f)}}t&&i&64&&Or(u),Ut(u,u.return);break;case 26:case 27:case 5:Il(n,u,t),t&&a===null&&i&4&&Rr(u),Ut(u,u.return);break;case 12:Il(n,u,t);break;case 13:Il(n,u,t),t&&i&4&&Yr(n,u);break;case 22:u.memoizedState===null&&Il(n,u,t),Ut(u,u.return);break;default:Il(n,u,t)}l=l.sibling}}function gc(e,l){var t=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),e=null,l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(e=l.memoizedState.cachePool.pool),e!==t&&(e!=null&&e.refCount++,t!=null&&Pa(t))}function pc(e,l){e=null,l.alternate!==null&&(e=l.alternate.memoizedState.cache),l=l.memoizedState.cache,l!==e&&(l.refCount++,e!=null&&Pa(e))}function et(e,l,t,a){if(l.subtreeFlags&10256)for(l=l.child;l!==null;)Vr(e,l,t,a),l=l.sibling}function Vr(e,l,t,a){var n=l.flags;switch(l.tag){case 0:case 11:case 15:et(e,l,t,a),n&2048&&cn(9,l);break;case 3:et(e,l,t,a),n&2048&&(e=null,l.alternate!==null&&(e=l.alternate.memoizedState.cache),l=l.memoizedState.cache,l!==e&&(l.refCount++,e!=null&&Pa(e)));break;case 12:if(n&2048){et(e,l,t,a),e=l.stateNode;try{var u=l.memoizedProps,i=u.id,c=u.onPostCommit;typeof c=="function"&&c(i,l.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(s){W(l,l.return,s)}}else et(e,l,t,a);break;case 23:break;case 22:u=l.stateNode,l.memoizedState!==null?u._visibility&4?et(e,l,t,a):sn(e,l):u._visibility&4?et(e,l,t,a):(u._visibility|=4,ma(e,l,t,a,(l.subtreeFlags&10256)!==0)),n&2048&&gc(l.alternate,l);break;case 24:et(e,l,t,a),n&2048&&pc(l.alternate,l);break;default:et(e,l,t,a)}}function ma(e,l,t,a,n){for(n=n&&(l.subtreeFlags&10256)!==0,l=l.child;l!==null;){var u=e,i=l,c=t,s=a,f=i.flags;switch(i.tag){case 0:case 11:case 15:ma(u,i,c,s,n),cn(8,i);break;case 23:break;case 22:var b=i.stateNode;i.memoizedState!==null?b._visibility&4?ma(u,i,c,s,n):sn(u,i):(b._visibility|=4,ma(u,i,c,s,n)),n&&f&2048&&gc(i.alternate,i);break;case 24:ma(u,i,c,s,n),n&&f&2048&&pc(i.alternate,i);break;default:ma(u,i,c,s,n)}l=l.sibling}}function sn(e,l){if(l.subtreeFlags&10256)for(l=l.child;l!==null;){var t=e,a=l,n=a.flags;switch(a.tag){case 22:sn(t,a),n&2048&&gc(a.alternate,a);break;case 24:sn(t,a),n&2048&&pc(a.alternate,a);break;default:sn(t,a)}l=l.sibling}}var on=8192;function ha(e){if(e.subtreeFlags&on)for(e=e.child;e!==null;)Qr(e),e=e.sibling}function Qr(e){switch(e.tag){case 26:ha(e),e.flags&on&&e.memoizedState!==null&&eg(il,e.memoizedState,e.memoizedProps);break;case 5:ha(e);break;case 3:case 4:var l=il;il=_u(e.stateNode.containerInfo),ha(e),il=l;break;case 22:e.memoizedState===null&&(l=e.alternate,l!==null&&l.memoizedState!==null?(l=on,on=16777216,ha(e),on=l):ha(e));break;default:ha(e)}}function Zr(e){var l=e.alternate;if(l!==null&&(e=l.child,e!==null)){l.child=null;do l=e.sibling,e.sibling=null,e=l;while(e!==null)}}function rn(e){var l=e.deletions;if(e.flags&16){if(l!==null)for(var t=0;t<l.length;t++){var a=l[t];xe=a,kr(a,e)}Zr(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Kr(e),e=e.sibling}function Kr(e){switch(e.tag){case 0:case 11:case 15:rn(e),e.flags&2048&&Wl(9,e,e.return);break;case 3:rn(e);break;case 12:rn(e);break;case 22:var l=e.stateNode;e.memoizedState!==null&&l._visibility&4&&(e.return===null||e.return.tag!==13)?(l._visibility&=-5,bu(e)):rn(e);break;default:rn(e)}}function bu(e){var l=e.deletions;if(e.flags&16){if(l!==null)for(var t=0;t<l.length;t++){var a=l[t];xe=a,kr(a,e)}Zr(e)}for(e=e.child;e!==null;){switch(l=e,l.tag){case 0:case 11:case 15:Wl(8,l,l.return),bu(l);break;case 22:t=l.stateNode,t._visibility&4&&(t._visibility&=-5,bu(l));break;default:bu(l)}e=e.sibling}}function kr(e,l){for(;xe!==null;){var t=xe;switch(t.tag){case 0:case 11:case 15:Wl(8,t,l);break;case 23:case 22:if(t.memoizedState!==null&&t.memoizedState.cachePool!==null){var a=t.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Pa(t.memoizedState.cache)}if(a=t.child,a!==null)a.return=t,xe=a;else e:for(t=e;xe!==null;){a=xe;var n=a.sibling,u=a.return;if(qr(a),a===t){xe=null;break e}if(n!==null){n.return=u,xe=n;break e}xe=u}}}function mh(e,l,t,a){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=l,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function el(e,l,t,a){return new mh(e,l,t,a)}function yc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function lt(e,l){var t=e.alternate;return t===null?(t=el(e.tag,l,e.key,e.mode),t.elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=l,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=e.flags&31457280,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,l=e.dependencies,t.dependencies=l===null?null:{lanes:l.lanes,firstContext:l.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t.refCleanup=e.refCleanup,t}function Jr(e,l){e.flags&=31457282;var t=e.alternate;return t===null?(e.childLanes=0,e.lanes=l,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,l=t.dependencies,e.dependencies=l===null?null:{lanes:l.lanes,firstContext:l.firstContext}),e}function Su(e,l,t,a,n,u){var i=0;if(a=e,typeof e=="function")yc(e)&&(i=1);else if(typeof e=="string")i=Wh(e,t,ol.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Y:return Ht(t.children,n,u,l);case $:i=8,n|=24;break;case he:return e=el(12,t,l,n|2),e.elementType=he,e.lanes=u,e;case C:return e=el(13,t,l,n),e.elementType=C,e.lanes=u,e;case pl:return e=el(19,t,l,n),e.elementType=pl,e.lanes=u,e;case ft:return Fr(t,n,u,l);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ge:case pe:i=10;break e;case w:i=9;break e;case le:i=11;break e;case rt:i=14;break e;case je:i=16,a=null;break e}i=29,t=Error(d(130,e===null?"null":typeof e,"")),a=null}return l=el(i,t,l,n),l.elementType=e,l.type=a,l.lanes=u,l}function Ht(e,l,t,a){return e=el(7,e,a,l),e.lanes=t,e}function Fr(e,l,t,a){e=el(22,e,a,l),e.elementType=ft,e.lanes=t;var n={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var u=n._current;if(u===null)throw Error(d(456));if(!(n._pendingVisibility&2)){var i=Vl(u,2);i!==null&&(n._pendingVisibility|=2,Oe(i,u,2))}},attach:function(){var u=n._current;if(u===null)throw Error(d(456));if(n._pendingVisibility&2){var i=Vl(u,2);i!==null&&(n._pendingVisibility&=-3,Oe(i,u,2))}}};return e.stateNode=n,e}function vc(e,l,t){return e=el(6,e,null,l),e.lanes=t,e}function bc(e,l,t){return l=el(4,e.children!==null?e.children:[],e.key,l),l.lanes=t,l.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},l}function jl(e){e.flags|=4}function Pr(e,l){if(l.type!=="stylesheet"||l.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!Hf(l)){if(l=$e.current,l!==null&&((V&4194176)===V?dl!==null:(V&62914560)!==V&&!(V&536870912)||l!==dl))throw ka=Di,mo;e.flags|=8192}}function Tu(e,l){l!==null&&(e.flags|=4),e.flags&16384&&(l=e.tag!==22?ms():536870912,e.lanes|=l,pa|=l)}function fn(e,l){if(!Z)switch(e.tailMode){case"hidden":l=e.tail;for(var t=null;l!==null;)l.alternate!==null&&(t=l),l=l.sibling;t===null?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?l||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function ne(e){var l=e.alternate!==null&&e.alternate.child===e.child,t=0,a=0;if(l)for(var n=e.child;n!==null;)t|=n.lanes|n.childLanes,a|=n.subtreeFlags&31457280,a|=n.flags&31457280,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)t|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=t,l}function hh(e,l,t){var a=l.pendingProps;switch(xi(l),l.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ne(l),null;case 1:return ne(l),null;case 3:return t=l.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),l.memoizedState.cache!==a&&(l.flags|=2048),Ml(ve),Vt(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),(e===null||e.child===null)&&(Va(l)?jl(l):e===null||e.memoizedState.isDehydrated&&!(l.flags&256)||(l.flags|=1024,ul!==null&&(Mc(ul),ul=null))),ne(l),null;case 26:return t=l.memoizedState,e===null?(jl(l),t!==null?(ne(l),Pr(l,t)):(ne(l),l.flags&=-16777217)):t?t!==e.memoizedState?(jl(l),ne(l),Pr(l,t)):(ne(l),l.flags&=-16777217):(e.memoizedProps!==a&&jl(l),ne(l),l.flags&=-16777217),null;case 27:_n(l),t=Gl.current;var n=l.type;if(e!==null&&l.stateNode!=null)e.memoizedProps!==a&&jl(l);else{if(!a){if(l.stateNode===null)throw Error(d(166));return ne(l),null}e=ol.current,Va(l)?ro(l):(e=Mf(n,a,t),l.stateNode=e,jl(l))}return ne(l),null;case 5:if(_n(l),t=l.type,e!==null&&l.stateNode!=null)e.memoizedProps!==a&&jl(l);else{if(!a){if(l.stateNode===null)throw Error(d(166));return ne(l),null}if(e=ol.current,Va(l))ro(l);else{switch(n=Ru(Gl.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",t);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;default:switch(t){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",t);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(t,{is:a.is}):n.createElement(t)}}e[Me]=l,e[Re]=a;e:for(n=l.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===l)break e;for(;n.sibling===null;){if(n.return===null||n.return===l)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}l.stateNode=e;e:switch(Ee(e,t,a),t){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&jl(l)}}return ne(l),l.flags&=-16777217,null;case 6:if(e&&l.stateNode!=null)e.memoizedProps!==a&&jl(l);else{if(typeof a!="string"&&l.stateNode===null)throw Error(d(166));if(e=Gl.current,Va(l)){if(e=l.stateNode,t=l.memoizedProps,a=null,n=Ce,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Me]=l,e=!!(e.nodeValue===t||a!==null&&a.suppressHydrationWarning===!0||Tf(e.nodeValue,t)),e||Nt(l)}else e=Ru(e).createTextNode(a),e[Me]=l,l.stateNode=e}return ne(l),null;case 13:if(a=l.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Va(l),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(d(318));if(n=l.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(d(317));n[Me]=l}else Qa(),!(l.flags&128)&&(l.memoizedState=null),l.flags|=4;ne(l),n=!1}else ul!==null&&(Mc(ul),ul=null),n=!0;if(!n)return l.flags&256?(Al(l),l):(Al(l),null)}if(Al(l),l.flags&128)return l.lanes=t,l;if(t=a!==null,e=e!==null&&e.memoizedState!==null,t){a=l.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return t!==e&&t&&(l.child.flags|=8192),Tu(l,l.updateQueue),ne(l),null;case 4:return Vt(),e===null&&qc(l.stateNode.containerInfo),ne(l),null;case 10:return Ml(l.type),ne(l),null;case 19:if(be(ye),n=l.memoizedState,n===null)return ne(l),null;if(a=(l.flags&128)!==0,u=n.rendering,u===null)if(a)fn(n,!1);else{if(ce!==0||e!==null&&e.flags&128)for(e=l.child;e!==null;){if(u=nu(e),u!==null){for(l.flags|=128,fn(n,!1),e=u.updateQueue,l.updateQueue=e,Tu(l,e),l.subtreeFlags=0,e=t,t=l.child;t!==null;)Jr(t,e),t=t.sibling;return ae(ye,ye.current&1|2),l.child}e=e.sibling}n.tail!==null&&rl()>xu&&(l.flags|=128,a=!0,fn(n,!1),l.lanes=4194304)}else{if(!a)if(e=nu(u),e!==null){if(l.flags|=128,a=!0,e=e.updateQueue,l.updateQueue=e,Tu(l,e),fn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!Z)return ne(l),null}else 2*rl()-n.renderingStartTime>xu&&t!==536870912&&(l.flags|=128,a=!0,fn(n,!1),l.lanes=4194304);n.isBackwards?(u.sibling=l.child,l.child=u):(e=n.last,e!==null?e.sibling=u:l.child=u,n.last=u)}return n.tail!==null?(l=n.tail,n.rendering=l,n.tail=l.sibling,n.renderingStartTime=rl(),l.sibling=null,e=ye.current,ae(ye,a?e&1|2:e&1),l):(ne(l),null);case 22:case 23:return Al(l),Ei(),a=l.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(l.flags|=8192):a&&(l.flags|=8192),a?t&536870912&&!(l.flags&128)&&(ne(l),l.subtreeFlags&6&&(l.flags|=8192)):ne(l),t=l.updateQueue,t!==null&&Tu(l,t.retryQueue),t=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),a=null,l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(a=l.memoizedState.cachePool.pool),a!==t&&(l.flags|=2048),e!==null&&be(Mt),null;case 24:return t=null,e!==null&&(t=e.memoizedState.cache),l.memoizedState.cache!==t&&(l.flags|=2048),Ml(ve),ne(l),null;case 25:return null}throw Error(d(156,l.tag))}function gh(e,l){switch(xi(l),l.tag){case 1:return e=l.flags,e&65536?(l.flags=e&-65537|128,l):null;case 3:return Ml(ve),Vt(),e=l.flags,e&65536&&!(e&128)?(l.flags=e&-65537|128,l):null;case 26:case 27:case 5:return _n(l),null;case 13:if(Al(l),e=l.memoizedState,e!==null&&e.dehydrated!==null){if(l.alternate===null)throw Error(d(340));Qa()}return e=l.flags,e&65536?(l.flags=e&-65537|128,l):null;case 19:return be(ye),null;case 4:return Vt(),null;case 10:return Ml(l.type),null;case 22:case 23:return Al(l),Ei(),e!==null&&be(Mt),e=l.flags,e&65536?(l.flags=e&-65537|128,l):null;case 24:return Ml(ve),null;case 25:return null;default:return null}}function $r(e,l){switch(xi(l),l.tag){case 3:Ml(ve),Vt();break;case 26:case 27:case 5:_n(l);break;case 4:Vt();break;case 13:Al(l);break;case 19:be(ye);break;case 10:Ml(l.type);break;case 22:case 23:Al(l),Ei(),e!==null&&be(Mt);break;case 24:Ml(ve)}}var ph={getCacheForType:function(e){var l=ze(ve),t=l.data.get(e);return t===void 0&&(t=e(),l.data.set(e,t)),t}},yh=typeof WeakMap=="function"?WeakMap:Map,ue=0,I=null,G=null,V=0,ee=0,Ye=null,Rl=!1,ga=!1,Sc=!1,_l=0,ce=0,tt=0,Lt=0,Tc=0,ll=0,pa=0,dn=null,hl=null,xc=!1,Ac=0,xu=1/0,Au=null,at=null,Du=!1,Bt=null,mn=0,Dc=0,Nc=null,hn=0,Ec=null;function Xe(){if(ue&2&&V!==0)return V&-V;if(R.T!==null){var e=ca;return e!==0?e:Uc()}return ys()}function Wr(){ll===0&&(ll=!(V&536870912)||Z?ds():536870912);var e=$e.current;return e!==null&&(e.flags|=32),ll}function Oe(e,l,t){(e===I&&ee===2||e.cancelPendingCommit!==null)&&(ya(e,0),Ul(e,V,ll,!1)),ja(e,t),(!(ue&2)||e!==I)&&(e===I&&(!(ue&2)&&(Lt|=t),ce===4&&Ul(e,V,ll,!1)),gl(e))}function Ir(e,l,t){if(ue&6)throw Error(d(327));var a=!t&&(l&60)===0&&(l&e.expiredLanes)===0||Oa(e,l),n=a?Sh(e,l):Oc(e,l,!0),u=a;do{if(n===0){ga&&!a&&Ul(e,l,0,!1);break}else if(n===6)Ul(e,l,0,!Rl);else{if(t=e.current.alternate,u&&!vh(t)){n=Oc(e,l,!1),u=!1;continue}if(n===2){if(u=l,e.errorRecoveryDisabledLanes&u)var i=0;else i=e.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){l=i;e:{var c=e;n=dn;var s=c.current.memoizedState.isDehydrated;if(s&&(ya(c,i).flags|=256),i=Oc(c,i,!1),i!==2){if(Sc&&!s){c.errorRecoveryDisabledLanes|=u,Lt|=u,n=4;break e}u=hl,hl=n,u!==null&&Mc(u)}n=i}if(u=!1,n!==2)continue}}if(n===1){ya(e,0),Ul(e,l,0,!0);break}e:{switch(a=e,n){case 0:case 1:throw Error(d(345));case 4:if((l&4194176)===l){Ul(a,l,ll,!Rl);break e}break;case 2:hl=null;break;case 3:case 5:break;default:throw Error(d(329))}if(a.finishedWork=t,a.finishedLanes=l,(l&62914560)===l&&(u=Ac+300-rl(),10<u)){if(Ul(a,l,ll,!Rl),Bn(a,0)!==0)break e;a.timeoutHandle=Df(ef.bind(null,a,t,hl,Au,xc,l,ll,Lt,pa,Rl,2,-0,0),u);break e}ef(a,t,hl,Au,xc,l,ll,Lt,pa,Rl,0,-0,0)}}break}while(!0);gl(e)}function Mc(e){hl===null?hl=e:hl.push.apply(hl,e)}function ef(e,l,t,a,n,u,i,c,s,f,b,T,p){var v=l.subtreeFlags;if((v&8192||(v&16785408)===16785408)&&(Sn={stylesheets:null,count:0,unsuspend:Ih},Qr(l),l=lg(),l!==null)){e.cancelPendingCommit=l(sf.bind(null,e,t,a,n,i,c,s,1,T,p)),Ul(e,u,i,!f);return}sf(e,t,a,n,i,c,s,b,T,p)}function vh(e){for(var l=e;;){var t=l.tag;if((t===0||t===11||t===15)&&l.flags&16384&&(t=l.updateQueue,t!==null&&(t=t.stores,t!==null)))for(var a=0;a<t.length;a++){var n=t[a],u=n.getSnapshot;n=n.value;try{if(!Be(u(),n))return!1}catch{return!1}}if(t=l.child,l.subtreeFlags&16384&&t!==null)t.return=l,l=t;else{if(l===e)break;for(;l.sibling===null;){if(l.return===null||l.return===e)return!0;l=l.return}l.sibling.return=l.return,l=l.sibling}}return!0}function Ul(e,l,t,a){l&=~Tc,l&=~Lt,e.suspendedLanes|=l,e.pingedLanes&=~l,a&&(e.warmLanes|=l),a=e.expirationTimes;for(var n=l;0<n;){var u=31-Le(n),i=1<<u;a[u]=-1,n&=~i}t!==0&&hs(e,t,l)}function Nu(){return ue&6?!0:(gn(0),!1)}function zc(){if(G!==null){if(ee===0)var e=G.return;else e=G,El=Rt=null,Ui(e),ua=null,Ja=0,e=G;for(;e!==null;)$r(e.alternate,e),e=e.return;G=null}}function ya(e,l){e.finishedWork=null,e.finishedLanes=0;var t=e.timeoutHandle;t!==-1&&(e.timeoutHandle=-1,Bh(t)),t=e.cancelPendingCommit,t!==null&&(e.cancelPendingCommit=null,t()),zc(),I=e,G=t=lt(e.current,null),V=l,ee=0,Ye=null,Rl=!1,ga=Oa(e,l),Sc=!1,pa=ll=Tc=Lt=tt=ce=0,hl=dn=null,xc=!1,l&8&&(l|=l&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=l;0<a;){var n=31-Le(a),u=1<<n;l|=e[n],a&=~u}return _l=l,Fn(),t}function lf(e,l){B=null,R.H=ml,l===Ka?(l=po(),ee=3):l===mo?(l=po(),ee=4):ee=l===hr?8:l!==null&&typeof l=="object"&&typeof l.then=="function"?6:1,Ye=l,G===null&&(ce=1,gu(e,Je(l,e.current)))}function tf(){var e=R.H;return R.H=ml,e===null?ml:e}function af(){var e=R.A;return R.A=ph,e}function Cc(){ce=4,Rl||(V&4194176)!==V&&$e.current!==null||(ga=!0),!(tt&134217727)&&!(Lt&134217727)||I===null||Ul(I,V,ll,!1)}function Oc(e,l,t){var a=ue;ue|=2;var n=tf(),u=af();(I!==e||V!==l)&&(Au=null,ya(e,l)),l=!1;var i=ce;e:do try{if(ee!==0&&G!==null){var c=G,s=Ye;switch(ee){case 8:zc(),i=6;break e;case 3:case 2:case 6:$e.current===null&&(l=!0);var f=ee;if(ee=0,Ye=null,va(e,c,s,f),t&&ga){i=0;break e}break;default:f=ee,ee=0,Ye=null,va(e,c,s,f)}}bh(),i=ce;break}catch(b){lf(e,b)}while(!0);return l&&e.shellSuspendCounter++,El=Rt=null,ue=a,R.H=n,R.A=u,G===null&&(I=null,V=0,Fn()),i}function bh(){for(;G!==null;)nf(G)}function Sh(e,l){var t=ue;ue|=2;var a=tf(),n=af();I!==e||V!==l?(Au=null,xu=rl()+500,ya(e,l)):ga=Oa(e,l);e:do try{if(ee!==0&&G!==null){l=G;var u=Ye;l:switch(ee){case 1:ee=0,Ye=null,va(e,l,u,1);break;case 2:if(ho(u)){ee=0,Ye=null,uf(l);break}l=function(){ee===2&&I===e&&(ee=7),gl(e)},u.then(l,l);break e;case 3:ee=7;break e;case 4:ee=5;break e;case 7:ho(u)?(ee=0,Ye=null,uf(l)):(ee=0,Ye=null,va(e,l,u,7));break;case 5:var i=null;switch(G.tag){case 26:i=G.memoizedState;case 5:case 27:var c=G;if(!i||Hf(i)){ee=0,Ye=null;var s=c.sibling;if(s!==null)G=s;else{var f=c.return;f!==null?(G=f,Eu(f)):G=null}break l}}ee=0,Ye=null,va(e,l,u,5);break;case 6:ee=0,Ye=null,va(e,l,u,6);break;case 8:zc(),ce=6;break e;default:throw Error(d(462))}}Th();break}catch(b){lf(e,b)}while(!0);return El=Rt=null,R.H=a,R.A=n,ue=t,G!==null?0:(I=null,V=0,Fn(),ce)}function Th(){for(;G!==null&&!Vd();)nf(G)}function nf(e){var l=Er(e.alternate,e,_l);e.memoizedProps=e.pendingProps,l===null?Eu(e):G=l}function uf(e){var l=e,t=l.alternate;switch(l.tag){case 15:case 0:l=Sr(t,l,l.pendingProps,l.type,void 0,V);break;case 11:l=Sr(t,l,l.pendingProps,l.type.render,l.ref,V);break;case 5:Ui(l);default:$r(t,l),l=G=Jr(l,_l),l=Er(t,l,_l)}e.memoizedProps=e.pendingProps,l===null?Eu(e):G=l}function va(e,l,t,a){El=Rt=null,Ui(l),ua=null,Ja=0;var n=l.return;try{if(oh(e,n,l,t,V)){ce=1,gu(e,Je(t,e.current)),G=null;return}}catch(u){if(n!==null)throw G=n,u;ce=1,gu(e,Je(t,e.current)),G=null;return}l.flags&32768?(Z||a===1?e=!0:ga||V&536870912?e=!1:(Rl=e=!0,(a===2||a===3||a===6)&&(a=$e.current,a!==null&&a.tag===13&&(a.flags|=16384))),cf(l,e)):Eu(l)}function Eu(e){var l=e;do{if(l.flags&32768){cf(l,Rl);return}e=l.return;var t=hh(l.alternate,l,_l);if(t!==null){G=t;return}if(l=l.sibling,l!==null){G=l;return}G=l=e}while(l!==null);ce===0&&(ce=5)}function cf(e,l){do{var t=gh(e.alternate,e);if(t!==null){t.flags&=32767,G=t;return}if(t=e.return,t!==null&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!l&&(e=e.sibling,e!==null)){G=e;return}G=e=t}while(e!==null);ce=6,G=null}function sf(e,l,t,a,n,u,i,c,s,f){var b=R.T,T=Q.p;try{Q.p=2,R.T=null,xh(e,l,t,a,T,n,u,i,c,s,f)}finally{R.T=b,Q.p=T}}function xh(e,l,t,a,n,u,i,c){do ba();while(Bt!==null);if(ue&6)throw Error(d(327));var s=e.finishedWork;if(a=e.finishedLanes,s===null)return null;if(e.finishedWork=null,e.finishedLanes=0,s===e.current)throw Error(d(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var f=s.lanes|s.childLanes;if(f|=bi,em(e,a,f,u,i,c),e===I&&(G=I=null,V=0),!(s.subtreeFlags&10256)&&!(s.flags&10256)||Du||(Du=!0,Dc=f,Nc=t,Eh(Un,function(){return ba(),null})),t=(s.flags&15990)!==0,s.subtreeFlags&15990||t?(t=R.T,R.T=null,u=Q.p,Q.p=2,i=ue,ue|=4,fh(e,s),Xr(s,e),Km(wc,e.containerInfo),qu=!!Xc,wc=Xc=null,e.current=s,Br(e,s.alternate,s),Qd(),ue=i,Q.p=u,R.T=t):e.current=s,Du?(Du=!1,Bt=e,mn=a):of(e,f),f=e.pendingLanes,f===0&&(at=null),Fd(s.stateNode),gl(e),l!==null)for(n=e.onRecoverableError,s=0;s<l.length;s++)f=l[s],n(f.value,{componentStack:f.stack});return mn&3&&ba(),f=e.pendingLanes,a&4194218&&f&42?e===Ec?hn++:(hn=0,Ec=e):hn=0,gn(0),null}function of(e,l){(e.pooledCacheLanes&=l)===0&&(l=e.pooledCache,l!=null&&(e.pooledCache=null,Pa(l)))}function ba(){if(Bt!==null){var e=Bt,l=Dc;Dc=0;var t=ps(mn),a=R.T,n=Q.p;try{if(Q.p=32>t?32:t,R.T=null,Bt===null)var u=!1;else{t=Nc,Nc=null;var i=Bt,c=mn;if(Bt=null,mn=0,ue&6)throw Error(d(331));var s=ue;if(ue|=4,Kr(i.current),Vr(i,i.current,c,t),ue=s,gn(0,!1),He&&typeof He.onPostCommitFiberRoot=="function")try{He.onPostCommitFiberRoot(Ca,i)}catch{}u=!0}return u}finally{Q.p=n,R.T=a,of(e,l)}}return!1}function rf(e,l,t){l=Je(t,l),l=Ji(e.stateNode,l,2),e=$l(e,l,2),e!==null&&(ja(e,2),gl(e))}function W(e,l,t){if(e.tag===3)rf(e,e,t);else for(;l!==null;){if(l.tag===3){rf(l,e,t);break}else if(l.tag===1){var a=l.stateNode;if(typeof l.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(at===null||!at.has(a))){e=Je(t,e),t=dr(2),a=$l(l,t,2),a!==null&&(mr(t,a,l,e),ja(a,2),gl(a));break}}l=l.return}}function jc(e,l,t){var a=e.pingCache;if(a===null){a=e.pingCache=new yh;var n=new Set;a.set(l,n)}else n=a.get(l),n===void 0&&(n=new Set,a.set(l,n));n.has(t)||(Sc=!0,n.add(t),e=Ah.bind(null,e,l,t),l.then(e,e))}function Ah(e,l,t){var a=e.pingCache;a!==null&&a.delete(l),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,I===e&&(V&t)===t&&(ce===4||ce===3&&(V&62914560)===V&&300>rl()-Ac?!(ue&2)&&ya(e,0):Tc|=t,pa===V&&(pa=0)),gl(e)}function ff(e,l){l===0&&(l=ms()),e=Vl(e,l),e!==null&&(ja(e,l),gl(e))}function Dh(e){var l=e.memoizedState,t=0;l!==null&&(t=l.retryLane),ff(e,t)}function Nh(e,l){var t=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(t=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(d(314))}a!==null&&a.delete(l),ff(e,t)}function Eh(e,l){return Fu(e,l)}var Mu=null,Sa=null,Rc=!1,zu=!1,_c=!1,qt=0;function gl(e){e!==Sa&&e.next===null&&(Sa===null?Mu=Sa=e:Sa=Sa.next=e),zu=!0,Rc||(Rc=!0,zh(Mh))}function gn(e,l){if(!_c&&zu){_c=!0;do for(var t=!1,a=Mu;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,c=a.pingedLanes;u=(1<<31-Le(42|e)+1)-1,u&=n&~(i&~c),u=u&201326677?u&201326677|1:u?u|2:0}u!==0&&(t=!0,hf(a,u))}else u=V,u=Bn(a,a===I?u:0),!(u&3)||Oa(a,u)||(t=!0,hf(a,u));a=a.next}while(t);_c=!1}}function Mh(){zu=Rc=!1;var e=0;qt!==0&&(Lh()&&(e=qt),qt=0);for(var l=rl(),t=null,a=Mu;a!==null;){var n=a.next,u=df(a,l);u===0?(a.next=null,t===null?Mu=n:t.next=n,n===null&&(Sa=t)):(t=a,(e!==0||u&3)&&(zu=!0)),a=n}gn(e)}function df(e,l){for(var t=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var i=31-Le(u),c=1<<i,s=n[i];s===-1?(!(c&t)||c&a)&&(n[i]=Id(c,l)):s<=l&&(e.expiredLanes|=c),u&=~c}if(l=I,t=V,t=Bn(e,e===l?t:0),a=e.callbackNode,t===0||e===l&&ee===2||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Pu(a),e.callbackNode=null,e.callbackPriority=0;if(!(t&3)||Oa(e,t)){if(l=t&-t,l===e.callbackPriority)return l;switch(a!==null&&Pu(a),ps(t)){case 2:case 8:t=rs;break;case 32:t=Un;break;case 268435456:t=fs;break;default:t=Un}return a=mf.bind(null,e),t=Fu(t,a),e.callbackPriority=l,e.callbackNode=t,l}return a!==null&&a!==null&&Pu(a),e.callbackPriority=2,e.callbackNode=null,2}function mf(e,l){var t=e.callbackNode;if(ba()&&e.callbackNode!==t)return null;var a=V;return a=Bn(e,e===I?a:0),a===0?null:(Ir(e,a,l),df(e,rl()),e.callbackNode!=null&&e.callbackNode===t?mf.bind(null,e):null)}function hf(e,l){if(ba())return null;Ir(e,l,!0)}function zh(e){qh(function(){ue&6?Fu(os,e):e()})}function Uc(){return qt===0&&(qt=ds()),qt}function gf(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:wn(""+e)}function pf(e,l){var t=l.ownerDocument.createElement("input");return t.name=l.name,t.value=l.value,e.id&&t.setAttribute("form",e.id),l.parentNode.insertBefore(t,l),e=new FormData(e),t.parentNode.removeChild(t),e}function Ch(e,l,t,a,n){if(l==="submit"&&t&&t.stateNode===n){var u=gf((n[Re]||null).action),i=a.submitter;i&&(l=(l=i[Re]||null)?gf(l.formAction):i.getAttribute("formAction"),l!==null&&(u=l,i=null));var c=new Kn("action","action",null,a,n);e.push({event:c,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(qt!==0){var s=i?pf(n,i):new FormData(n);Vi(t,{pending:!0,data:s,method:n.method,action:u},null,s)}}else typeof u=="function"&&(c.preventDefault(),s=i?pf(n,i):new FormData(n),Vi(t,{pending:!0,data:s,method:n.method,action:u},u,s))},currentTarget:n}]})}}for(var Hc=0;Hc<io.length;Hc++){var Lc=io[Hc],Oh=Lc.toLowerCase(),jh=Lc[0].toUpperCase()+Lc.slice(1);nl(Oh,"on"+jh)}nl(lo,"onAnimationEnd"),nl(to,"onAnimationIteration"),nl(ao,"onAnimationStart"),nl("dblclick","onDoubleClick"),nl("focusin","onFocus"),nl("focusout","onBlur"),nl(Jm,"onTransitionRun"),nl(Fm,"onTransitionStart"),nl(Pm,"onTransitionCancel"),nl(no,"onTransitionEnd"),kt("onMouseEnter",["mouseout","mouseover"]),kt("onMouseLeave",["mouseout","mouseover"]),kt("onPointerEnter",["pointerout","pointerover"]),kt("onPointerLeave",["pointerout","pointerover"]),bt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),bt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),bt("onBeforeInput",["compositionend","keypress","textInput","paste"]),bt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),bt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),bt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(pn));function yf(e,l){l=(l&4)!==0;for(var t=0;t<e.length;t++){var a=e[t],n=a.event;a=a.listeners;e:{var u=void 0;if(l)for(var i=a.length-1;0<=i;i--){var c=a[i],s=c.instance,f=c.currentTarget;if(c=c.listener,s!==u&&n.isPropagationStopped())break e;u=c,n.currentTarget=f;try{u(n)}catch(b){hu(b)}n.currentTarget=null,u=s}else for(i=0;i<a.length;i++){if(c=a[i],s=c.instance,f=c.currentTarget,c=c.listener,s!==u&&n.isPropagationStopped())break e;u=c,n.currentTarget=f;try{u(n)}catch(b){hu(b)}n.currentTarget=null,u=s}}}}function X(e,l){var t=l[Wu];t===void 0&&(t=l[Wu]=new Set);var a=e+"__bubble";t.has(a)||(vf(l,e,2,!1),t.add(a))}function Bc(e,l,t){var a=0;l&&(a|=4),vf(t,e,a,l)}var Cu="_reactListening"+Math.random().toString(36).slice(2);function qc(e){if(!e[Cu]){e[Cu]=!0,bs.forEach(function(t){t!=="selectionchange"&&(Rh.has(t)||Bc(t,!1,e),Bc(t,!0,e))});var l=e.nodeType===9?e:e.ownerDocument;l===null||l[Cu]||(l[Cu]=!0,Bc("selectionchange",!1,l))}}function vf(e,l,t,a){switch(Xf(l)){case 2:var n=ng;break;case 8:n=ug;break;default:n=$c}t=n.bind(null,l,t,e),n=void 0,!ii||l!=="touchstart"&&l!=="touchmove"&&l!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(l,t,{capture:!0,passive:n}):e.addEventListener(l,t,!0):n!==void 0?e.addEventListener(l,t,{passive:n}):e.addEventListener(l,t,!1)}function Gc(e,l,t,a,n){var u=a;if(!(l&1)&&!(l&2)&&a!==null)e:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var c=a.stateNode.containerInfo;if(c===n||c.nodeType===8&&c.parentNode===n)break;if(i===4)for(i=a.return;i!==null;){var s=i.tag;if((s===3||s===4)&&(s=i.stateNode.containerInfo,s===n||s.nodeType===8&&s.parentNode===n))return;i=i.return}for(;c!==null;){if(i=vt(c),i===null)return;if(s=i.tag,s===5||s===6||s===26||s===27){a=u=i;continue e}c=c.parentNode}}a=a.return}js(function(){var f=u,b=ni(t),T=[];e:{var p=uo.get(e);if(p!==void 0){var v=Kn,M=e;switch(e){case"keypress":if(Qn(t)===0)break e;case"keydown":case"keyup":v=Nm;break;case"focusin":M="focus",v=ri;break;case"focusout":M="blur",v=ri;break;case"beforeblur":case"afterblur":v=ri;break;case"click":if(t.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Us;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=mm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=zm;break;case lo:case to:case ao:v=pm;break;case no:v=Om;break;case"scroll":case"scrollend":v=fm;break;case"wheel":v=Rm;break;case"copy":case"cut":case"paste":v=vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Ls;break;case"toggle":case"beforetoggle":v=Um}var U=(l&4)!==0,se=!U&&(e==="scroll"||e==="scrollend"),m=U?p!==null?p+"Capture":null:p;U=[];for(var r=f,g;r!==null;){var S=r;if(g=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||g===null||m===null||(S=Ua(r,m),S!=null&&U.push(yn(r,S,g))),se)break;r=r.return}0<U.length&&(p=new v(p,M,null,t,b),T.push({event:p,listeners:U}))}}if(!(l&7)){e:{if(p=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",p&&t!==ai&&(M=t.relatedTarget||t.fromElement)&&(vt(M)||M[Qt]))break e;if((v||p)&&(p=b.window===b?b:(p=b.ownerDocument)?p.defaultView||p.parentWindow:window,v?(M=t.relatedTarget||t.toElement,v=f,M=M?vt(M):null,M!==null&&(se=_(M),U=M.tag,M!==se||U!==5&&U!==27&&U!==6)&&(M=null)):(v=null,M=f),v!==M)){if(U=Us,S="onMouseLeave",m="onMouseEnter",r="mouse",(e==="pointerout"||e==="pointerover")&&(U=Ls,S="onPointerLeave",m="onPointerEnter",r="pointer"),se=v==null?p:_a(v),g=M==null?p:_a(M),p=new U(S,r+"leave",v,t,b),p.target=se,p.relatedTarget=g,S=null,vt(b)===f&&(U=new U(m,r+"enter",M,t,b),U.target=g,U.relatedTarget=se,S=U),se=S,v&&M)l:{for(U=v,m=M,r=0,g=U;g;g=Ta(g))r++;for(g=0,S=m;S;S=Ta(S))g++;for(;0<r-g;)U=Ta(U),r--;for(;0<g-r;)m=Ta(m),g--;for(;r--;){if(U===m||m!==null&&U===m.alternate)break l;U=Ta(U),m=Ta(m)}U=null}else U=null;v!==null&&bf(T,p,v,U,!1),M!==null&&se!==null&&bf(T,se,M,U,!0)}}e:{if(p=f?_a(f):window,v=p.nodeName&&p.nodeName.toLowerCase(),v==="select"||v==="input"&&p.type==="file")var D=Qs;else if(ws(p))if(Zs)D=Qm;else{D=wm;var q=Xm}else v=p.nodeName,!v||v.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?f&&ti(f.elementType)&&(D=Qs):D=Vm;if(D&&(D=D(e,f))){Vs(T,D,t,b);break e}q&&q(e,p,f),e==="focusout"&&f&&p.type==="number"&&f.memoizedProps.value!=null&&li(p,"number",p.value)}switch(q=f?_a(f):window,e){case"focusin":(ws(q)||q.contentEditable==="true")&&(It=q,pi=f,wa=null);break;case"focusout":wa=pi=It=null;break;case"mousedown":yi=!0;break;case"contextmenu":case"mouseup":case"dragend":yi=!1,Is(T,t,b);break;case"selectionchange":if(km)break;case"keydown":case"keyup":Is(T,t,b)}var z;if(di)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else Wt?Ys(e,t)&&(O="onCompositionEnd"):e==="keydown"&&t.keyCode===229&&(O="onCompositionStart");O&&(Bs&&t.locale!=="ko"&&(Wt||O!=="onCompositionStart"?O==="onCompositionEnd"&&Wt&&(z=Rs()):(wl=b,ci="value"in wl?wl.value:wl.textContent,Wt=!0)),q=Ou(f,O),0<q.length&&(O=new Hs(O,e,null,t,b),T.push({event:O,listeners:q}),z?O.data=z:(z=Xs(t),z!==null&&(O.data=z)))),(z=Lm?Bm(e,t):qm(e,t))&&(O=Ou(f,"onBeforeInput"),0<O.length&&(q=new Hs("onBeforeInput","beforeinput",null,t,b),T.push({event:q,listeners:O}),q.data=z)),Ch(T,e,f,t,b)}yf(T,l)})}function yn(e,l,t){return{instance:e,listener:l,currentTarget:t}}function Ou(e,l){for(var t=l+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ua(e,t),n!=null&&a.unshift(yn(e,n,u)),n=Ua(e,l),n!=null&&a.push(yn(e,n,u))),e=e.return}return a}function Ta(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function bf(e,l,t,a,n){for(var u=l._reactName,i=[];t!==null&&t!==a;){var c=t,s=c.alternate,f=c.stateNode;if(c=c.tag,s!==null&&s===a)break;c!==5&&c!==26&&c!==27||f===null||(s=f,n?(f=Ua(t,u),f!=null&&i.unshift(yn(t,f,s))):n||(f=Ua(t,u),f!=null&&i.push(yn(t,f,s)))),t=t.return}i.length!==0&&e.push({event:l,listeners:i})}var _h=/\r\n?/g,Uh=/\u0000|\uFFFD/g;function Sf(e){return(typeof e=="string"?e:""+e).replace(_h,`
`).replace(Uh,"")}function Tf(e,l){return l=Sf(l),Sf(e)===l}function ju(){}function F(e,l,t,a,n,u){switch(t){case"children":typeof a=="string"?l==="body"||l==="textarea"&&a===""||Ft(e,a):(typeof a=="number"||typeof a=="bigint")&&l!=="body"&&Ft(e,""+a);break;case"className":Gn(e,"class",a);break;case"tabIndex":Gn(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Gn(e,t,a);break;case"style":Cs(e,a,u);break;case"data":if(l!=="object"){Gn(e,"data",a);break}case"src":case"href":if(a===""&&(l!=="a"||t!=="href")){e.removeAttribute(t);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(t);break}a=wn(""+a),e.setAttribute(t,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(t,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(t==="formAction"?(l!=="input"&&F(e,l,"name",n.name,n,null),F(e,l,"formEncType",n.formEncType,n,null),F(e,l,"formMethod",n.formMethod,n,null),F(e,l,"formTarget",n.formTarget,n,null)):(F(e,l,"encType",n.encType,n,null),F(e,l,"method",n.method,n,null),F(e,l,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(t);break}a=wn(""+a),e.setAttribute(t,a);break;case"onClick":a!=null&&(e.onclick=ju);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(d(61));if(t=a.__html,t!=null){if(n.children!=null)throw Error(d(60));e.innerHTML=t}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}t=wn(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(t,""+a):e.removeAttribute(t);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(t,""):e.removeAttribute(t);break;case"capture":case"download":a===!0?e.setAttribute(t,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(t,a):e.removeAttribute(t);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(t,a):e.removeAttribute(t);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(t):e.setAttribute(t,a);break;case"popover":X("beforetoggle",e),X("toggle",e),qn(e,"popover",a);break;case"xlinkActuate":Sl(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Sl(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Sl(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Sl(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Sl(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Sl(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Sl(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Sl(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Sl(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":qn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(t=om.get(t)||t,qn(e,t,a))}}function Yc(e,l,t,a,n,u){switch(t){case"style":Cs(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(d(61));if(t=a.__html,t!=null){if(n.children!=null)throw Error(d(60));e.innerHTML=t}}break;case"children":typeof a=="string"?Ft(e,a):(typeof a=="number"||typeof a=="bigint")&&Ft(e,""+a);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"onClick":a!=null&&(e.onclick=ju);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ss.hasOwnProperty(t))e:{if(t[0]==="o"&&t[1]==="n"&&(n=t.endsWith("Capture"),l=t.slice(2,n?t.length-7:void 0),u=e[Re]||null,u=u!=null?u[t]:null,typeof u=="function"&&e.removeEventListener(l,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(t in e?e[t]=null:e.hasAttribute(t)&&e.removeAttribute(t)),e.addEventListener(l,a,n);break e}t in e?e[t]=a:a===!0?e.setAttribute(t,""):qn(e,t,a)}}}function Ee(e,l,t){switch(l){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":X("error",e),X("load",e);var a=!1,n=!1,u;for(u in t)if(t.hasOwnProperty(u)){var i=t[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(d(137,l));default:F(e,l,u,i,t,null)}}n&&F(e,l,"srcSet",t.srcSet,t,null),a&&F(e,l,"src",t.src,t,null);return;case"input":X("invalid",e);var c=u=i=n=null,s=null,f=null;for(a in t)if(t.hasOwnProperty(a)){var b=t[a];if(b!=null)switch(a){case"name":n=b;break;case"type":i=b;break;case"checked":s=b;break;case"defaultChecked":f=b;break;case"value":u=b;break;case"defaultValue":c=b;break;case"children":case"dangerouslySetInnerHTML":if(b!=null)throw Error(d(137,l));break;default:F(e,l,a,b,t,null)}}Ns(e,u,c,s,f,i,n,!1),Yn(e);return;case"select":X("invalid",e),a=i=u=null;for(n in t)if(t.hasOwnProperty(n)&&(c=t[n],c!=null))switch(n){case"value":u=c;break;case"defaultValue":i=c;break;case"multiple":a=c;default:F(e,l,n,c,t,null)}l=u,t=i,e.multiple=!!a,l!=null?Jt(e,!!a,l,!1):t!=null&&Jt(e,!!a,t,!0);return;case"textarea":X("invalid",e),u=n=a=null;for(i in t)if(t.hasOwnProperty(i)&&(c=t[i],c!=null))switch(i){case"value":a=c;break;case"defaultValue":n=c;break;case"children":u=c;break;case"dangerouslySetInnerHTML":if(c!=null)throw Error(d(91));break;default:F(e,l,i,c,t,null)}Ms(e,a,n,u),Yn(e);return;case"option":for(s in t)if(t.hasOwnProperty(s)&&(a=t[s],a!=null))switch(s){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:F(e,l,s,a,t,null)}return;case"dialog":X("cancel",e),X("close",e);break;case"iframe":case"object":X("load",e);break;case"video":case"audio":for(a=0;a<pn.length;a++)X(pn[a],e);break;case"image":X("error",e),X("load",e);break;case"details":X("toggle",e);break;case"embed":case"source":case"link":X("error",e),X("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(f in t)if(t.hasOwnProperty(f)&&(a=t[f],a!=null))switch(f){case"children":case"dangerouslySetInnerHTML":throw Error(d(137,l));default:F(e,l,f,a,t,null)}return;default:if(ti(l)){for(b in t)t.hasOwnProperty(b)&&(a=t[b],a!==void 0&&Yc(e,l,b,a,t,void 0));return}}for(c in t)t.hasOwnProperty(c)&&(a=t[c],a!=null&&F(e,l,c,a,t,null))}function Hh(e,l,t,a){switch(l){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,c=null,s=null,f=null,b=null;for(v in t){var T=t[v];if(t.hasOwnProperty(v)&&T!=null)switch(v){case"checked":break;case"value":break;case"defaultValue":s=T;default:a.hasOwnProperty(v)||F(e,l,v,null,a,T)}}for(var p in a){var v=a[p];if(T=t[p],a.hasOwnProperty(p)&&(v!=null||T!=null))switch(p){case"type":u=v;break;case"name":n=v;break;case"checked":f=v;break;case"defaultChecked":b=v;break;case"value":i=v;break;case"defaultValue":c=v;break;case"children":case"dangerouslySetInnerHTML":if(v!=null)throw Error(d(137,l));break;default:v!==T&&F(e,l,p,v,a,T)}}ei(e,i,c,s,f,b,u,n);return;case"select":v=i=c=p=null;for(u in t)if(s=t[u],t.hasOwnProperty(u)&&s!=null)switch(u){case"value":break;case"multiple":v=s;default:a.hasOwnProperty(u)||F(e,l,u,null,a,s)}for(n in a)if(u=a[n],s=t[n],a.hasOwnProperty(n)&&(u!=null||s!=null))switch(n){case"value":p=u;break;case"defaultValue":c=u;break;case"multiple":i=u;default:u!==s&&F(e,l,n,u,a,s)}l=c,t=i,a=v,p!=null?Jt(e,!!t,p,!1):!!a!=!!t&&(l!=null?Jt(e,!!t,l,!0):Jt(e,!!t,t?[]:"",!1));return;case"textarea":v=p=null;for(c in t)if(n=t[c],t.hasOwnProperty(c)&&n!=null&&!a.hasOwnProperty(c))switch(c){case"value":break;case"children":break;default:F(e,l,c,null,a,n)}for(i in a)if(n=a[i],u=t[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":p=n;break;case"defaultValue":v=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(d(91));break;default:n!==u&&F(e,l,i,n,a,u)}Es(e,p,v);return;case"option":for(var M in t)if(p=t[M],t.hasOwnProperty(M)&&p!=null&&!a.hasOwnProperty(M))switch(M){case"selected":e.selected=!1;break;default:F(e,l,M,null,a,p)}for(s in a)if(p=a[s],v=t[s],a.hasOwnProperty(s)&&p!==v&&(p!=null||v!=null))switch(s){case"selected":e.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:F(e,l,s,p,a,v)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var U in t)p=t[U],t.hasOwnProperty(U)&&p!=null&&!a.hasOwnProperty(U)&&F(e,l,U,null,a,p);for(f in a)if(p=a[f],v=t[f],a.hasOwnProperty(f)&&p!==v&&(p!=null||v!=null))switch(f){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(d(137,l));break;default:F(e,l,f,p,a,v)}return;default:if(ti(l)){for(var se in t)p=t[se],t.hasOwnProperty(se)&&p!==void 0&&!a.hasOwnProperty(se)&&Yc(e,l,se,void 0,a,p);for(b in a)p=a[b],v=t[b],!a.hasOwnProperty(b)||p===v||p===void 0&&v===void 0||Yc(e,l,b,p,a,v);return}}for(var m in t)p=t[m],t.hasOwnProperty(m)&&p!=null&&!a.hasOwnProperty(m)&&F(e,l,m,null,a,p);for(T in a)p=a[T],v=t[T],!a.hasOwnProperty(T)||p===v||p==null&&v==null||F(e,l,T,p,a,v)}var Xc=null,wc=null;function Ru(e){return e.nodeType===9?e:e.ownerDocument}function xf(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Af(e,l){if(e===0)switch(l){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&l==="foreignObject"?0:e}function Vc(e,l){return e==="textarea"||e==="noscript"||typeof l.children=="string"||typeof l.children=="number"||typeof l.children=="bigint"||typeof l.dangerouslySetInnerHTML=="object"&&l.dangerouslySetInnerHTML!==null&&l.dangerouslySetInnerHTML.__html!=null}var Qc=null;function Lh(){var e=window.event;return e&&e.type==="popstate"?e===Qc?!1:(Qc=e,!0):(Qc=null,!1)}var Df=typeof setTimeout=="function"?setTimeout:void 0,Bh=typeof clearTimeout=="function"?clearTimeout:void 0,Nf=typeof Promise=="function"?Promise:void 0,qh=typeof queueMicrotask=="function"?queueMicrotask:typeof Nf<"u"?function(e){return Nf.resolve(null).then(e).catch(Gh)}:Df;function Gh(e){setTimeout(function(){throw e})}function Zc(e,l){var t=l,a=0;do{var n=t.nextSibling;if(e.removeChild(t),n&&n.nodeType===8)if(t=n.data,t==="/$"){if(a===0){e.removeChild(n),Nn(l);return}a--}else t!=="$"&&t!=="$?"&&t!=="$!"||a++;t=n}while(t);Nn(l)}function Kc(e){var l=e.firstChild;for(l&&l.nodeType===10&&(l=l.nextSibling);l;){var t=l;switch(l=l.nextSibling,t.nodeName){case"HTML":case"HEAD":case"BODY":Kc(t),Iu(t);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(t.rel.toLowerCase()==="stylesheet")continue}e.removeChild(t)}}function Yh(e,l,t,a){for(;e.nodeType===1;){var n=t;if(e.nodeName.toLowerCase()!==l.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ra])switch(l){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(l==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=cl(e.nextSibling),e===null)break}return null}function Xh(e,l,t){if(l==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!t||(e=cl(e.nextSibling),e===null))return null;return e}function cl(e){for(;e!=null;e=e.nextSibling){var l=e.nodeType;if(l===1||l===3)break;if(l===8){if(l=e.data,l==="$"||l==="$!"||l==="$?"||l==="F!"||l==="F")break;if(l==="/$")return null}}return e}function Ef(e){e=e.previousSibling;for(var l=0;e;){if(e.nodeType===8){var t=e.data;if(t==="$"||t==="$!"||t==="$?"){if(l===0)return e;l--}else t==="/$"&&l++}e=e.previousSibling}return null}function Mf(e,l,t){switch(l=Ru(t),e){case"html":if(e=l.documentElement,!e)throw Error(d(452));return e;case"head":if(e=l.head,!e)throw Error(d(453));return e;case"body":if(e=l.body,!e)throw Error(d(454));return e;default:throw Error(d(451))}}var tl=new Map,zf=new Set;function _u(e){return typeof e.getRootNode=="function"?e.getRootNode():e.ownerDocument}var Hl=Q.d;Q.d={f:wh,r:Vh,D:Qh,C:Zh,L:Kh,m:kh,X:Fh,S:Jh,M:Ph};function wh(){var e=Hl.f(),l=Nu();return e||l}function Vh(e){var l=Zt(e);l!==null&&l.tag===5&&l.type==="form"?er(l):Hl.r(e)}var xa=typeof document>"u"?null:document;function Cf(e,l,t){var a=xa;if(a&&typeof l=="string"&&l){var n=Ke(l);n='link[rel="'+e+'"][href="'+n+'"]',typeof t=="string"&&(n+='[crossorigin="'+t+'"]'),zf.has(n)||(zf.add(n),e={rel:e,crossOrigin:t,href:l},a.querySelector(n)===null&&(l=a.createElement("link"),Ee(l,"link",e),Se(l),a.head.appendChild(l)))}}function Qh(e){Hl.D(e),Cf("dns-prefetch",e,null)}function Zh(e,l){Hl.C(e,l),Cf("preconnect",e,l)}function Kh(e,l,t){Hl.L(e,l,t);var a=xa;if(a&&e&&l){var n='link[rel="preload"][as="'+Ke(l)+'"]';l==="image"&&t&&t.imageSrcSet?(n+='[imagesrcset="'+Ke(t.imageSrcSet)+'"]',typeof t.imageSizes=="string"&&(n+='[imagesizes="'+Ke(t.imageSizes)+'"]')):n+='[href="'+Ke(e)+'"]';var u=n;switch(l){case"style":u=Aa(e);break;case"script":u=Da(e)}tl.has(u)||(e=k({rel:"preload",href:l==="image"&&t&&t.imageSrcSet?void 0:e,as:l},t),tl.set(u,e),a.querySelector(n)!==null||l==="style"&&a.querySelector(vn(u))||l==="script"&&a.querySelector(bn(u))||(l=a.createElement("link"),Ee(l,"link",e),Se(l),a.head.appendChild(l)))}}function kh(e,l){Hl.m(e,l);var t=xa;if(t&&e){var a=l&&typeof l.as=="string"?l.as:"script",n='link[rel="modulepreload"][as="'+Ke(a)+'"][href="'+Ke(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Da(e)}if(!tl.has(u)&&(e=k({rel:"modulepreload",href:e},l),tl.set(u,e),t.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(t.querySelector(bn(u)))return}a=t.createElement("link"),Ee(a,"link",e),Se(a),t.head.appendChild(a)}}}function Jh(e,l,t){Hl.S(e,l,t);var a=xa;if(a&&e){var n=Kt(a).hoistableStyles,u=Aa(e);l=l||"default";var i=n.get(u);if(!i){var c={loading:0,preload:null};if(i=a.querySelector(vn(u)))c.loading=5;else{e=k({rel:"stylesheet",href:e,"data-precedence":l},t),(t=tl.get(u))&&kc(e,t);var s=i=a.createElement("link");Se(s),Ee(s,"link",e),s._p=new Promise(function(f,b){s.onload=f,s.onerror=b}),s.addEventListener("load",function(){c.loading|=1}),s.addEventListener("error",function(){c.loading|=2}),c.loading|=4,Uu(i,l,a)}i={type:"stylesheet",instance:i,count:1,state:c},n.set(u,i)}}}function Fh(e,l){Hl.X(e,l);var t=xa;if(t&&e){var a=Kt(t).hoistableScripts,n=Da(e),u=a.get(n);u||(u=t.querySelector(bn(n)),u||(e=k({src:e,async:!0},l),(l=tl.get(n))&&Jc(e,l),u=t.createElement("script"),Se(u),Ee(u,"link",e),t.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Ph(e,l){Hl.M(e,l);var t=xa;if(t&&e){var a=Kt(t).hoistableScripts,n=Da(e),u=a.get(n);u||(u=t.querySelector(bn(n)),u||(e=k({src:e,async:!0,type:"module"},l),(l=tl.get(n))&&Jc(e,l),u=t.createElement("script"),Se(u),Ee(u,"link",e),t.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Of(e,l,t,a){var n=(n=Gl.current)?_u(n):null;if(!n)throw Error(d(446));switch(e){case"meta":case"title":return null;case"style":return typeof t.precedence=="string"&&typeof t.href=="string"?(l=Aa(t.href),t=Kt(n).hoistableStyles,a=t.get(l),a||(a={type:"style",instance:null,count:0,state:null},t.set(l,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(t.rel==="stylesheet"&&typeof t.href=="string"&&typeof t.precedence=="string"){e=Aa(t.href);var u=Kt(n).hoistableStyles,i=u.get(e);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,i),(u=n.querySelector(vn(e)))&&!u._p&&(i.instance=u,i.state.loading=5),tl.has(e)||(t={rel:"preload",as:"style",href:t.href,crossOrigin:t.crossOrigin,integrity:t.integrity,media:t.media,hrefLang:t.hrefLang,referrerPolicy:t.referrerPolicy},tl.set(e,t),u||$h(n,e,t,i.state))),l&&a===null)throw Error(d(528,""));return i}if(l&&a!==null)throw Error(d(529,""));return null;case"script":return l=t.async,t=t.src,typeof t=="string"&&l&&typeof l!="function"&&typeof l!="symbol"?(l=Da(t),t=Kt(n).hoistableScripts,a=t.get(l),a||(a={type:"script",instance:null,count:0,state:null},t.set(l,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(d(444,e))}}function Aa(e){return'href="'+Ke(e)+'"'}function vn(e){return'link[rel="stylesheet"]['+e+"]"}function jf(e){return k({},e,{"data-precedence":e.precedence,precedence:null})}function $h(e,l,t,a){e.querySelector('link[rel="preload"][as="style"]['+l+"]")?a.loading=1:(l=e.createElement("link"),a.preload=l,l.addEventListener("load",function(){return a.loading|=1}),l.addEventListener("error",function(){return a.loading|=2}),Ee(l,"link",t),Se(l),e.head.appendChild(l))}function Da(e){return'[src="'+Ke(e)+'"]'}function bn(e){return"script[async]"+e}function Rf(e,l,t){if(l.count++,l.instance===null)switch(l.type){case"style":var a=e.querySelector('style[data-href~="'+Ke(t.href)+'"]');if(a)return l.instance=a,Se(a),a;var n=k({},t,{"data-href":t.href,"data-precedence":t.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Se(a),Ee(a,"style",n),Uu(a,t.precedence,e),l.instance=a;case"stylesheet":n=Aa(t.href);var u=e.querySelector(vn(n));if(u)return l.state.loading|=4,l.instance=u,Se(u),u;a=jf(t),(n=tl.get(n))&&kc(a,n),u=(e.ownerDocument||e).createElement("link"),Se(u);var i=u;return i._p=new Promise(function(c,s){i.onload=c,i.onerror=s}),Ee(u,"link",a),l.state.loading|=4,Uu(u,t.precedence,e),l.instance=u;case"script":return u=Da(t.src),(n=e.querySelector(bn(u)))?(l.instance=n,Se(n),n):(a=t,(n=tl.get(u))&&(a=k({},t),Jc(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Se(n),Ee(n,"link",a),e.head.appendChild(n),l.instance=n);case"void":return null;default:throw Error(d(443,l.type))}else l.type==="stylesheet"&&!(l.state.loading&4)&&(a=l.instance,l.state.loading|=4,Uu(a,t.precedence,e));return l.instance}function Uu(e,l,t){for(var a=t.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var c=a[i];if(c.dataset.precedence===l)u=c;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(l=t.nodeType===9?t.head:t,l.insertBefore(e,l.firstChild))}function kc(e,l){e.crossOrigin==null&&(e.crossOrigin=l.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=l.referrerPolicy),e.title==null&&(e.title=l.title)}function Jc(e,l){e.crossOrigin==null&&(e.crossOrigin=l.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=l.referrerPolicy),e.integrity==null&&(e.integrity=l.integrity)}var Hu=null;function _f(e,l,t){if(Hu===null){var a=new Map,n=Hu=new Map;n.set(t,a)}else n=Hu,a=n.get(t),a||(a=new Map,n.set(t,a));if(a.has(e))return a;for(a.set(e,null),t=t.getElementsByTagName(e),n=0;n<t.length;n++){var u=t[n];if(!(u[Ra]||u[Me]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(l)||"";i=e+i;var c=a.get(i);c?c.push(u):a.set(i,[u])}}return a}function Uf(e,l,t){e=e.ownerDocument||e,e.head.insertBefore(t,l==="title"?e.querySelector("head > title"):null)}function Wh(e,l,t){if(t===1||l.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof l.precedence!="string"||typeof l.href!="string"||l.href==="")break;return!0;case"link":if(typeof l.rel!="string"||typeof l.href!="string"||l.href===""||l.onLoad||l.onError)break;switch(l.rel){case"stylesheet":return e=l.disabled,typeof l.precedence=="string"&&e==null;default:return!0}case"script":if(l.async&&typeof l.async!="function"&&typeof l.async!="symbol"&&!l.onLoad&&!l.onError&&l.src&&typeof l.src=="string")return!0}return!1}function Hf(e){return!(e.type==="stylesheet"&&!(e.state.loading&3))}var Sn=null;function Ih(){}function eg(e,l,t){if(Sn===null)throw Error(d(475));var a=Sn;if(l.type==="stylesheet"&&(typeof t.media!="string"||matchMedia(t.media).matches!==!1)&&!(l.state.loading&4)){if(l.instance===null){var n=Aa(t.href),u=e.querySelector(vn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Lu.bind(a),e.then(a,a)),l.state.loading|=4,l.instance=u,Se(u);return}u=e.ownerDocument||e,t=jf(t),(n=tl.get(n))&&kc(t,n),u=u.createElement("link"),Se(u);var i=u;i._p=new Promise(function(c,s){i.onload=c,i.onerror=s}),Ee(u,"link",t),l.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(l,e),(e=l.state.preload)&&!(l.state.loading&3)&&(a.count++,l=Lu.bind(a),e.addEventListener("load",l),e.addEventListener("error",l))}}function lg(){if(Sn===null)throw Error(d(475));var e=Sn;return e.stylesheets&&e.count===0&&Fc(e,e.stylesheets),0<e.count?function(l){var t=setTimeout(function(){if(e.stylesheets&&Fc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=l,function(){e.unsuspend=null,clearTimeout(t)}}:null}function Lu(){if(this.count--,this.count===0){if(this.stylesheets)Fc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Bu=null;function Fc(e,l){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Bu=new Map,l.forEach(tg,e),Bu=null,Lu.call(e))}function tg(e,l){if(!(l.state.loading&4)){var t=Bu.get(e);if(t)var a=t.get(null);else{t=new Map,Bu.set(e,t);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(t.set(i.dataset.precedence,i),a=i)}a&&t.set(null,a)}n=l.instance,i=n.getAttribute("data-precedence"),u=t.get(i)||a,u===a&&t.set(null,n),t.set(i,n),this.count++,a=Lu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),l.state.loading|=4}}var Tn={$$typeof:pe,Provider:null,Consumer:null,_currentValue:Qe,_currentValue2:Qe,_threadCount:0};function ag(e,l,t,a,n,u,i,c){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=$u(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$u(0),this.hiddenUpdates=$u(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=c,this.incompleteTransitions=new Map}function Lf(e,l,t,a,n,u,i,c,s,f,b,T){return e=new ag(e,l,t,i,c,s,f,T),l=1,u===!0&&(l|=24),u=el(3,null,null,l),e.current=u,u.stateNode=e,l=Mi(),l.refCount++,e.pooledCache=l,l.refCount++,u.memoizedState={element:a,isDehydrated:t,cache:l},cc(u),e}function Bf(e){return e?(e=ta,e):ta}function qf(e,l,t,a,n,u){n=Bf(n),a.context===null?a.context=n:a.pendingContext=n,a=Pl(l),a.payload={element:t},u=u===void 0?null:u,u!==null&&(a.callback=u),t=$l(e,a,l),t!==null&&(Oe(t,e,l),an(t,e,l))}function Gf(e,l){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var t=e.retryLane;e.retryLane=t!==0&&t<l?t:l}}function Pc(e,l){Gf(e,l),(e=e.alternate)&&Gf(e,l)}function Yf(e){if(e.tag===13){var l=Vl(e,67108864);l!==null&&Oe(l,e,67108864),Pc(e,67108864)}}var qu=!0;function ng(e,l,t,a){var n=R.T;R.T=null;var u=Q.p;try{Q.p=2,$c(e,l,t,a)}finally{Q.p=u,R.T=n}}function ug(e,l,t,a){var n=R.T;R.T=null;var u=Q.p;try{Q.p=8,$c(e,l,t,a)}finally{Q.p=u,R.T=n}}function $c(e,l,t,a){if(qu){var n=Wc(a);if(n===null)Gc(e,l,a,Gu,t),wf(e,a);else if(cg(n,e,l,t,a))a.stopPropagation();else if(wf(e,a),l&4&&-1<ig.indexOf(e)){for(;n!==null;){var u=Zt(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=yt(u.pendingLanes);if(i!==0){var c=u;for(c.pendingLanes|=2,c.entangledLanes|=2;i;){var s=1<<31-Le(i);c.entanglements[1]|=s,i&=~s}gl(u),!(ue&6)&&(xu=rl()+500,gn(0))}}break;case 13:c=Vl(u,2),c!==null&&Oe(c,u,2),Nu(),Pc(u,2)}if(u=Wc(a),u===null&&Gc(e,l,a,Gu,t),u===n)break;n=u}n!==null&&a.stopPropagation()}else Gc(e,l,a,null,t)}}function Wc(e){return e=ni(e),Ic(e)}var Gu=null;function Ic(e){if(Gu=null,e=vt(e),e!==null){var l=_(e);if(l===null)e=null;else{var t=l.tag;if(t===13){if(e=te(l),e!==null)return e;e=null}else if(t===3){if(l.stateNode.current.memoizedState.isDehydrated)return l.tag===3?l.stateNode.containerInfo:null;e=null}else l!==e&&(e=null)}}return Gu=e,null}function Xf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Zd()){case os:return 2;case rs:return 8;case Un:case Kd:return 32;case fs:return 268435456;default:return 32}default:return 32}}var es=!1,nt=null,ut=null,it=null,xn=new Map,An=new Map,ct=[],ig="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,l){switch(e){case"focusin":case"focusout":nt=null;break;case"dragenter":case"dragleave":ut=null;break;case"mouseover":case"mouseout":it=null;break;case"pointerover":case"pointerout":xn.delete(l.pointerId);break;case"gotpointercapture":case"lostpointercapture":An.delete(l.pointerId)}}function Dn(e,l,t,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:l,domEventName:t,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},l!==null&&(l=Zt(l),l!==null&&Yf(l)),e):(e.eventSystemFlags|=a,l=e.targetContainers,n!==null&&l.indexOf(n)===-1&&l.push(n),e)}function cg(e,l,t,a,n){switch(l){case"focusin":return nt=Dn(nt,e,l,t,a,n),!0;case"dragenter":return ut=Dn(ut,e,l,t,a,n),!0;case"mouseover":return it=Dn(it,e,l,t,a,n),!0;case"pointerover":var u=n.pointerId;return xn.set(u,Dn(xn.get(u)||null,e,l,t,a,n)),!0;case"gotpointercapture":return u=n.pointerId,An.set(u,Dn(An.get(u)||null,e,l,t,a,n)),!0}return!1}function Vf(e){var l=vt(e.target);if(l!==null){var t=_(l);if(t!==null){if(l=t.tag,l===13){if(l=te(t),l!==null){e.blockedOn=l,lm(e.priority,function(){if(t.tag===13){var a=Xe(),n=Vl(t,a);n!==null&&Oe(n,t,a),Pc(t,a)}});return}}else if(l===3&&t.stateNode.current.memoizedState.isDehydrated){e.blockedOn=t.tag===3?t.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yu(e){if(e.blockedOn!==null)return!1;for(var l=e.targetContainers;0<l.length;){var t=Wc(e.nativeEvent);if(t===null){t=e.nativeEvent;var a=new t.constructor(t.type,t);ai=a,t.target.dispatchEvent(a),ai=null}else return l=Zt(t),l!==null&&Yf(l),e.blockedOn=t,!1;l.shift()}return!0}function Qf(e,l,t){Yu(e)&&t.delete(l)}function sg(){es=!1,nt!==null&&Yu(nt)&&(nt=null),ut!==null&&Yu(ut)&&(ut=null),it!==null&&Yu(it)&&(it=null),xn.forEach(Qf),An.forEach(Qf)}function Xu(e,l){e.blockedOn===l&&(e.blockedOn=null,es||(es=!0,h.unstable_scheduleCallback(h.unstable_NormalPriority,sg)))}var wu=null;function Zf(e){wu!==e&&(wu=e,h.unstable_scheduleCallback(h.unstable_NormalPriority,function(){wu===e&&(wu=null);for(var l=0;l<e.length;l+=3){var t=e[l],a=e[l+1],n=e[l+2];if(typeof a!="function"){if(Ic(a||t)===null)continue;break}var u=Zt(t);u!==null&&(e.splice(l,3),l-=3,Vi(u,{pending:!0,data:n,method:t.method,action:a},a,n))}}))}function Nn(e){function l(s){return Xu(s,e)}nt!==null&&Xu(nt,e),ut!==null&&Xu(ut,e),it!==null&&Xu(it,e),xn.forEach(l),An.forEach(l);for(var t=0;t<ct.length;t++){var a=ct[t];a.blockedOn===e&&(a.blockedOn=null)}for(;0<ct.length&&(t=ct[0],t.blockedOn===null);)Vf(t),t.blockedOn===null&&ct.shift();if(t=(e.ownerDocument||e).$$reactFormReplay,t!=null)for(a=0;a<t.length;a+=3){var n=t[a],u=t[a+1],i=n[Re]||null;if(typeof u=="function")i||Zf(t);else if(i){var c=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[Re]||null)c=i.formAction;else if(Ic(n)!==null)continue}else c=i.action;typeof c=="function"?t[a+1]=c:(t.splice(a,3),a-=3),Zf(t)}}}function ls(e){this._internalRoot=e}Vu.prototype.render=ls.prototype.render=function(e){var l=this._internalRoot;if(l===null)throw Error(d(409));var t=l.current,a=Xe();qf(t,a,e,l,null,null)},Vu.prototype.unmount=ls.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var l=e.containerInfo;e.tag===0&&ba(),qf(e.current,2,null,e,null,null),Nu(),l[Qt]=null}};function Vu(e){this._internalRoot=e}Vu.prototype.unstable_scheduleHydration=function(e){if(e){var l=ys();e={blockedOn:null,target:e,priority:l};for(var t=0;t<ct.length&&l!==0&&l<ct[t].priority;t++);ct.splice(t,0,e),t===0&&Vf(e)}};var Kf=y.version;if(Kf!=="19.0.0")throw Error(d(527,Kf,"19.0.0"));Q.findDOMNode=function(e){var l=e._reactInternals;if(l===void 0)throw typeof e.render=="function"?Error(d(188)):(e=Object.keys(e).join(","),Error(d(268,e)));return e=Xt(l),e=e!==null?pt(e):null,e=e===null?null:e.stateNode,e};var og={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:R,findFiberByHostInstance:vt,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Qu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Qu.isDisabled&&Qu.supportsFiber)try{Ca=Qu.inject(og),He=Qu}catch{}}return Mn.createRoot=function(e,l){if(!N(e))throw Error(d(299));var t=!1,a="",n=sr,u=or,i=rr,c=null;return l!=null&&(l.unstable_strictMode===!0&&(t=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(n=l.onUncaughtError),l.onCaughtError!==void 0&&(u=l.onCaughtError),l.onRecoverableError!==void 0&&(i=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(c=l.unstable_transitionCallbacks)),l=Lf(e,1,!1,null,null,t,a,n,u,i,c,null),e[Qt]=l.current,qc(e.nodeType===8?e.parentNode:e),new ls(l)},Mn.hydrateRoot=function(e,l,t){if(!N(e))throw Error(d(299));var a=!1,n="",u=sr,i=or,c=rr,s=null,f=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(c=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(s=t.unstable_transitionCallbacks),t.formState!==void 0&&(f=t.formState)),l=Lf(e,1,!0,l,t??null,a,n,u,i,c,s,f),l.context=Bf(null),t=l.current,a=Xe(),n=Pl(a),n.callback=null,$l(t,n,a),l.current.lanes=a,ja(l,a),gl(l),e[Qt]=l.current,qc(e),new Vu(l)},Mn.version="19.0.0",Mn}var ld;function tp(){if(ld)return ts.exports;ld=1;function h(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(h)}catch(y){console.error(y)}}return h(),ts.exports=lp(),ts.exports}var ap=tp();const np={visibleTabs:{},setTabVisibility:()=>{},isTabVisible:()=>!1},pd=E.createContext(np),up=({children:h})=>{const y=we.use.currentTab(),[x,d]=E.useState(()=>({documents:!0,"knowledge-graph":!0,retrieval:!0,api:!0}));E.useEffect(()=>{d(j=>({...j,documents:!0,"knowledge-graph":!0,retrieval:!0,api:!0}))},[y]);const N=E.useMemo(()=>({visibleTabs:x,setTabVisibility:(j,H)=>{d(P=>({...P,[j]:H}))},isTabVisible:j=>!!x[j]}),[x]);return o.jsx(pd.Provider,{value:N,children:h})};var yd="AlertDialog",[ip,Yy]=hg(yd,[td]),ql=td(),vd=h=>{const{__scopeAlertDialog:y,...x}=h,d=ql(y);return o.jsx(Sg,{...d,...x,modal:!0})};vd.displayName=yd;var cp="AlertDialogTrigger",sp=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,N=ql(x);return o.jsx(Tg,{...N,...d,ref:y})});sp.displayName=cp;var op="AlertDialogPortal",bd=h=>{const{__scopeAlertDialog:y,...x}=h,d=ql(y);return o.jsx(dg,{...d,...x})};bd.displayName=op;var rp="AlertDialogOverlay",Sd=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,N=ql(x);return o.jsx(fg,{...N,...d,ref:y})});Sd.displayName=rp;var Na="AlertDialogContent",[fp,dp]=ip(Na),Td=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,children:d,...N}=h,j=ql(x),H=E.useRef(null),P=ad(y,H),Y=E.useRef(null);return o.jsx(mg,{contentName:Na,titleName:xd,docsSlug:"alert-dialog",children:o.jsx(fp,{scope:x,cancelRef:Y,children:o.jsxs(gg,{role:"alertdialog",...j,...N,ref:P,onOpenAutoFocus:pg(N.onOpenAutoFocus,$=>{var he;$.preventDefault(),(he=Y.current)==null||he.focus({preventScroll:!0})}),onPointerDownOutside:$=>$.preventDefault(),onInteractOutside:$=>$.preventDefault(),children:[o.jsx(yg,{children:d}),o.jsx(hp,{contentRef:H})]})})})});Td.displayName=Na;var xd="AlertDialogTitle",Ad=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,N=ql(x);return o.jsx(vg,{...N,...d,ref:y})});Ad.displayName=xd;var Dd="AlertDialogDescription",Nd=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,N=ql(x);return o.jsx(bg,{...N,...d,ref:y})});Nd.displayName=Dd;var mp="AlertDialogAction",Ed=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,N=ql(x);return o.jsx(nd,{...N,...d,ref:y})});Ed.displayName=mp;var Md="AlertDialogCancel",zd=E.forwardRef((h,y)=>{const{__scopeAlertDialog:x,...d}=h,{cancelRef:N}=dp(Md,x),j=ql(x),H=ad(y,N);return o.jsx(nd,{...j,...d,ref:H})});zd.displayName=Md;var hp=({contentRef:h})=>{const y=`\`${Na}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${Na}\` by passing a \`${Dd}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Na}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return E.useEffect(()=>{var d;document.getElementById((d=h.current)==null?void 0:d.getAttribute("aria-describedby"))||console.warn(y)},[y,h]),null},gp=vd,pp=bd,Cd=Sd,Od=Td,jd=Ed,Rd=zd,_d=Ad,Ud=Nd;const yp=gp,vp=pp,Hd=E.forwardRef(({className:h,...y},x)=>o.jsx(Cd,{className:Ve("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",h),...y,ref:x}));Hd.displayName=Cd.displayName;const Ld=E.forwardRef(({className:h,...y},x)=>o.jsxs(vp,{children:[o.jsx(Hd,{}),o.jsx(Od,{ref:x,className:Ve("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",h),...y})]}));Ld.displayName=Od.displayName;const Bd=({className:h,...y})=>o.jsx("div",{className:Ve("flex flex-col space-y-2 text-center sm:text-left",h),...y});Bd.displayName="AlertDialogHeader";const qd=E.forwardRef(({className:h,...y},x)=>o.jsx(_d,{ref:x,className:Ve("text-lg font-semibold",h),...y}));qd.displayName=_d.displayName;const Gd=E.forwardRef(({className:h,...y},x)=>o.jsx(Ud,{ref:x,className:Ve("text-muted-foreground text-sm",h),...y}));Gd.displayName=Ud.displayName;const bp=E.forwardRef(({className:h,...y},x)=>o.jsx(jd,{ref:x,className:Ve(od(),h),...y}));bp.displayName=jd.displayName;const Sp=E.forwardRef(({className:h,...y},x)=>o.jsx(Rd,{ref:x,className:Ve(od({variant:"outline"}),"mt-2 sm:mt-0",h),...y}));Sp.displayName=Rd.displayName;const Tp=({open:h,onOpenChange:y})=>{const{t:x}=Bl(),d=we.use.apiKey(),[N,j]=E.useState(""),H=Gt.use.message();E.useEffect(()=>{j(d||"")},[d,h]),E.useEffect(()=>{H&&(H.includes(rd)||H.includes(fd))&&y(!0)},[H,y]);const P=E.useCallback(()=>{we.setState({apiKey:N||null}),y(!1)},[N,y]),Y=E.useCallback($=>{j($.target.value)},[j]);return o.jsx(yp,{open:h,onOpenChange:y,children:o.jsxs(Ld,{children:[o.jsxs(Bd,{children:[o.jsx(qd,{children:x("apiKeyAlert.title")}),o.jsx(Gd,{children:x("apiKeyAlert.description")})]}),o.jsxs("div",{className:"flex flex-col gap-4",children:[o.jsxs("form",{className:"flex gap-2",onSubmit:$=>$.preventDefault(),children:[o.jsx(us,{type:"password",value:N,onChange:Y,placeholder:x("apiKeyAlert.placeholder"),className:"max-h-full w-full min-w-0",autoComplete:"off"}),o.jsx(Cn,{onClick:P,variant:"outline",size:"sm",children:x("apiKeyAlert.save")})]}),H&&o.jsx("div",{className:"text-sm text-red-500",children:H})]})]})})},xp=({status:h})=>{const{t:y}=Bl();return h?o.jsxs("div",{className:"min-w-[300px] space-y-2 text-xs",children:[o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.storageInfo")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.workingDirectory"),":"]}),o.jsx("span",{className:"truncate",children:h.working_directory}),o.jsxs("span",{children:[y("graphPanel.statusCard.inputDirectory"),":"]}),o.jsx("span",{className:"truncate",children:h.input_directory})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.llmConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.llmBinding"),":"]}),o.jsx("span",{children:h.configuration.llm_binding}),o.jsxs("span",{children:[y("graphPanel.statusCard.llmBindingHost"),":"]}),o.jsx("span",{children:h.configuration.llm_binding_host}),o.jsxs("span",{children:[y("graphPanel.statusCard.llmModel"),":"]}),o.jsx("span",{children:h.configuration.llm_model}),o.jsxs("span",{children:[y("graphPanel.statusCard.maxTokens"),":"]}),o.jsx("span",{children:h.configuration.max_tokens})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.embeddingConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.embeddingBinding"),":"]}),o.jsx("span",{children:h.configuration.embedding_binding}),o.jsxs("span",{children:[y("graphPanel.statusCard.embeddingBindingHost"),":"]}),o.jsx("span",{children:h.configuration.embedding_binding_host}),o.jsxs("span",{children:[y("graphPanel.statusCard.embeddingModel"),":"]}),o.jsx("span",{children:h.configuration.embedding_model})]})]}),h.configuration.enable_rerank&&o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.rerankerConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.rerankerBindingHost"),":"]}),o.jsx("span",{children:h.configuration.rerank_binding_host||"-"}),o.jsxs("span",{children:[y("graphPanel.statusCard.rerankerModel"),":"]}),o.jsx("span",{children:h.configuration.rerank_model||"-"})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx("h4",{className:"font-medium",children:y("graphPanel.statusCard.storageConfig")}),o.jsxs("div",{className:"text-foreground grid grid-cols-[160px_1fr] gap-1",children:[o.jsxs("span",{children:[y("graphPanel.statusCard.kvStorage"),":"]}),o.jsx("span",{children:h.configuration.kv_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.docStatusStorage"),":"]}),o.jsx("span",{children:h.configuration.doc_status_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.graphStorage"),":"]}),o.jsx("span",{children:h.configuration.graph_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.vectorStorage"),":"]}),o.jsx("span",{children:h.configuration.vector_storage}),o.jsxs("span",{children:[y("graphPanel.statusCard.workspace"),":"]}),o.jsx("span",{children:h.configuration.workspace||"-"}),o.jsxs("span",{children:[y("graphPanel.statusCard.maxGraphNodes"),":"]}),o.jsx("span",{children:h.configuration.max_graph_nodes||"-"}),h.keyed_locks&&o.jsxs(o.Fragment,{children:[o.jsxs("span",{children:[y("graphPanel.statusCard.lockStatus"),":"]}),o.jsxs("span",{children:["mp ",h.keyed_locks.current_status.pending_mp_cleanup,"/",h.keyed_locks.current_status.total_mp_locks," | async ",h.keyed_locks.current_status.pending_async_cleanup,"/",h.keyed_locks.current_status.total_async_locks,"(pid: ",h.keyed_locks.process_id,")"]})]})]})]})]}):o.jsx("div",{className:"text-foreground text-xs",children:y("graphPanel.statusCard.unavailable")})},Ap=({open:h,onOpenChange:y,status:x})=>{const{t:d}=Bl();return o.jsx(Mg,{open:h,onOpenChange:y,children:o.jsxs(zg,{className:"sm:max-w-[700px]",children:[o.jsxs(Cg,{children:[o.jsx(Og,{children:d("graphPanel.statusDialog.title")}),o.jsx(jg,{children:d("graphPanel.statusDialog.description")})]}),o.jsx(xp,{status:x})]})})},Dp=()=>{const{t:h}=Bl(),y=Gt.use.health(),x=Gt.use.lastCheckTime(),d=Gt.use.status(),[N,j]=E.useState(!1),[H,P]=E.useState(!1);return E.useEffect(()=>{j(!0);const Y=setTimeout(()=>j(!1),300);return()=>clearTimeout(Y)},[x]),o.jsxs("div",{className:"fixed right-4 bottom-4 flex items-center gap-2 opacity-80 select-none",children:[o.jsxs("div",{className:"flex cursor-pointer items-center gap-2",onClick:()=>P(!0),children:[o.jsx("div",{className:Ve("h-3 w-3 rounded-full transition-all duration-300","shadow-[0_0_8px_rgba(0,0,0,0.2)]",y?"bg-green-500":"bg-red-500",N&&"scale-125",N&&y&&"shadow-[0_0_12px_rgba(34,197,94,0.4)]",N&&!y&&"shadow-[0_0_12px_rgba(239,68,68,0.4)]")}),o.jsx("span",{className:"text-muted-foreground text-xs",children:h(y?"graphPanel.statusIndicator.connected":"graphPanel.statusIndicator.disconnected")})]}),o.jsx(Ap,{open:H,onOpenChange:P,status:d})]})};function Yd({className:h}){const[y,x]=E.useState(!1),{t:d}=Bl(),N=we.use.language(),j=we.use.setLanguage(),H=we.use.theme(),P=we.use.setTheme(),Y=E.useCallback(he=>{j(he)},[j]),$=E.useCallback(he=>{P(he)},[P]);return o.jsxs(Rg,{open:y,onOpenChange:x,children:[o.jsx(_g,{asChild:!0,children:o.jsx(Cn,{variant:"ghost",size:"icon",className:Ve("h-9 w-9",h),children:o.jsx(Ug,{className:"h-5 w-5"})})}),o.jsx(Hg,{side:"bottom",align:"end",className:"w-56",children:o.jsxs("div",{className:"flex flex-col gap-4",children:[o.jsxs("div",{className:"flex flex-col gap-2",children:[o.jsx("label",{className:"text-sm font-medium",children:d("settings.language")}),o.jsxs(Jf,{value:N,onValueChange:Y,children:[o.jsx(Ff,{children:o.jsx(Pf,{})}),o.jsxs($f,{children:[o.jsx(ot,{value:"en",children:"English"}),o.jsx(ot,{value:"zh",children:"中文"}),o.jsx(ot,{value:"fr",children:"Français"}),o.jsx(ot,{value:"ar",children:"العربية"}),o.jsx(ot,{value:"zh_TW",children:"繁體中文"})]})]})]}),o.jsxs("div",{className:"flex flex-col gap-2",children:[o.jsx("label",{className:"text-sm font-medium",children:d("settings.theme")}),o.jsxs(Jf,{value:H,onValueChange:$,children:[o.jsx(Ff,{children:o.jsx(Pf,{})}),o.jsxs($f,{children:[o.jsx(ot,{value:"light",children:d("settings.light")}),o.jsx(ot,{value:"dark",children:d("settings.dark")}),o.jsx(ot,{value:"system",children:d("settings.system")})]})]})]})]})})]})}const Np=xg,Xd=E.forwardRef(({className:h,...y},x)=>o.jsx(ud,{ref:x,className:Ve("bg-muted text-muted-foreground inline-flex h-10 items-center justify-center rounded-md p-1",h),...y}));Xd.displayName=ud.displayName;const wd=E.forwardRef(({className:h,...y},x)=>o.jsx(id,{ref:x,className:Ve("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center rounded-sm px-3 py-1.5 text-sm font-medium whitespace-nowrap transition-all focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm",h),...y}));wd.displayName=id.displayName;const zn=E.forwardRef(({className:h,...y},x)=>o.jsx(cd,{ref:x,className:Ve("ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none","data-[state=inactive]:invisible data-[state=active]:visible","h-full w-full",h),forceMount:!0,...y}));zn.displayName=cd.displayName;function Zu({value:h,currentTab:y,children:x}){return o.jsx(wd,{value:h,className:Ve("cursor-pointer px-2 py-1 transition-all",y===h?"!bg-emerald-400 !text-zinc-50":"hover:bg-background/60"),children:x})}function Ep(){const h=we.use.currentTab(),{t:y}=Bl();return o.jsx("div",{className:"flex h-8 self-center",children:o.jsxs(Xd,{className:"h-full gap-2",children:[o.jsx(Zu,{value:"documents",currentTab:h,children:y("header.documents")}),o.jsx(Zu,{value:"knowledge-graph",currentTab:h,children:y("header.knowledgeGraph")}),o.jsx(Zu,{value:"retrieval",currentTab:h,children:y("header.retrieval")}),o.jsx(Zu,{value:"api",currentTab:h,children:y("header.api")})]})})}function Mp(){const{t:h}=Bl(),{isGuestMode:y,coreVersion:x,apiVersion:d,username:N,webuiTitle:j,webuiDescription:H}=Ll(),P=x&&d?`${x}/${d}`:null,Y=()=>{md.navigateToLogin()};return o.jsxs("header",{className:"border-border/40 bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 flex h-10 w-full border-b px-4 backdrop-blur",children:[o.jsxs("div",{className:"min-w-[200px] w-auto flex items-center",children:[o.jsxs("a",{href:dd,className:"flex items-center gap-2",children:[o.jsx(ss,{className:"size-4 text-emerald-400","aria-hidden":"true"}),o.jsx("span",{className:"font-bold md:inline-block",children:is.name})]}),j&&o.jsxs("div",{className:"flex items-center",children:[o.jsx("span",{className:"mx-1 text-xs text-gray-500 dark:text-gray-400",children:"|"}),o.jsx(Lg,{children:o.jsxs(Bg,{children:[o.jsx(qg,{asChild:!0,children:o.jsx("span",{className:"font-medium text-sm cursor-default",children:j})}),H&&o.jsx(Gg,{side:"bottom",children:H})]})})]})]}),o.jsxs("div",{className:"flex h-10 flex-1 items-center justify-center",children:[o.jsx(Ep,{}),y&&o.jsx("div",{className:"ml-2 self-center px-2 py-1 text-xs bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 rounded-md",children:h("login.guestMode","Guest Mode")})]}),o.jsx("nav",{className:"w-[200px] flex items-center justify-end",children:o.jsxs("div",{className:"flex items-center gap-2",children:[P&&o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 mr-1",children:["v",P]}),o.jsx(Cn,{variant:"ghost",size:"icon",side:"bottom",tooltip:h("header.projectRepository"),children:o.jsx("a",{href:is.github,target:"_blank",rel:"noopener noreferrer",children:o.jsx(Yg,{className:"size-4","aria-hidden":"true"})})}),o.jsx(Yd,{}),!y&&o.jsx(Cn,{variant:"ghost",size:"icon",side:"bottom",tooltip:`${h("header.logout")} (${N})`,onClick:Y,children:o.jsx(Xg,{className:"size-4","aria-hidden":"true"})})]})})]})}const zp=()=>{const h=E.useContext(pd);if(!h)throw new Error("useTabVisibility must be used within a TabVisibilityProvider");return h};function Cp(){const{t:h}=Bl(),{isTabVisible:y}=zp(),x=y("api"),[d,N]=E.useState(!1);return E.useEffect(()=>{d||N(!0)},[d]),o.jsx("div",{className:`size-full ${x?"":"hidden"}`,children:d?o.jsx("iframe",{src:wg+"/docs",className:"size-full w-full h-full",style:{width:"100%",height:"100%",border:"none"}},"api-docs-iframe"):o.jsx("div",{className:"flex h-full w-full items-center justify-center bg-background",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),o.jsx("p",{children:h("apiSite.loading")})]})})})}function Op(){const h=Gt.use.message(),y=we.use.enableHealthCheck(),x=we.use.currentTab(),[d,N]=E.useState(!1),[j,H]=E.useState(!0),P=E.useRef(!1),Y=E.useRef(!1),$=E.useCallback(w=>{N(w),w||Gt.getState().clear()},[]),he=E.useRef(!0);E.useEffect(()=>{he.current=!0;const w=()=>{he.current=!1};return window.addEventListener("beforeunload",w),()=>{he.current=!1,window.removeEventListener("beforeunload",w)}},[]),E.useEffect(()=>{if(!y||d)return;const w=async()=>{try{he.current&&await Gt.getState().check()}catch(le){console.error("Health check error:",le)}};Y.current||(Y.current=!0,w());const pe=setInterval(w,Vg*1e3);return()=>clearInterval(pe)},[y,d]),E.useEffect(()=>{(async()=>{if(P.current)return;if(P.current=!0,sessionStorage.getItem("VERSION_CHECKED_FROM_LOGIN")==="true"){H(!1);return}try{H(!0);const le=localStorage.getItem("LIGHTRAG-API-TOKEN"),C=await gd();if(!C.auth_configured&&C.access_token)Ll.getState().login(C.access_token,!0,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null);else if(le&&(C.core_version||C.api_version||C.webui_title||C.webui_description)){const pl=C.auth_mode==="disabled"||Ll.getState().isGuestMode;Ll.getState().login(le,pl,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null)}sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true")}catch(le){console.error("Failed to get version info:",le)}finally{H(!1)}})()},[]);const ge=E.useCallback(w=>we.getState().setCurrentTab(w),[]);return E.useEffect(()=>{h&&(h.includes(rd)||h.includes(fd))&&N(!0)},[h]),o.jsx(hd,{children:o.jsx(up,{children:j?o.jsxs("div",{className:"flex h-screen w-screen flex-col",children:[o.jsxs("header",{className:"border-border/40 bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 flex h-10 w-full border-b px-4 backdrop-blur",children:[o.jsx("div",{className:"min-w-[200px] w-auto flex items-center",children:o.jsxs("a",{href:dd,className:"flex items-center gap-2",children:[o.jsx(ss,{className:"size-4 text-emerald-400","aria-hidden":"true"}),o.jsx("span",{className:"font-bold md:inline-block",children:is.name})]})}),o.jsx("div",{className:"flex h-10 flex-1 items-center justify-center"}),o.jsx("nav",{className:"w-[200px] flex items-center justify-end"})]}),o.jsx("div",{className:"flex flex-1 items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),o.jsx("p",{children:"Initializing..."})]})})]}):o.jsxs("main",{className:"flex h-screen w-screen overflow-hidden",children:[o.jsxs(Np,{defaultValue:x,className:"!m-0 flex grow flex-col !p-0 overflow-hidden",onValueChange:ge,children:[o.jsx(Mp,{}),o.jsxs("div",{className:"relative grow",children:[o.jsx(zn,{value:"documents",className:"absolute top-0 right-0 bottom-0 left-0 overflow-auto",children:o.jsx(Wg,{})}),o.jsx(zn,{value:"knowledge-graph",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx(Qg,{})}),o.jsx(zn,{value:"retrieval",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx($g,{})}),o.jsx(zn,{value:"api",className:"absolute top-0 right-0 bottom-0 left-0 overflow-hidden",children:o.jsx(Cp,{})})]})]}),y&&o.jsx(Dp,{}),o.jsx(Tp,{open:d,onOpenChange:$})]})})})}const jp=()=>{const h=sd(),{login:y,isAuthenticated:x}=Ll(),{t:d}=Bl(),[N,j]=E.useState(!1),[H,P]=E.useState(""),[Y,$]=E.useState(""),[he,ge]=E.useState(!0),w=E.useRef(!1);if(E.useEffect(()=>{console.log("LoginPage mounted")},[]),E.useEffect(()=>((async()=>{if(!w.current){w.current=!0;try{if(x){h("/");return}const C=await gd();if((C.core_version||C.api_version)&&sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true"),!C.auth_configured&&C.access_token){y(C.access_token,!0,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null),C.message&&En.info(C.message),h("/");return}ge(!1)}catch(C){console.error("Failed to check auth configuration:",C),ge(!1)}}})(),()=>{}),[x,y,h]),he)return null;const pe=async le=>{if(le.preventDefault(),!H||!Y){En.error(d("login.errorEmptyFields"));return}try{j(!0);const C=await Jg(H,Y);localStorage.getItem("LIGHTRAG-PREVIOUS-USER")===H?console.log("Same user logging in, preserving chat history"):(console.log("Different user logging in, clearing chat history"),we.getState().setRetrievalHistory([])),localStorage.setItem("LIGHTRAG-PREVIOUS-USER",H);const je=C.auth_mode==="disabled";y(C.access_token,je,C.core_version,C.api_version,C.webui_title||null,C.webui_description||null),(C.core_version||C.api_version)&&sessionStorage.setItem("VERSION_CHECKED_FROM_LOGIN","true"),je?En.info(C.message||d("login.authDisabled","Authentication is disabled. Using guest access.")):En.success(d("login.successMessage")),h("/")}catch(C){console.error("Login failed...",C),En.error(d("login.errorInvalidCredentials")),Ll.getState().logout(),localStorage.removeItem("LIGHTRAG-API-TOKEN")}finally{j(!1)}};return o.jsxs("div",{className:"flex h-screen w-screen items-center justify-center bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-gray-900 dark:to-gray-800",children:[o.jsx("div",{className:"absolute top-4 right-4 flex items-center gap-2",children:o.jsx(Yd,{className:"bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-md"})}),o.jsxs(Zg,{className:"w-full max-w-[480px] shadow-lg mx-4",children:[o.jsx(Kg,{className:"flex items-center justify-center space-y-2 pb-8 pt-6",children:o.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("img",{src:"logo.svg",alt:"LightRAG Logo",className:"h-12 w-12"}),o.jsx(ss,{className:"size-10 text-emerald-400","aria-hidden":"true"})]}),o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"LightRAG"}),o.jsx("p",{className:"text-muted-foreground text-sm",children:d("login.description")})]})]})}),o.jsx(kg,{className:"px-8 pb-8",children:o.jsxs("form",{onSubmit:pe,className:"space-y-6",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("label",{htmlFor:"username-input",className:"text-sm font-medium w-16 shrink-0",children:d("login.username")}),o.jsx(us,{id:"username-input",placeholder:d("login.usernamePlaceholder"),value:H,onChange:le=>P(le.target.value),required:!0,className:"h-11 flex-1"})]}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("label",{htmlFor:"password-input",className:"text-sm font-medium w-16 shrink-0",children:d("login.password")}),o.jsx(us,{id:"password-input",type:"password",placeholder:d("login.passwordPlaceholder"),value:Y,onChange:le=>$(le.target.value),required:!0,className:"h-11 flex-1"})]}),o.jsx(Cn,{type:"submit",className:"w-full h-11 text-base font-medium mt-2",disabled:N,children:d(N?"login.loggingIn":"login.loginButton")})]})})]})]})},Rp=()=>{const[h,y]=E.useState(!0),{isAuthenticated:x}=Ll(),d=sd();return E.useEffect(()=>{md.setNavigate(d)},[d]),E.useEffect(()=>((async()=>{try{const j=localStorage.getItem("LIGHTRAG-API-TOKEN");if(j&&x){y(!1);return}j||Ll.getState().logout()}catch(j){console.error("Auth initialization error:",j),x||Ll.getState().logout()}finally{y(!1)}})(),()=>{}),[x]),E.useEffect(()=>{!h&&!x&&window.location.hash.slice(1)!=="/login"&&(console.log("Not authenticated, redirecting to login"),d("/login"))},[h,x,d]),h?null:o.jsxs(Eg,{children:[o.jsx(kf,{path:"/login",element:o.jsx(jp,{})}),o.jsx(kf,{path:"/*",element:x?o.jsx(Op,{}):null})]})},_p=()=>o.jsx(hd,{children:o.jsxs(Ng,{children:[o.jsx(Rp,{}),o.jsx(Fg,{position:"bottom-center",theme:"system",closeButton:!0,richColors:!0})]})}),Up={language:"Language",theme:"Theme",light:"Light",dark:"Dark",system:"System"},Hp={documents:"Documents",knowledgeGraph:"Knowledge Graph",retrieval:"Retrieval",api:"API",projectRepository:"Project Repository",logout:"Logout",themeToggle:{switchToLight:"Switch to light theme",switchToDark:"Switch to dark theme"}},Lp={description:"Please enter your account and password to log in to the system",username:"Username",usernamePlaceholder:"Please input a username",password:"Password",passwordPlaceholder:"Please input a password",loginButton:"Login",loggingIn:"Logging in...",successMessage:"Login succeeded",errorEmptyFields:"Please enter your username and password",errorInvalidCredentials:"Login failed, please check username and password",authDisabled:"Authentication is disabled. Using login free mode.",guestMode:"Login Free"},Bp={cancel:"Cancel",save:"Save",saving:"Saving...",saveFailed:"Save failed"},qp={clearDocuments:{button:"Clear",tooltip:"Clear documents",title:"Clear Documents",description:"This will remove all documents from the system",warning:"WARNING: This action will permanently delete all documents and cannot be undone!",confirm:"Do you really want to clear all documents?",confirmPrompt:"Type 'yes' to confirm this action",confirmPlaceholder:"Type yes to confirm",clearCache:"Clear LLM cache",confirmButton:"YES",success:"Documents cleared successfully",cacheCleared:"Cache cleared successfully",cacheClearFailed:`Failed to clear cache:
{{error}}`,failed:`Clear Documents Failed:
{{message}}`,error:`Clear Documents Failed:
{{error}}`},deleteDocuments:{button:"Delete",tooltip:"Delete selected documents",title:"Delete Documents",description:"This will permanently delete the selected documents from the system",warning:"WARNING: This action will permanently delete the selected documents and cannot be undone!",confirm:"Do you really want to delete {{count}} selected document(s)?",confirmPrompt:"Type 'yes' to confirm this action",confirmPlaceholder:"Type yes to confirm",confirmButton:"YES",deleteFileOption:"Also delete uploaded files",deleteFileTooltip:"Check this option to also delete the corresponding uploaded files on the server",success:"Document deletion pipeline started successfully",failed:`Delete Documents Failed:
{{message}}`,error:`Delete Documents Failed:
{{error}}`,busy:"Pipeline is busy, please try again later",notAllowed:"No permission to perform this operation",cannotDeleteAll:"Cannot delete all documents. If you need to delete all documents, please use the Clear Documents feature."},deselectDocuments:{button:"Deselect",tooltip:"Deselect all selected documents",title:"Deselect Documents",description:"This will clear all selected documents ({{count}} selected)",confirmButton:"Deselect All"},uploadDocuments:{button:"Upload",tooltip:"Upload documents",title:"Upload Documents",description:"Drag and drop your documents here or click to browse.",single:{uploading:"Uploading {{name}}: {{percent}}%",success:`Upload Success:
{{name}} uploaded successfully`,failed:`Upload Failed:
{{name}}
{{message}}`,error:`Upload Failed:
{{name}}
{{error}}`},batch:{uploading:"Uploading files...",success:"Files uploaded successfully",error:"Some files failed to upload"},generalError:`Upload Failed
{{error}}`,fileTypes:"Supported types: TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"Cannot upload more than 1 file at a time",maxFilesLimit:"Cannot upload more than {{count}} files",fileRejected:"File {{name}} was rejected",unsupportedType:"Unsupported file type",fileTooLarge:"File too large, maximum size is {{maxSize}}",dropHere:"Drop the files here",dragAndDrop:"Drag and drop files here, or click to select files",removeFile:"Remove file",uploadDescription:"You can upload {{isMultiple ? 'multiple' : count}} files (up to {{maxSize}} each)",duplicateFile:"File name already exists in server cache"}},documentManager:{title:"Document Management",scanButton:"Scan",scanTooltip:"Scan documents in input folder",pipelineStatusButton:"Pipeline Status",pipelineStatusTooltip:"View pipeline status",uploadedTitle:"Uploaded Documents",uploadedDescription:"List of uploaded documents and their statuses.",emptyTitle:"No Documents",emptyDescription:"There are no uploaded documents yet.",columns:{id:"ID",summary:"Summary",status:"Status",length:"Length",chunks:"Chunks",created:"Created",updated:"Updated",metadata:"Metadata",select:"Select"},status:{all:"All",completed:"Completed",processing:"Processing",pending:"Pending",failed:"Failed"},errors:{loadFailed:`Failed to load documents
{{error}}`,scanFailed:`Failed to scan documents
{{error}}`,scanProgressFailed:`Failed to get scan progress
{{error}}`},fileNameLabel:"File Name",showButton:"Show",hideButton:"Hide",showFileNameTooltip:"Show file name",hideFileNameTooltip:"Hide file name"},pipelineStatus:{title:"Pipeline Status",busy:"Pipeline Busy",requestPending:"Request Pending",jobName:"Job Name",startTime:"Start Time",progress:"Progress",unit:"batch",latestMessage:"Latest Message",historyMessages:"History Messages",errors:{fetchFailed:`Failed to get pipeline status
{{error}}`}}},Gp={dataIsTruncated:"Graph data is truncated to Max Nodes",statusDialog:{title:"LightRAG Server Settings",description:"View current system status and connection information"},legend:"Legend",nodeTypes:{person:"Person",category:"Category",geo:"Geographic",location:"Location",organization:"Organization",event:"Event",equipment:"Equipment",weapon:"Weapon",animal:"Animal",unknown:"Unknown",object:"Object",group:"Group",technology:"Technology"},sideBar:{settings:{settings:"Settings",healthCheck:"Health Check",showPropertyPanel:"Show Property Panel",showSearchBar:"Show Search Bar",showNodeLabel:"Show Node Label",nodeDraggable:"Node Draggable",showEdgeLabel:"Show Edge Label",hideUnselectedEdges:"Hide Unselected Edges",edgeEvents:"Edge Events",maxQueryDepth:"Max Query Depth",maxNodes:"Max Nodes",maxLayoutIterations:"Max Layout Iterations",resetToDefault:"Reset to default",edgeSizeRange:"Edge Size Range",depth:"D",max:"Max",degree:"Degree",apiKey:"API Key",enterYourAPIkey:"Enter your API key",save:"Save",refreshLayout:"Refresh Layout"},zoomControl:{zoomIn:"Zoom In",zoomOut:"Zoom Out",resetZoom:"Reset Zoom",rotateCamera:"Clockwise Rotate",rotateCameraCounterClockwise:"Counter-Clockwise Rotate"},layoutsControl:{startAnimation:"Continue layout animation",stopAnimation:"Stop layout animation",layoutGraph:"Layout Graph",layouts:{Circular:"Circular",Circlepack:"Circlepack",Random:"Random",Noverlaps:"Noverlaps","Force Directed":"Force Directed","Force Atlas":"Force Atlas"}},fullScreenControl:{fullScreen:"Full Screen",windowed:"Windowed"},legendControl:{toggleLegend:"Toggle Legend"}},statusIndicator:{connected:"Connected",disconnected:"Disconnected"},statusCard:{unavailable:"Status information unavailable",storageInfo:"Storage Info",workingDirectory:"Working Directory",inputDirectory:"Input Directory",llmConfig:"LLM Configuration",llmBinding:"LLM Binding",llmBindingHost:"LLM Endpoint",llmModel:"LLM Model",maxTokens:"Max Tokens",embeddingConfig:"Embedding Configuration",embeddingBinding:"Embedding Binding",embeddingBindingHost:"Embedding Endpoint",embeddingModel:"Embedding Model",storageConfig:"Storage Configuration",kvStorage:"KV Storage",docStatusStorage:"Doc Status Storage",graphStorage:"Graph Storage",vectorStorage:"Vector Storage",workspace:"Workspace",maxGraphNodes:"Max Graph Nodes",rerankerConfig:"Reranker Configuration",rerankerBindingHost:"Reranker Endpoint",rerankerModel:"Reranker Model",lockStatus:"Lock Status"},propertiesView:{editProperty:"Edit {{property}}",editPropertyDescription:"Edit the property value in the text area below.",errors:{duplicateName:"Node name already exists",updateFailed:"Failed to update node",tryAgainLater:"Please try again later"},success:{entityUpdated:"Node updated successfully",relationUpdated:"Relation updated successfully"},node:{title:"Node",id:"ID",labels:"Labels",degree:"Degree",properties:"Properties",relationships:"Relations(within subgraph)",expandNode:"Expand Node",pruneNode:"Prune Node",deleteAllNodesError:"Refuse to delete all nodes in the graph",nodesRemoved:"{{count}} nodes removed, including orphan nodes",noNewNodes:"No expandable nodes found",propertyNames:{description:"Description",entity_id:"Name",entity_type:"Type",source_id:"SrcID",Neighbour:"Neigh",file_path:"Source",keywords:"Keys",weight:"Weight"}},edge:{title:"Relationship",id:"ID",type:"Type",source:"Source",target:"Target",properties:"Properties"}},search:{placeholder:"Search nodes...",message:"And {count} others"},graphLabels:{selectTooltip:"Select query label",noLabels:"No labels found",label:"Label",placeholder:"Search labels...",andOthers:"And {count} others",refreshTooltip:"Reload data(After file added)"},emptyGraph:"Empty(Try Reload Again)"},Yp={chatMessage:{copyTooltip:"Copy to clipboard",copyError:"Failed to copy text to clipboard"},retrieval:{startPrompt:"Start a retrieval by typing your query below",clear:"Clear",send:"Send",placeholder:"Enter your query (Support prefix: /<Query Mode>)",error:"Error: Failed to get response",queryModeError:"Only supports the following query modes: {{modes}}",queryModePrefixInvalid:"Invalid query mode prefix. Use: /<mode> [space] your query"},querySettings:{parametersTitle:"Parameters",parametersDescription:"Configure your query parameters",queryMode:"Query Mode",queryModeTooltip:`Select the retrieval strategy:
• Naive: Basic search without advanced techniques
• Local: Context-dependent information retrieval
• Global: Utilizes global knowledge base
• Hybrid: Combines local and global retrieval
• Mix: Integrates knowledge graph with vector retrieval
• Bypass: Passes query directly to LLM without retrieval`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"Response Format",responseFormatTooltip:`Defines the response format. Examples:
• Multiple Paragraphs
• Single Paragraph
• Bullet Points`,responseFormatOptions:{multipleParagraphs:"Multiple Paragraphs",singleParagraph:"Single Paragraph",bulletPoints:"Bullet Points"},topK:"Top K Results",topKTooltip:"Number of top items to retrieve. Represents entities in 'local' mode and relationships in 'global' mode",topKPlaceholder:"Number of results",maxTokensTextUnit:"Max Tokens for Text Unit",maxTokensTextUnitTooltip:"Maximum number of tokens allowed for each retrieved text chunk",maxTokensGlobalContext:"Max Tokens for Global Context",maxTokensGlobalContextTooltip:"Maximum number of tokens allocated for relationship descriptions in global retrieval",maxTokensLocalContext:"Max Tokens for Local Context",maxTokensLocalContextTooltip:"Maximum number of tokens allocated for entity descriptions in local retrieval",historyTurns:"History Turns",historyTurnsTooltip:"Number of complete conversation turns (user-assistant pairs) to consider in the response context",historyTurnsPlaceholder:"Number of history turns",onlyNeedContext:"Only Need Context",onlyNeedContextTooltip:"If True, only returns the retrieved context without generating a response",onlyNeedPrompt:"Only Need Prompt",onlyNeedPromptTooltip:"If True, only returns the generated prompt without producing a response",streamResponse:"Stream Response",streamResponseTooltip:"If True, enables streaming output for real-time responses",userPrompt:"User Prompt",userPromptTooltip:"Provide additional response requirements to the LLM (unrelated to query content, only for output processing).",userPromptPlaceholder:"Enter custom prompt (optional)"}},Xp={loading:"Loading API Documentation..."},wp={title:"API Key is required",description:"Please enter your API key to access the service",placeholder:"Enter your API key",save:"Save"},Vp={settings:Up,header:Hp,login:Lp,common:Bp,documentPanel:qp,graphPanel:Gp,retrievePanel:Yp,apiSite:Xp,apiKeyAlert:wp},Qp={language:"语言",theme:"主题",light:"浅色",dark:"深色",system:"系统"},Zp={documents:"文档",knowledgeGraph:"知识图谱",retrieval:"检索",api:"API",projectRepository:"项目仓库",logout:"退出登录",themeToggle:{switchToLight:"切换到浅色主题",switchToDark:"切换到深色主题"}},Kp={description:"请输入您的账号和密码登录系统",username:"用户名",usernamePlaceholder:"请输入用户名",password:"密码",passwordPlaceholder:"请输入密码",loginButton:"登录",loggingIn:"登录中...",successMessage:"登录成功",errorEmptyFields:"请输入您的用户名和密码",errorInvalidCredentials:"登录失败，请检查用户名和密码",authDisabled:"认证已禁用，使用无需登陆模式。",guestMode:"无需登陆"},kp={cancel:"取消",save:"保存",saving:"保存中...",saveFailed:"保存失败"},Jp={clearDocuments:{button:"清空",tooltip:"清空文档",title:"清空文档",description:"此操作将从系统中移除所有文档",warning:"警告：此操作将永久删除所有文档，无法恢复！",confirm:"确定要清空所有文档吗？",confirmPrompt:"请输入 yes 确认操作",confirmPlaceholder:"输入 yes 确认",clearCache:"清空LLM缓存",confirmButton:"确定",success:"文档清空成功",cacheCleared:"缓存清空成功",cacheClearFailed:`清空缓存失败：
{{error}}`,failed:`清空文档失败：
{{message}}`,error:`清空文档失败：
{{error}}`},deleteDocuments:{button:"删除",tooltip:"删除选中的文档",title:"删除文档",description:"此操作将永久删除选中的文档",warning:"警告：此操作将永久删除选中的文档，无法恢复！",confirm:"确定要删除 {{count}} 个选中的文档吗？",confirmPrompt:"请输入 yes 确认操作",confirmPlaceholder:"输入 yes 确认",confirmButton:"确定",deleteFileOption:"同时删除上传文件",deleteFileTooltip:"选中此选项将同时删除服务器上对应的上传文件",success:"文档删除流水线启动成功",failed:`删除文档失败：
{{message}}`,error:`删除文档失败：
{{error}}`,busy:"流水线被占用，请稍后再试",notAllowed:"没有操作权限",cannotDeleteAll:"无法删除所有文档。如确实需要删除所有文档请使用清空文档功能。"},deselectDocuments:{button:"取消选择",tooltip:"取消选择所有文档",title:"取消选择文档",description:"此操作将清除所有选中的文档（已选择 {{count}} 个）",confirmButton:"取消全部选择"},uploadDocuments:{button:"上传",tooltip:"上传文档",title:"上传文档",description:"拖拽文件到此处或点击浏览",single:{uploading:"正在上传 {{name}}：{{percent}}%",success:`上传成功：
{{name}} 上传完成`,failed:`上传失败：
{{name}}
{{message}}`,error:`上传失败：
{{name}}
{{error}}`},batch:{uploading:"正在上传文件...",success:"文件上传完成",error:"部分文件上传失败"},generalError:`上传失败
{{error}}`,fileTypes:"支持的文件类型：TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"一次只能上传一个文件",maxFilesLimit:"最多只能上传 {{count}} 个文件",fileRejected:"文件 {{name}} 被拒绝",unsupportedType:"不支持的文件类型",fileTooLarge:"文件过大，最大允许 {{maxSize}}",dropHere:"将文件拖放到此处",dragAndDrop:"拖放文件到此处，或点击选择文件",removeFile:"移除文件",uploadDescription:"您可以上传{{isMultiple ? '多个' : count}}个文件（每个文件最大{{maxSize}}）",duplicateFile:"文件名与服务器上的缓存重复"}},documentManager:{title:"文档管理",scanButton:"扫描",scanTooltip:"扫描输入目录中的文档",pipelineStatusButton:"流水线状态",pipelineStatusTooltip:"查看流水线状态",uploadedTitle:"已上传文档",uploadedDescription:"已上传文档列表及其状态",emptyTitle:"无文档",emptyDescription:"还没有上传任何文档",columns:{id:"ID",summary:"摘要",status:"状态",length:"长度",chunks:"分块",created:"创建时间",updated:"更新时间",metadata:"元数据",select:"选择"},status:{all:"全部",completed:"已完成",processing:"处理中",pending:"等待中",failed:"失败"},errors:{loadFailed:`加载文档失败
{{error}}`,scanFailed:`扫描文档失败
{{error}}`,scanProgressFailed:`获取扫描进度失败
{{error}}`},fileNameLabel:"文件名",showButton:"显示",hideButton:"隐藏",showFileNameTooltip:"显示文件名",hideFileNameTooltip:"隐藏文件名"},pipelineStatus:{title:"流水线状态",busy:"流水线忙碌",requestPending:"待处理请求",jobName:"作业名称",startTime:"开始时间",progress:"进度",unit:"批",latestMessage:"最新消息",historyMessages:"历史消息",errors:{fetchFailed:`获取流水线状态失败
{{error}}`}}},Fp={dataIsTruncated:"图数据已截断至最大返回节点数",statusDialog:{title:"LightRAG 服务器设置",description:"查看当前系统状态和连接信息"},legend:"图例",nodeTypes:{person:"人物角色",category:"分类",geo:"地理名称",location:"位置",organization:"组织机构",event:"事件",equipment:"装备",weapon:"武器",animal:"动物",unknown:"未知",object:"物品",group:"群组",technology:"技术"},sideBar:{settings:{settings:"设置",healthCheck:"健康检查",showPropertyPanel:"显示属性面板",showSearchBar:"显示搜索栏",showNodeLabel:"显示节点标签",nodeDraggable:"节点可拖动",showEdgeLabel:"显示边标签",hideUnselectedEdges:"隐藏未选中的边",edgeEvents:"边事件",maxQueryDepth:"最大查询深度",maxNodes:"最大返回节点数",maxLayoutIterations:"最大布局迭代次数",resetToDefault:"重置为默认值",edgeSizeRange:"边粗细范围",depth:"深",max:"Max",degree:"邻边",apiKey:"API密钥",enterYourAPIkey:"输入您的API密钥",save:"保存",refreshLayout:"刷新布局"},zoomControl:{zoomIn:"放大",zoomOut:"缩小",resetZoom:"重置缩放",rotateCamera:"顺时针旋转图形",rotateCameraCounterClockwise:"逆时针旋转图形"},layoutsControl:{startAnimation:"继续布局动画",stopAnimation:"停止布局动画",layoutGraph:"图布局",layouts:{Circular:"环形",Circlepack:"圆形打包",Random:"随机",Noverlaps:"无重叠","Force Directed":"力导向","Force Atlas":"力地图"}},fullScreenControl:{fullScreen:"全屏",windowed:"窗口"},legendControl:{toggleLegend:"切换图例显示"}},statusIndicator:{connected:"已连接",disconnected:"未连接"},statusCard:{unavailable:"状态信息不可用",storageInfo:"存储信息",workingDirectory:"工作目录",inputDirectory:"输入目录",llmConfig:"LLM配置",llmBinding:"LLM绑定",llmBindingHost:"LLM端点",llmModel:"LLM模型",maxTokens:"最大令牌数",embeddingConfig:"嵌入配置",embeddingBinding:"嵌入绑定",embeddingBindingHost:"嵌入端点",embeddingModel:"嵌入模型",storageConfig:"存储配置",kvStorage:"KV存储",docStatusStorage:"文档状态存储",graphStorage:"图存储",vectorStorage:"向量存储",workspace:"工作空间",maxGraphNodes:"最大图节点数",rerankerConfig:"重排序配置",rerankerBindingHost:"重排序端点",rerankerModel:"重排序模型",lockStatus:"锁状态"},propertiesView:{editProperty:"编辑{{property}}",editPropertyDescription:"在下方文本区域编辑属性值。",errors:{duplicateName:"节点名称已存在",updateFailed:"更新节点失败",tryAgainLater:"请稍后重试"},success:{entityUpdated:"节点更新成功",relationUpdated:"关系更新成功"},node:{title:"节点",id:"ID",labels:"标签",degree:"度数",properties:"属性",relationships:"关系(子图内)",expandNode:"扩展节点",pruneNode:"修剪节点",deleteAllNodesError:"拒绝删除图中的所有节点",nodesRemoved:"已删除 {{count}} 个节点，包括孤立节点",noNewNodes:"没有发现可以扩展的节点",propertyNames:{description:"描述",entity_id:"名称",entity_type:"类型",source_id:"信源ID",Neighbour:"邻接",file_path:"信源",keywords:"Keys",weight:"权重"}},edge:{title:"关系",id:"ID",type:"类型",source:"源节点",target:"目标节点",properties:"属性"}},search:{placeholder:"搜索节点...",message:"还有 {count} 个"},graphLabels:{selectTooltip:"选择查询标签",noLabels:"未找到标签",label:"标签",placeholder:"搜索标签...",andOthers:"还有 {count} 个",refreshTooltip:"重载图形数据(添加文件后需重载)"},emptyGraph:"无数据(请重载图形数据)"},Pp={chatMessage:{copyTooltip:"复制到剪贴板",copyError:"复制文本到剪贴板失败"},retrieval:{startPrompt:"输入查询开始检索",clear:"清空",send:"发送",placeholder:"输入查询内容 (支持模式前缀: /<Query Mode>)",error:"错误：获取响应失败",queryModeError:"仅支持以下查询模式：{{modes}}",queryModePrefixInvalid:"无效的查询模式前缀。请使用：/<模式> [空格] 查询内容"},querySettings:{parametersTitle:"参数",parametersDescription:"配置查询参数",queryMode:"查询模式",queryModeTooltip:`选择检索策略：
• Naive：基础搜索，无高级技术
• Local：上下文相关信息检索
• Global：利用全局知识库
• Hybrid：结合本地和全局检索
• Mix：整合知识图谱和向量检索
• Bypass：直接传递查询到LLM，不进行检索`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"响应格式",responseFormatTooltip:`定义响应格式。例如：
• 多段落
• 单段落
• 要点`,responseFormatOptions:{multipleParagraphs:"多段落",singleParagraph:"单段落",bulletPoints:"要点"},topK:"Top K结果",topKTooltip:"检索的顶部项目数。在'local'模式下表示实体，在'global'模式下表示关系",topKPlaceholder:"结果数量",maxTokensTextUnit:"文本单元最大令牌数",maxTokensTextUnitTooltip:"每个检索文本块允许的最大令牌数",maxTokensGlobalContext:"全局上下文最大令牌数",maxTokensGlobalContextTooltip:"全局检索中关系描述的最大令牌数",maxTokensLocalContext:"本地上下文最大令牌数",maxTokensLocalContextTooltip:"本地检索中实体描述的最大令牌数",historyTurns:"历史轮次",historyTurnsTooltip:"响应上下文中考虑的完整对话轮次（用户-助手对）数量",historyTurnsPlaceholder:"历史轮次数",onlyNeedContext:"仅需上下文",onlyNeedContextTooltip:"如果为True，仅返回检索到的上下文而不生成响应",onlyNeedPrompt:"仅需提示",onlyNeedPromptTooltip:"如果为True，仅返回生成的提示而不产生响应",streamResponse:"流式响应",streamResponseTooltip:"如果为True，启用实时流式输出响应",userPrompt:"用户提示词",userPromptTooltip:"向LLM提供额外的响应要求（与查询内容无关，仅用于处理输出）。",userPromptPlaceholder:"输入自定义提示词（可选）"}},$p={loading:"正在加载 API 文档..."},Wp={title:"需要 API Key",description:"请输入您的 API Key 以访问服务",placeholder:"请输入 API Key",save:"保存"},Ip={settings:Qp,header:Zp,login:Kp,common:kp,documentPanel:Jp,graphPanel:Fp,retrievePanel:Pp,apiSite:$p,apiKeyAlert:Wp},ey={language:"Langue",theme:"Thème",light:"Clair",dark:"Sombre",system:"Système"},ly={documents:"Documents",knowledgeGraph:"Graphe de connaissances",retrieval:"Récupération",api:"API",projectRepository:"Référentiel du projet",logout:"Déconnexion",themeToggle:{switchToLight:"Passer au thème clair",switchToDark:"Passer au thème sombre"}},ty={description:"Veuillez entrer votre compte et mot de passe pour vous connecter au système",username:"Nom d'utilisateur",usernamePlaceholder:"Veuillez saisir un nom d'utilisateur",password:"Mot de passe",passwordPlaceholder:"Veuillez saisir un mot de passe",loginButton:"Connexion",loggingIn:"Connexion en cours...",successMessage:"Connexion réussie",errorEmptyFields:"Veuillez saisir votre nom d'utilisateur et mot de passe",errorInvalidCredentials:"Échec de la connexion, veuillez vérifier le nom d'utilisateur et le mot de passe",authDisabled:"L'authentification est désactivée. Utilisation du mode sans connexion.",guestMode:"Mode sans connexion"},ay={cancel:"Annuler",save:"Sauvegarder",saving:"Sauvegarde en cours...",saveFailed:"Échec de la sauvegarde"},ny={clearDocuments:{button:"Effacer",tooltip:"Effacer les documents",title:"Effacer les documents",description:"Cette action supprimera tous les documents du système",warning:"ATTENTION : Cette action supprimera définitivement tous les documents et ne peut pas être annulée !",confirm:"Voulez-vous vraiment effacer tous les documents ?",confirmPrompt:"Tapez 'yes' pour confirmer cette action",confirmPlaceholder:"Tapez yes pour confirmer",clearCache:"Effacer le cache LLM",confirmButton:"OUI",success:"Documents effacés avec succès",cacheCleared:"Cache effacé avec succès",cacheClearFailed:`Échec de l'effacement du cache :
{{error}}`,failed:`Échec de l'effacement des documents :
{{message}}`,error:`Échec de l'effacement des documents :
{{error}}`},deleteDocuments:{button:"Supprimer",tooltip:"Supprimer les documents sélectionnés",title:"Supprimer les documents",description:"Cette action supprimera définitivement les documents sélectionnés du système",warning:"ATTENTION : Cette action supprimera définitivement les documents sélectionnés et ne peut pas être annulée !",confirm:"Voulez-vous vraiment supprimer {{count}} document(s) sélectionné(s) ?",confirmPrompt:"Tapez 'yes' pour confirmer cette action",confirmPlaceholder:"Tapez yes pour confirmer",confirmButton:"OUI",deleteFileOption:"Supprimer également les fichiers téléchargés",deleteFileTooltip:"Cochez cette option pour supprimer également les fichiers téléchargés correspondants sur le serveur",success:"Pipeline de suppression de documents démarré avec succès",failed:`Échec de la suppression des documents :
{{message}}`,error:`Échec de la suppression des documents :
{{error}}`,busy:"Le pipeline est occupé, veuillez réessayer plus tard",notAllowed:"Aucune autorisation pour effectuer cette opération",cannotDeleteAll:"Impossible de supprimer tous les documents. Si vous devez supprimer tous les documents, veuillez utiliser la fonction Effacer les documents."},deselectDocuments:{button:"Désélectionner",tooltip:"Désélectionner tous les documents sélectionnés",title:"Désélectionner les documents",description:"Cette action effacera tous les documents sélectionnés ({{count}} sélectionnés)",confirmButton:"Tout désélectionner"},uploadDocuments:{button:"Télécharger",tooltip:"Télécharger des documents",title:"Télécharger des documents",description:"Glissez-déposez vos documents ici ou cliquez pour parcourir.",single:{uploading:"Téléchargement de {{name}} : {{percent}}%",success:`Succès du téléchargement :
{{name}} téléchargé avec succès`,failed:`Échec du téléchargement :
{{name}}
{{message}}`,error:`Échec du téléchargement :
{{name}}
{{error}}`},batch:{uploading:"Téléchargement des fichiers...",success:"Fichiers téléchargés avec succès",error:"Certains fichiers n'ont pas pu être téléchargés"},generalError:`Échec du téléchargement
{{error}}`,fileTypes:"Types pris en charge : TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"Impossible de télécharger plus d'un fichier à la fois",maxFilesLimit:"Impossible de télécharger plus de {{count}} fichiers",fileRejected:"Le fichier {{name}} a été rejeté",unsupportedType:"Type de fichier non pris en charge",fileTooLarge:"Fichier trop volumineux, taille maximale {{maxSize}}",dropHere:"Déposez les fichiers ici",dragAndDrop:"Glissez et déposez les fichiers ici, ou cliquez pour sélectionner",removeFile:"Supprimer le fichier",uploadDescription:"Vous pouvez télécharger {{isMultiple ? 'plusieurs' : count}} fichiers (jusqu'à {{maxSize}} chacun)",duplicateFile:"Le nom du fichier existe déjà dans le cache du serveur"}},documentManager:{title:"Gestion des documents",scanButton:"Scanner",scanTooltip:"Scanner les documents dans le dossier d'entrée",pipelineStatusButton:"État du Pipeline",pipelineStatusTooltip:"Voir l'état du pipeline",uploadedTitle:"Documents téléchargés",uploadedDescription:"Liste des documents téléchargés et leurs statuts.",emptyTitle:"Aucun document",emptyDescription:"Il n'y a pas encore de documents téléchargés.",columns:{id:"ID",summary:"Résumé",status:"Statut",length:"Longueur",chunks:"Fragments",created:"Créé",updated:"Mis à jour",metadata:"Métadonnées",select:"Sélectionner"},status:{all:"Tous",completed:"Terminé",processing:"En traitement",pending:"En attente",failed:"Échoué"},errors:{loadFailed:`Échec du chargement des documents
{{error}}`,scanFailed:`Échec de la numérisation des documents
{{error}}`,scanProgressFailed:`Échec de l'obtention de la progression de la numérisation
{{error}}`},fileNameLabel:"Nom du fichier",showButton:"Afficher",hideButton:"Masquer",showFileNameTooltip:"Afficher le nom du fichier",hideFileNameTooltip:"Masquer le nom du fichier"},pipelineStatus:{title:"État du Pipeline",busy:"Pipeline occupé",requestPending:"Requête en attente",jobName:"Nom du travail",startTime:"Heure de début",progress:"Progression",unit:"lot",latestMessage:"Dernier message",historyMessages:"Historique des messages",errors:{fetchFailed:`Échec de la récupération de l'état du pipeline
{{error}}`}}},uy={dataIsTruncated:"Les données du graphe sont tronquées au nombre maximum de nœuds",statusDialog:{title:"Paramètres du Serveur LightRAG",description:"Afficher l'état actuel du système et les informations de connexion"},legend:"Légende",nodeTypes:{person:"Personne",category:"Catégorie",geo:"Géographique",location:"Emplacement",organization:"Organisation",event:"Événement",equipment:"Équipement",weapon:"Arme",animal:"Animal",unknown:"Inconnu",object:"Objet",group:"Groupe",technology:"Technologie"},sideBar:{settings:{settings:"Paramètres",healthCheck:"Vérification de l'état",showPropertyPanel:"Afficher le panneau des propriétés",showSearchBar:"Afficher la barre de recherche",showNodeLabel:"Afficher l'étiquette du nœud",nodeDraggable:"Nœud déplaçable",showEdgeLabel:"Afficher l'étiquette de l'arête",hideUnselectedEdges:"Masquer les arêtes non sélectionnées",edgeEvents:"Événements des arêtes",maxQueryDepth:"Profondeur maximale de la requête",maxNodes:"Nombre maximum de nœuds",maxLayoutIterations:"Itérations maximales de mise en page",resetToDefault:"Réinitialiser par défaut",edgeSizeRange:"Plage de taille des arêtes",depth:"D",max:"Max",degree:"Degré",apiKey:"Clé API",enterYourAPIkey:"Entrez votre clé API",save:"Sauvegarder",refreshLayout:"Actualiser la mise en page"},zoomControl:{zoomIn:"Zoom avant",zoomOut:"Zoom arrière",resetZoom:"Réinitialiser le zoom",rotateCamera:"Rotation horaire",rotateCameraCounterClockwise:"Rotation antihoraire"},layoutsControl:{startAnimation:"Démarrer l'animation de mise en page",stopAnimation:"Arrêter l'animation de mise en page",layoutGraph:"Mettre en page le graphe",layouts:{Circular:"Circulaire",Circlepack:"Paquet circulaire",Random:"Aléatoire",Noverlaps:"Sans chevauchement","Force Directed":"Dirigé par la force","Force Atlas":"Atlas de force"}},fullScreenControl:{fullScreen:"Plein écran",windowed:"Fenêtré"},legendControl:{toggleLegend:"Basculer la légende"}},statusIndicator:{connected:"Connecté",disconnected:"Déconnecté"},statusCard:{unavailable:"Informations sur l'état indisponibles",storageInfo:"Informations de stockage",workingDirectory:"Répertoire de travail",inputDirectory:"Répertoire d'entrée",llmConfig:"Configuration du modèle de langage",llmBinding:"Liaison du modèle de langage",llmBindingHost:"Point de terminaison LLM",llmModel:"Modèle de langage",maxTokens:"Nombre maximum de jetons",embeddingConfig:"Configuration d'incorporation",embeddingBinding:"Liaison d'incorporation",embeddingBindingHost:"Point de terminaison d'incorporation",embeddingModel:"Modèle d'incorporation",storageConfig:"Configuration de stockage",kvStorage:"Stockage clé-valeur",docStatusStorage:"Stockage de l'état des documents",graphStorage:"Stockage du graphe",vectorStorage:"Stockage vectoriel",workspace:"Espace de travail",maxGraphNodes:"Nombre maximum de nœuds du graphe",rerankerConfig:"Configuration du reclassement",rerankerBindingHost:"Point de terminaison de reclassement",rerankerModel:"Modèle de reclassement",lockStatus:"État des verrous"},propertiesView:{editProperty:"Modifier {{property}}",editPropertyDescription:"Modifiez la valeur de la propriété dans la zone de texte ci-dessous.",errors:{duplicateName:"Le nom du nœud existe déjà",updateFailed:"Échec de la mise à jour du nœud",tryAgainLater:"Veuillez réessayer plus tard"},success:{entityUpdated:"Nœud mis à jour avec succès",relationUpdated:"Relation mise à jour avec succès"},node:{title:"Nœud",id:"ID",labels:"Étiquettes",degree:"Degré",properties:"Propriétés",relationships:"Relations(dans le sous-graphe)",expandNode:"Développer le nœud",pruneNode:"Élaguer le nœud",deleteAllNodesError:"Refus de supprimer tous les nœuds du graphe",nodesRemoved:"{{count}} nœuds supprimés, y compris les nœuds orphelins",noNewNodes:"Aucun nœud développable trouvé",propertyNames:{description:"Description",entity_id:"Nom",entity_type:"Type",source_id:"ID source",Neighbour:"Voisin",file_path:"Source",keywords:"Keys",weight:"Poids"}},edge:{title:"Relation",id:"ID",type:"Type",source:"Source",target:"Cible",properties:"Propriétés"}},search:{placeholder:"Rechercher des nœuds...",message:"Et {{count}} autres"},graphLabels:{selectTooltip:"Sélectionner l'étiquette de la requête",noLabels:"Aucune étiquette trouvée",label:"Étiquette",placeholder:"Rechercher des étiquettes...",andOthers:"Et {{count}} autres",refreshTooltip:"Recharger les données (Après l'ajout de fichier)"},emptyGraph:"Vide (Essayez de recharger)"},iy={chatMessage:{copyTooltip:"Copier dans le presse-papiers",copyError:"Échec de la copie du texte dans le presse-papiers"},retrieval:{startPrompt:"Démarrez une récupération en tapant votre requête ci-dessous",clear:"Effacer",send:"Envoyer",placeholder:"Tapez votre requête (Préfixe de requête : /<Query Mode>)",error:"Erreur : Échec de l'obtention de la réponse",queryModeError:"Seuls les modes de requête suivants sont pris en charge : {{modes}}",queryModePrefixInvalid:"Préfixe de mode de requête invalide. Utilisez : /<mode> [espace] votre requête"},querySettings:{parametersTitle:"Paramètres",parametersDescription:"Configurez vos paramètres de requête",queryMode:"Mode de requête",queryModeTooltip:`Sélectionnez la stratégie de récupération :
• Naïf : Recherche de base sans techniques avancées
• Local : Récupération d'informations dépendante du contexte
• Global : Utilise une base de connaissances globale
• Hybride : Combine récupération locale et globale
• Mixte : Intègre le graphe de connaissances avec la récupération vectorielle
• Bypass : Transmet directement la requête au LLM sans récupération`,queryModeOptions:{naive:"Naïf",local:"Local",global:"Global",hybrid:"Hybride",mix:"Mixte",bypass:"Bypass"},responseFormat:"Format de réponse",responseFormatTooltip:`Définit le format de la réponse. Exemples :
• Plusieurs paragraphes
• Paragraphe unique
• Points à puces`,responseFormatOptions:{multipleParagraphs:"Plusieurs paragraphes",singleParagraph:"Paragraphe unique",bulletPoints:"Points à puces"},topK:"Top K résultats",topKTooltip:"Nombre d'éléments supérieurs à récupérer. Représente les entités en mode 'local' et les relations en mode 'global'",topKPlaceholder:"Nombre de résultats",maxTokensTextUnit:"Nombre maximum de jetons pour l'unité de texte",maxTokensTextUnitTooltip:"Nombre maximum de jetons autorisés pour chaque fragment de texte récupéré",maxTokensGlobalContext:"Nombre maximum de jetons pour le contexte global",maxTokensGlobalContextTooltip:"Nombre maximum de jetons alloués pour les descriptions des relations dans la récupération globale",maxTokensLocalContext:"Nombre maximum de jetons pour le contexte local",maxTokensLocalContextTooltip:"Nombre maximum de jetons alloués pour les descriptions des entités dans la récupération locale",historyTurns:"Tours d'historique",historyTurnsTooltip:"Nombre de tours complets de conversation (paires utilisateur-assistant) à prendre en compte dans le contexte de la réponse",historyTurnsPlaceholder:"Nombre de tours d'historique",onlyNeedContext:"Besoin uniquement du contexte",onlyNeedContextTooltip:"Si vrai, ne renvoie que le contexte récupéré sans générer de réponse",onlyNeedPrompt:"Besoin uniquement de l'invite",onlyNeedPromptTooltip:"Si vrai, ne renvoie que l'invite générée sans produire de réponse",streamResponse:"Réponse en flux",streamResponseTooltip:"Si vrai, active la sortie en flux pour des réponses en temps réel",userPrompt:"Invite personnalisée",userPromptTooltip:"Fournir des exigences de réponse supplémentaires au LLM (sans rapport avec le contenu de la requête, uniquement pour le traitement de sortie).",userPromptPlaceholder:"Entrez une invite personnalisée (facultatif)"}},cy={loading:"Chargement de la documentation de l'API..."},sy={title:"Clé API requise",description:"Veuillez entrer votre clé API pour accéder au service",placeholder:"Entrez votre clé API",save:"Sauvegarder"},oy={settings:ey,header:ly,login:ty,common:ay,documentPanel:ny,graphPanel:uy,retrievePanel:iy,apiSite:cy,apiKeyAlert:sy},ry={language:"اللغة",theme:"السمة",light:"فاتح",dark:"داكن",system:"النظام"},fy={documents:"المستندات",knowledgeGraph:"شبكة المعرفة",retrieval:"الاسترجاع",api:"واجهة برمجة التطبيقات",projectRepository:"مستودع المشروع",logout:"تسجيل الخروج",themeToggle:{switchToLight:"التحويل إلى السمة الفاتحة",switchToDark:"التحويل إلى السمة الداكنة"}},dy={description:"الرجاء إدخال حسابك وكلمة المرور لتسجيل الدخول إلى النظام",username:"اسم المستخدم",usernamePlaceholder:"الرجاء إدخال اسم المستخدم",password:"كلمة المرور",passwordPlaceholder:"الرجاء إدخال كلمة المرور",loginButton:"تسجيل الدخول",loggingIn:"جاري تسجيل الدخول...",successMessage:"تم تسجيل الدخول بنجاح",errorEmptyFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",errorInvalidCredentials:"فشل تسجيل الدخول، يرجى التحقق من اسم المستخدم وكلمة المرور",authDisabled:"تم تعطيل المصادقة. استخدام وضع بدون تسجيل دخول.",guestMode:"وضع بدون تسجيل دخول"},my={cancel:"إلغاء",save:"حفظ",saving:"جارٍ الحفظ...",saveFailed:"فشل الحفظ"},hy={clearDocuments:{button:"مسح",tooltip:"مسح المستندات",title:"مسح المستندات",description:"سيؤدي هذا إلى إزالة جميع المستندات من النظام",warning:"تحذير: سيؤدي هذا الإجراء إلى حذف جميع المستندات بشكل دائم ولا يمكن التراجع عنه!",confirm:"هل تريد حقًا مسح جميع المستندات؟",confirmPrompt:"اكتب 'yes' لتأكيد هذا الإجراء",confirmPlaceholder:"اكتب yes للتأكيد",clearCache:"مسح كاش نموذج اللغة",confirmButton:"نعم",success:"تم مسح المستندات بنجاح",cacheCleared:"تم مسح ذاكرة التخزين المؤقت بنجاح",cacheClearFailed:`فشل مسح ذاكرة التخزين المؤقت:
{{error}}`,failed:`فشل مسح المستندات:
{{message}}`,error:`فشل مسح المستندات:
{{error}}`},deleteDocuments:{button:"حذف",tooltip:"حذف المستندات المحددة",title:"حذف المستندات",description:"سيؤدي هذا إلى حذف المستندات المحددة نهائيًا من النظام",warning:"تحذير: سيؤدي هذا الإجراء إلى حذف المستندات المحددة نهائيًا ولا يمكن التراجع عنه!",confirm:"هل تريد حقًا حذف {{count}} مستند(ات) محدد(ة)؟",confirmPrompt:"اكتب 'yes' لتأكيد هذا الإجراء",confirmPlaceholder:"اكتب yes للتأكيد",confirmButton:"نعم",deleteFileOption:"حذف الملفات المرفوعة أيضًا",deleteFileTooltip:"حدد هذا الخيار لحذف الملفات المرفوعة المقابلة على الخادم أيضًا",success:"تم بدء تشغيل خط معالجة حذف المستندات بنجاح",failed:`فشل حذف المستندات:
{{message}}`,error:`فشل حذف المستندات:
{{error}}`,busy:"خط المعالجة مشغول، يرجى المحاولة مرة أخرى لاحقًا",notAllowed:"لا توجد صلاحية لتنفيذ هذه العملية",cannotDeleteAll:"لا يمكن حذف جميع المستندات. إذا كنت بحاجة لحذف جميع المستندات، يرجى استخدام ميزة مسح المستندات."},deselectDocuments:{button:"إلغاء التحديد",tooltip:"إلغاء تحديد جميع المستندات المحددة",title:"إلغاء تحديد المستندات",description:"سيؤدي هذا إلى مسح جميع المستندات المحددة ({{count}} محدد)",confirmButton:"إلغاء تحديد الكل"},uploadDocuments:{button:"رفع",tooltip:"رفع المستندات",title:"رفع المستندات",description:"اسحب وأفلت مستنداتك هنا أو انقر للتصفح.",single:{uploading:"جارٍ الرفع {{name}}: {{percent}}%",success:`نجاح الرفع:
تم رفع {{name}} بنجاح`,failed:`فشل الرفع:
{{name}}
{{message}}`,error:`فشل الرفع:
{{name}}
{{error}}`},batch:{uploading:"جارٍ رفع الملفات...",success:"تم رفع الملفات بنجاح",error:"فشل رفع بعض الملفات"},generalError:`فشل الرفع
{{error}}`,fileTypes:"الأنواع المدعومة: TXT، MD، DOCX، PDF، PPTX، RTF، ODT، EPUB، HTML، HTM، TEX، JSON، XML، YAML، YML، CSV، LOG، CONF، INI، PROPERTIES، SQL، BAT، SH، C، CPP، PY، JAVA، JS، TS، SWIFT، GO، RB، PHP، CSS، SCSS، LESS",fileUploader:{singleFileLimit:"لا يمكن رفع أكثر من ملف واحد في المرة الواحدة",maxFilesLimit:"لا يمكن رفع أكثر من {{count}} ملفات",fileRejected:"تم رفض الملف {{name}}",unsupportedType:"نوع الملف غير مدعوم",fileTooLarge:"حجم الملف كبير جدًا، الحد الأقصى {{maxSize}}",dropHere:"أفلت الملفات هنا",dragAndDrop:"اسحب وأفلت الملفات هنا، أو انقر للاختيار",removeFile:"إزالة الملف",uploadDescription:"يمكنك رفع {{isMultiple ? 'عدة' : count}} ملفات (حتى {{maxSize}} لكل منها)",duplicateFile:"اسم الملف موجود بالفعل في ذاكرة التخزين المؤقت للخادم"}},documentManager:{title:"إدارة المستندات",scanButton:"مسح ضوئي",scanTooltip:"مسح المستندات ضوئيًا في مجلد الإدخال",pipelineStatusButton:"حالة خط المعالجة",pipelineStatusTooltip:"عرض حالة خط المعالجة",uploadedTitle:"المستندات المرفوعة",uploadedDescription:"قائمة المستندات المرفوعة وحالاتها.",emptyTitle:"لا توجد مستندات",emptyDescription:"لا توجد مستندات مرفوعة بعد.",columns:{id:"المعرف",summary:"الملخص",status:"الحالة",length:"الطول",chunks:"الأجزاء",created:"تم الإنشاء",updated:"تم التحديث",metadata:"البيانات الوصفية",select:"اختيار"},status:{all:"الكل",completed:"مكتمل",processing:"قيد المعالجة",pending:"معلق",failed:"فشل"},errors:{loadFailed:`فشل تحميل المستندات
{{error}}`,scanFailed:`فشل مسح المستندات
{{error}}`,scanProgressFailed:`فشل الحصول على تقدم المسح
{{error}}`},fileNameLabel:"اسم الملف",showButton:"عرض",hideButton:"إخفاء",showFileNameTooltip:"عرض اسم الملف",hideFileNameTooltip:"إخفاء اسم الملف"},pipelineStatus:{title:"حالة خط المعالجة",busy:"خط المعالجة مشغول",requestPending:"الطلب معلق",jobName:"اسم المهمة",startTime:"وقت البدء",progress:"التقدم",unit:"دفعة",latestMessage:"آخر رسالة",historyMessages:"سجل الرسائل",errors:{fetchFailed:`فشل في جلب حالة خط المعالجة
{{error}}`}}},gy={dataIsTruncated:"تم اقتصار بيانات الرسم البياني على الحد الأقصى للعقد",statusDialog:{title:"إعدادات خادم LightRAG",description:"عرض حالة النظام الحالية ومعلومات الاتصال"},legend:"المفتاح",nodeTypes:{person:"شخص",category:"فئة",geo:"كيان جغرافي",location:"موقع",organization:"منظمة",event:"حدث",equipment:"معدات",weapon:"سلاح",animal:"حيوان",unknown:"غير معروف",object:"مصنوع",group:"مجموعة",technology:"العلوم"},sideBar:{settings:{settings:"الإعدادات",healthCheck:"فحص الحالة",showPropertyPanel:"إظهار لوحة الخصائص",showSearchBar:"إظهار شريط البحث",showNodeLabel:"إظهار تسمية العقدة",nodeDraggable:"العقدة قابلة للسحب",showEdgeLabel:"إظهار تسمية الحافة",hideUnselectedEdges:"إخفاء الحواف غير المحددة",edgeEvents:"أحداث الحافة",maxQueryDepth:"أقصى عمق للاستعلام",maxNodes:"الحد الأقصى للعقد",maxLayoutIterations:"أقصى تكرارات التخطيط",resetToDefault:"إعادة التعيين إلى الافتراضي",edgeSizeRange:"نطاق حجم الحافة",depth:"D",max:"Max",degree:"الدرجة",apiKey:"مفتاح واجهة برمجة التطبيقات",enterYourAPIkey:"أدخل مفتاح واجهة برمجة التطبيقات الخاص بك",save:"حفظ",refreshLayout:"تحديث التخطيط"},zoomControl:{zoomIn:"تكبير",zoomOut:"تصغير",resetZoom:"إعادة تعيين التكبير",rotateCamera:"تدوير في اتجاه عقارب الساعة",rotateCameraCounterClockwise:"تدوير عكس اتجاه عقارب الساعة"},layoutsControl:{startAnimation:"بدء حركة التخطيط",stopAnimation:"إيقاف حركة التخطيط",layoutGraph:"تخطيط الرسم البياني",layouts:{Circular:"دائري",Circlepack:"حزمة دائرية",Random:"عشوائي",Noverlaps:"بدون تداخل","Force Directed":"موجه بالقوة","Force Atlas":"أطلس القوة"}},fullScreenControl:{fullScreen:"شاشة كاملة",windowed:"نوافذ"},legendControl:{toggleLegend:"تبديل المفتاح"}},statusIndicator:{connected:"متصل",disconnected:"غير متصل"},statusCard:{unavailable:"معلومات الحالة غير متوفرة",storageInfo:"معلومات التخزين",workingDirectory:"دليل العمل",inputDirectory:"دليل الإدخال",llmConfig:"تكوين نموذج اللغة الكبير",llmBinding:"ربط نموذج اللغة الكبير",llmBindingHost:"نقطة نهاية نموذج اللغة الكبير",llmModel:"نموذج اللغة الكبير",maxTokens:"أقصى عدد من الرموز",embeddingConfig:"تكوين التضمين",embeddingBinding:"ربط التضمين",embeddingBindingHost:"نقطة نهاية التضمين",embeddingModel:"نموذج التضمين",storageConfig:"تكوين التخزين",kvStorage:"تخزين المفتاح-القيمة",docStatusStorage:"تخزين حالة المستند",graphStorage:"تخزين الرسم البياني",vectorStorage:"تخزين المتجهات",workspace:"مساحة العمل",maxGraphNodes:"الحد الأقصى لعقد الرسم البياني",rerankerConfig:"تكوين إعادة الترتيب",rerankerBindingHost:"نقطة نهاية إعادة الترتيب",rerankerModel:"نموذج إعادة الترتيب",lockStatus:"حالة القفل"},propertiesView:{editProperty:"تعديل {{property}}",editPropertyDescription:"قم بتحرير قيمة الخاصية في منطقة النص أدناه.",errors:{duplicateName:"اسم العقدة موجود بالفعل",updateFailed:"فشل تحديث العقدة",tryAgainLater:"يرجى المحاولة مرة أخرى لاحقًا"},success:{entityUpdated:"تم تحديث العقدة بنجاح",relationUpdated:"تم تحديث العلاقة بنجاح"},node:{title:"عقدة",id:"المعرف",labels:"التسميات",degree:"الدرجة",properties:"الخصائص",relationships:"العلاقات (داخل الرسم الفرعي)",expandNode:"توسيع العقدة",pruneNode:"تقليم العقدة",deleteAllNodesError:"رفض حذف جميع العقد في الرسم البياني",nodesRemoved:"تم إزالة {{count}} عقدة، بما في ذلك العقد اليتيمة",noNewNodes:"لم يتم العثور على عقد قابلة للتوسيع",propertyNames:{description:"الوصف",entity_id:"الاسم",entity_type:"النوع",source_id:"معرف المصدر",Neighbour:"الجار",file_path:"المصدر",keywords:"الكلمات الرئيسية",weight:"الوزن"}},edge:{title:"علاقة",id:"المعرف",type:"النوع",source:"المصدر",target:"الهدف",properties:"الخصائص"}},search:{placeholder:"ابحث في العقد...",message:"و {{count}} آخرون"},graphLabels:{selectTooltip:"حدد تسمية الاستعلام",noLabels:"لم يتم العثور على تسميات",label:"التسمية",placeholder:"ابحث في التسميات...",andOthers:"و {{count}} آخرون",refreshTooltip:"إعادة تحميل البيانات (بعد إضافة الملف)"},emptyGraph:"فارغ (حاول إعادة التحميل)"},py={chatMessage:{copyTooltip:"نسخ إلى الحافظة",copyError:"فشل نسخ النص إلى الحافظة"},retrieval:{startPrompt:"ابدأ الاسترجاع بكتابة استفسارك أدناه",clear:"مسح",send:"إرسال",placeholder:"اكتب استفسارك (بادئة وضع الاستعلام: /<Query Mode>)",error:"خطأ: فشل الحصول على الرد",queryModeError:"يُسمح فقط بأنماط الاستعلام التالية: {{modes}}",queryModePrefixInvalid:"بادئة وضع الاستعلام غير صالحة. استخدم: /<الوضع> [مسافة] استفسارك"},querySettings:{parametersTitle:"المعلمات",parametersDescription:"تكوين معلمات الاستعلام الخاص بك",queryMode:"وضع الاستعلام",queryModeTooltip:`حدد استراتيجية الاسترجاع:
• ساذج: بحث أساسي بدون تقنيات متقدمة
• محلي: استرجاع معلومات يعتمد على السياق
• عالمي: يستخدم قاعدة المعرفة العالمية
• مختلط: يجمع بين الاسترجاع المحلي والعالمي
• مزيج: يدمج شبكة المعرفة مع الاسترجاع المتجهي
• تجاوز: يمرر الاستعلام مباشرة إلى LLM بدون استرجاع`,queryModeOptions:{naive:"ساذج",local:"محلي",global:"عالمي",hybrid:"مختلط",mix:"مزيج",bypass:"تجاوز"},responseFormat:"تنسيق الرد",responseFormatTooltip:`يحدد تنسيق الرد. أمثلة:
• فقرات متعددة
• فقرة واحدة
• نقاط نقطية`,responseFormatOptions:{multipleParagraphs:"فقرات متعددة",singleParagraph:"فقرة واحدة",bulletPoints:"نقاط نقطية"},topK:"أعلى K نتائج",topKTooltip:"عدد العناصر العلوية للاسترجاع. يمثل الكيانات في وضع 'محلي' والعلاقات في وضع 'عالمي'",topKPlaceholder:"عدد النتائج",maxTokensTextUnit:"أقصى عدد من الرموز لوحدة النص",maxTokensTextUnitTooltip:"الحد الأقصى لعدد الرموز المسموح به لكل جزء نصي مسترجع",maxTokensGlobalContext:"أقصى عدد من الرموز للسياق العالمي",maxTokensGlobalContextTooltip:"الحد الأقصى لعدد الرموز المخصص لأوصاف العلاقات في الاسترجاع العالمي",maxTokensLocalContext:"أقصى عدد من الرموز للسياق المحلي",maxTokensLocalContextTooltip:"الحد الأقصى لعدد الرموز المخصص لأوصاف الكيانات في الاسترجاع المحلي",historyTurns:"دورات التاريخ",historyTurnsTooltip:"عدد الدورات الكاملة للمحادثة (أزواج المستخدم-المساعد) التي يجب مراعاتها في سياق الرد",historyTurnsPlaceholder:"عدد دورات التاريخ",onlyNeedContext:"تحتاج فقط إلى السياق",onlyNeedContextTooltip:"إذا كان صحيحًا، يتم إرجاع السياق المسترجع فقط دون إنشاء رد",onlyNeedPrompt:"تحتاج فقط إلى المطالبة",onlyNeedPromptTooltip:"إذا كان صحيحًا، يتم إرجاع المطالبة المولدة فقط دون إنتاج رد",streamResponse:"تدفق الرد",streamResponseTooltip:"إذا كان صحيحًا، يتيح إخراج التدفق للردود في الوقت الفعلي",userPrompt:"مطالبة مخصصة",userPromptTooltip:"تقديم متطلبات استجابة إضافية إلى نموذج اللغة الكبير (غير متعلقة بمحتوى الاستعلام، فقط لمعالجة المخرجات).",userPromptPlaceholder:"أدخل مطالبة مخصصة (اختياري)"}},yy={loading:"جارٍ تحميل وثائق واجهة برمجة التطبيقات..."},vy={title:"مفتاح واجهة برمجة التطبيقات مطلوب",description:"الرجاء إدخال مفتاح واجهة برمجة التطبيقات للوصول إلى الخدمة",placeholder:"أدخل مفتاح واجهة برمجة التطبيقات",save:"حفظ"},by={settings:ry,header:fy,login:dy,common:my,documentPanel:hy,graphPanel:gy,retrievePanel:py,apiSite:yy,apiKeyAlert:vy},Sy={language:"語言",theme:"主題",light:"淺色",dark:"深色",system:"系統"},Ty={documents:"文件",knowledgeGraph:"知識圖譜",retrieval:"檢索",api:"API",projectRepository:"專案庫",logout:"登出",themeToggle:{switchToLight:"切換至淺色主題",switchToDark:"切換至深色主題"}},xy={description:"請輸入您的帳號和密碼登入系統",username:"帳號",usernamePlaceholder:"請輸入帳號",password:"密碼",passwordPlaceholder:"請輸入密碼",loginButton:"登入",loggingIn:"登入中...",successMessage:"登入成功",errorEmptyFields:"請輸入您的帳號和密碼",errorInvalidCredentials:"登入失敗，請檢查帳號和密碼",authDisabled:"認證已停用，使用免登入模式",guestMode:"免登入"},Ay={cancel:"取消",save:"儲存",saving:"儲存中...",saveFailed:"儲存失敗"},Dy={clearDocuments:{button:"清空",tooltip:"清空文件",title:"清空文件",description:"此操作將從系統中移除所有文件",warning:"警告：此操作將永久刪除所有文件，無法復原！",confirm:"確定要清空所有文件嗎？",confirmPrompt:"請輸入 yes 確認操作",confirmPlaceholder:"輸入 yes 以確認",clearCache:"清空 LLM 快取",confirmButton:"確定",success:"文件清空成功",cacheCleared:"快取清空成功",cacheClearFailed:`清空快取失敗：
{{error}}`,failed:`清空文件失敗：
{{message}}`,error:`清空文件失敗：
{{error}}`},deleteDocuments:{button:"刪除",tooltip:"刪除選取的文件",title:"刪除文件",description:"此操作將永久刪除選取的文件",warning:"警告：此操作將永久刪除選取的文件，無法復原！",confirm:"確定要刪除 {{count}} 個選取的文件嗎？",confirmPrompt:"請輸入 yes 確認操作",confirmPlaceholder:"輸入 yes 以確認",confirmButton:"確定",deleteFileOption:"同時刪除上傳檔案",deleteFileTooltip:"選取此選項將同時刪除伺服器上對應的上傳檔案",success:"文件刪除流水線啟動成功",failed:`刪除文件失敗：
{{message}}`,error:`刪除文件失敗：
{{error}}`,busy:"pipeline 被佔用，請稍後再試",notAllowed:"沒有操作權限",cannotDeleteAll:"無法刪除所有文件。如確實需要刪除所有文件請使用清空文件功能。"},deselectDocuments:{button:"取消選取",tooltip:"取消選取所有文件",title:"取消選取文件",description:"此操作將清除所有選取的文件（已選取 {{count}} 個）",confirmButton:"取消全部選取"},uploadDocuments:{button:"上傳",tooltip:"上傳文件",title:"上傳文件",description:"拖曳檔案至此處或點擊瀏覽",single:{uploading:"正在上傳 {{name}}：{{percent}}%",success:`上傳成功：
{{name}} 上傳完成`,failed:`上傳失敗：
{{name}}
{{message}}`,error:`上傳失敗：
{{name}}
{{error}}`},batch:{uploading:"正在上傳檔案...",success:"檔案上傳完成",error:"部分檔案上傳失敗"},generalError:`上傳失敗
{{error}}`,fileTypes:"支援的檔案類型：TXT, MD, DOCX, PDF, PPTX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS",fileUploader:{singleFileLimit:"一次只能上傳一個檔案",maxFilesLimit:"最多只能上傳 {{count}} 個檔案",fileRejected:"檔案 {{name}} 被拒絕",unsupportedType:"不支援的檔案類型",fileTooLarge:"檔案過大，最大允許 {{maxSize}}",dropHere:"將檔案拖放至此處",dragAndDrop:"拖放檔案至此處，或點擊選擇檔案",removeFile:"移除檔案",uploadDescription:"您可以上傳{{isMultiple ? '多個' : count}}個檔案（每個檔案最大{{maxSize}}）",duplicateFile:"檔案名稱與伺服器上的快取重複"}},documentManager:{title:"文件管理",scanButton:"掃描",scanTooltip:"掃描輸入目錄中的文件",pipelineStatusButton:"pipeline 狀態",pipelineStatusTooltip:"查看pipeline 狀態",uploadedTitle:"已上傳文件",uploadedDescription:"已上傳文件清單及其狀態",emptyTitle:"無文件",emptyDescription:"尚未上傳任何文件",columns:{id:"ID",summary:"摘要",status:"狀態",length:"長度",chunks:"分塊",created:"建立時間",updated:"更新時間",metadata:"元資料",select:"選擇"},status:{all:"全部",completed:"已完成",processing:"處理中",pending:"等待中",failed:"失敗"},errors:{loadFailed:`載入文件失敗
{{error}}`,scanFailed:`掃描文件失敗
{{error}}`,scanProgressFailed:`取得掃描進度失敗
{{error}}`},fileNameLabel:"檔案名稱",showButton:"顯示",hideButton:"隱藏",showFileNameTooltip:"顯示檔案名稱",hideFileNameTooltip:"隱藏檔案名稱"},pipelineStatus:{title:"pipeline 狀態",busy:"pipeline 忙碌中",requestPending:"待處理請求",jobName:"工作名稱",startTime:"開始時間",progress:"進度",unit:"梯次",latestMessage:"最新訊息",historyMessages:"歷史訊息",errors:{fetchFailed:`取得pipeline 狀態失敗
{{error}}`}}},Ny={dataIsTruncated:"圖資料已截斷至最大回傳節點數",statusDialog:{title:"LightRAG 伺服器設定",description:"查看目前系統狀態和連線資訊"},legend:"圖例",nodeTypes:{person:"人物角色",category:"分類",geo:"地理名稱",location:"位置",organization:"組織機構",event:"事件",equipment:"設備",weapon:"武器",animal:"動物",unknown:"未知",object:"物品",group:"群組",technology:"技術"},sideBar:{settings:{settings:"設定",healthCheck:"健康檢查",showPropertyPanel:"顯示屬性面板",showSearchBar:"顯示搜尋列",showNodeLabel:"顯示節點標籤",nodeDraggable:"節點可拖曳",showEdgeLabel:"顯示 Edge 標籤",hideUnselectedEdges:"隱藏未選取的 Edge",edgeEvents:"Edge 事件",maxQueryDepth:"最大查詢深度",maxNodes:"最大回傳節點數",maxLayoutIterations:"最大版面配置迭代次數",resetToDefault:"重設為預設值",edgeSizeRange:"Edge 粗細範圍",depth:"深度",max:"最大值",degree:"鄰邊",apiKey:"API key",enterYourAPIkey:"輸入您的 API key",save:"儲存",refreshLayout:"重新整理版面配置"},zoomControl:{zoomIn:"放大",zoomOut:"縮小",resetZoom:"重設縮放",rotateCamera:"順時針旋轉圖形",rotateCameraCounterClockwise:"逆時針旋轉圖形"},layoutsControl:{startAnimation:"繼續版面配置動畫",stopAnimation:"停止版面配置動畫",layoutGraph:"圖形版面配置",layouts:{Circular:"環形",Circlepack:"圓形打包",Random:"隨機",Noverlaps:"無重疊","Force Directed":"力導向","Force Atlas":"力圖"}},fullScreenControl:{fullScreen:"全螢幕",windowed:"視窗"},legendControl:{toggleLegend:"切換圖例顯示"}},statusIndicator:{connected:"已連線",disconnected:"未連線"},statusCard:{unavailable:"狀態資訊不可用",storageInfo:"儲存資訊",workingDirectory:"工作目錄",inputDirectory:"輸入目錄",llmConfig:"LLM 設定",llmBinding:"LLM 綁定",llmBindingHost:"LLM 端點",llmModel:"LLM 模型",maxTokens:"最大權杖數",embeddingConfig:"嵌入設定",embeddingBinding:"嵌入綁定",embeddingBindingHost:"嵌入端點",embeddingModel:"嵌入模型",storageConfig:"儲存設定",kvStorage:"KV 儲存",docStatusStorage:"文件狀態儲存",graphStorage:"圖形儲存",vectorStorage:"向量儲存",workspace:"工作空間",maxGraphNodes:"最大圖形節點數",rerankerConfig:"重排序設定",rerankerBindingHost:"重排序端點",rerankerModel:"重排序模型",lockStatus:"鎖定狀態"},propertiesView:{editProperty:"編輯{{property}}",editPropertyDescription:"在下方文字區域編輯屬性值。",errors:{duplicateName:"節點名稱已存在",updateFailed:"更新節點失敗",tryAgainLater:"請稍後重試"},success:{entityUpdated:"節點更新成功",relationUpdated:"關係更新成功"},node:{title:"節點",id:"ID",labels:"標籤",degree:"度數",properties:"屬性",relationships:"關係(子圖內)",expandNode:"展開節點",pruneNode:"修剪節點",deleteAllNodesError:"拒絕刪除圖中的所有節點",nodesRemoved:"已刪除 {{count}} 個節點，包括孤立節點",noNewNodes:"沒有發現可以展開的節點",propertyNames:{description:"描述",entity_id:"名稱",entity_type:"類型",source_id:"來源ID",Neighbour:"鄰接",file_path:"來源",keywords:"Keys",weight:"權重"}},edge:{title:"關係",id:"ID",type:"類型",source:"來源節點",target:"目標節點",properties:"屬性"}},search:{placeholder:"搜尋節點...",message:"還有 {count} 個"},graphLabels:{selectTooltip:"選擇查詢標籤",noLabels:"未找到標籤",label:"標籤",placeholder:"搜尋標籤...",andOthers:"還有 {count} 個",refreshTooltip:"重載圖形數據(新增檔案後需重載)"},emptyGraph:"無數據(請重載圖形數據)"},Ey={chatMessage:{copyTooltip:"複製到剪貼簿",copyError:"複製文字到剪貼簿失敗"},retrieval:{startPrompt:"輸入查詢開始檢索",clear:"清空",send:"送出",placeholder:"輸入查詢內容 (支援模式前綴：/<Query Mode>)",error:"錯誤：取得回應失敗",queryModeError:"僅支援以下查詢模式：{{modes}}",queryModePrefixInvalid:"無效的查詢模式前綴。請使用：/<模式> [空格] 查詢內容"},querySettings:{parametersTitle:"參數",parametersDescription:"設定查詢參數",queryMode:"查詢模式",queryModeTooltip:`選擇檢索策略：
• Naive：基礎搜尋，無進階技術
• Local：上下文相關資訊檢索
• Global：利用全域知識庫
• Hybrid：結合本地和全域檢索
• Mix：整合知識圖譜和向量檢索
• Bypass：直接傳遞查詢到LLM，不進行檢索`,queryModeOptions:{naive:"Naive",local:"Local",global:"Global",hybrid:"Hybrid",mix:"Mix",bypass:"Bypass"},responseFormat:"回應格式",responseFormatTooltip:`定義回應格式。例如：
• 多段落
• 單段落
• 重點`,responseFormatOptions:{multipleParagraphs:"多段落",singleParagraph:"單段落",bulletPoints:"重點"},topK:"Top K結果",topKTooltip:"檢索的前幾項結果數。在'local'模式下表示實體，在'global'模式下表示關係",topKPlaceholder:"結果數量",maxTokensTextUnit:"文字單元最大權杖數",maxTokensTextUnitTooltip:"每個檢索文字區塊允許的最大權杖數",maxTokensGlobalContext:"全域上下文最大權杖數",maxTokensGlobalContextTooltip:"全域檢索中關係描述的最大權杖數",maxTokensLocalContext:"本地上下文最大權杖數",maxTokensLocalContextTooltip:"本地檢索中實體描述的最大權杖數",historyTurns:"歷史輪次",historyTurnsTooltip:"回應上下文中考慮的完整對話輪次（使用者-助手對）數量",historyTurnsPlaceholder:"歷史輪次數",onlyNeedContext:"僅需上下文",onlyNeedContextTooltip:"如果為True，僅回傳檢索到的上下文而不產生回應",onlyNeedPrompt:"僅需提示",onlyNeedPromptTooltip:"如果為True，僅回傳產生的提示而不產生回應",streamResponse:"串流回應",streamResponseTooltip:"如果為True，啟用即時串流輸出回應",userPrompt:"用戶提示詞",userPromptTooltip:"向LLM提供額外的響應要求（與查詢內容無關，僅用於處理輸出）。",userPromptPlaceholder:"輸入自定義提示詞（可選）"}},My={loading:"正在載入 API 文件..."},zy={title:"需要 API key",description:"請輸入您的 API key 以存取服務",placeholder:"請輸入 API key",save:"儲存"},Cy={settings:Sy,header:Ty,login:xy,common:Ay,documentPanel:Dy,graphPanel:Ny,retrievePanel:Ey,apiSite:My,apiKeyAlert:zy},Oy=()=>{var h;try{const y=localStorage.getItem("settings-storage");if(y)return((h=JSON.parse(y).state)==null?void 0:h.language)||"en"}catch(y){console.error("Failed to get stored language:",y)}return"en"};cs.use(Pg).init({resources:{en:{translation:Vp},zh:{translation:Ip},fr:{translation:oy},ar:{translation:by},zh_TW:{translation:Cy}},lng:Oy(),fallbackLng:"en",interpolation:{escapeValue:!1},returnEmptyString:!1,returnNull:!1});we.subscribe(h=>{const y=h.language;cs.language!==y&&cs.changeLanguage(y)});ap.createRoot(document.getElementById("root")).render(o.jsx(E.StrictMode,{children:o.jsx(_p,{})}));
