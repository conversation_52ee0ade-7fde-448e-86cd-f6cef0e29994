"""
GuiXiaoxi 工具函数模块 - 简化版本
"""

import os
import json
import logging
import asyncio
import weakref
from functools import wraps
from typing import Any, Protocol, Callable, TYPE_CHECKING, List
import numpy as np
from dotenv import load_dotenv
from guixiaoxi.constants import (
    DEFAULT_LOG_MAX_BYTES,
    DEFAULT_LOG_BACKUP_COUNT,
    DEFAULT_LOG_FILENAME,
)


def get_env_value(key: str, default: Any = None) -> Any:
    """获取环境变量值"""
    return os.getenv(key, default)


def setup_logger(name: str, level: str = "INFO") -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, level.upper()))
    return logger


# 设置全局日志记录器
logger = setup_logger("guixiaoxi")


class QueueFullError(Exception):
    """Raised when the queue is full and the wait times out"""
    pass


def priority_limit_async_func_call(max_size: int, max_queue_size: int = 1000):
    """
    简化的异步函数调用装饰器
    
    Args:
        max_size: 最大并发数量 (暂时忽略)
        max_queue_size: 最大队列大小 (暂时忽略)
    Returns:
        装饰器函数
    """

    def final_decro(func):
        if not callable(func):
            raise TypeError(f"Expected a callable object, got {type(func)}")
        
        @wraps(func)
        async def wait_func(*args, **kwargs):
            """简化的等待函数，直接调用原函数"""
            try:
                # 直接调用原函数，添加超时保护
                timeout = kwargs.pop('_timeout', 120)  # 默认2分钟超时
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=timeout
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"Function {func.__name__} timed out after {timeout}s")
                raise
            except Exception as e:
                logger.error(f"Function {func.__name__} failed: {e}")
                raise
        
        # 添加兼容性方法
        wait_func.shutdown = lambda: None
        wait_func.__name__ = f"simplified_{func.__name__}"
        
        return wait_func
    
    return final_decro


def wrap_embedding_func_with_attrs(**kwargs):
    """Wrap a function with attributes"""

    def final_decro(func) -> 'EmbeddingFunc':
        new_func = EmbeddingFunc(**kwargs, func=func)
        return new_func

    return final_decro


def load_json(file_name):
    """加载JSON文件"""
    if not os.path.exists(file_name):
        return None
    with open(file_name, encoding="utf-8") as f:
        return json.load(f)


def write_json(json_obj, file_name):
    """写入JSON文件"""
    with open(file_name, "w", encoding="utf-8") as f:
        json.dump(json_obj, f, indent=2, ensure_ascii=False)


class TokenizerInterface(Protocol):
    """分词器接口"""
    def encode(self, text: str) -> List[int]:
        ...
    
    def decode(self, tokens: List[int]) -> str:
        ...


class EmbeddingFunc:
    """嵌入函数包装器"""
    
    def __init__(self, func: Callable = None, **kwargs):
        self.func = func
        self.kwargs = kwargs
    
    async def __call__(self, *args, **kwargs):
        if self.func:
            return await self.func(*args, **kwargs)
        raise NotImplementedError("No embedding function provided")


def compute_mdhash_id(content, prefix: str = ""):
    """计算MD5哈希ID"""
    from hashlib import md5
    return prefix + md5(content.encode()).hexdigest()


def locate_json_string_and_key_from_string(content: str, target_key: str):
    """从字符串中定位JSON字符串和键"""
    try:
        data = json.loads(content)
        if target_key in data:
            return content, data[target_key]
    except json.JSONDecodeError:
        pass
    return None, None


def truncate_list_by_token_size(list_data, key: str, max_token_size: int):
    """根据token大小截断列表"""
    if not list_data:
        return []
    
    result = []
    current_size = 0
    
    for item in list_data:
        item_size = len(str(item.get(key, "")))
        if current_size + item_size <= max_token_size:
            result.append(item)
            current_size += item_size
        else:
            break
    
    return result


def pack_user_ass_to_openai_messages(user: str, assistant: str):
    """打包用户和助手消息为OpenAI格式"""
    return [
        {"role": "user", "content": user},
        {"role": "assistant", "content": assistant}
    ]


def is_float_regex(value):
    """检查是否为浮点数"""
    try:
        float(value)
        return True
    except ValueError:
        return False


def encode_string_by_tiktoken(content: str, model_name: str = "gpt-4"):
    """使用tiktoken编码字符串"""
    try:
        import tiktoken
        encoding = tiktoken.encoding_for_model(model_name)
        return encoding.encode(content)
    except ImportError:
        # 如果没有tiktoken，使用简单的字符计数
        return list(content.encode('utf-8'))


def decode_tokens_by_tiktoken(tokens: List[int], model_name: str = "gpt-4"):
    """使用tiktoken解码tokens"""
    try:
        import tiktoken
        encoding = tiktoken.encoding_for_model(model_name)
        return encoding.decode(tokens)
    except ImportError:
        # 如果没有tiktoken，使用简单的字节解码
        return bytes(tokens).decode('utf-8', errors='ignore')


def num_tokens_from_string(string: str, model_name: str = "gpt-4") -> int:
    """计算字符串的token数量"""
    try:
        import tiktoken
        encoding = tiktoken.encoding_for_model(model_name)
        return len(encoding.encode(string))
    except ImportError:
        # 如果没有tiktoken，使用简单的字符计数估算
        return len(string) // 4  # 粗略估算


def split_string_by_multi_markers(content: str, markers: List[str]) -> List[str]:
    """使用多个标记分割字符串"""
    if not markers:
        return [content]
    
    result = [content]
    for marker in markers:
        new_result = []
        for part in result:
            new_result.extend(part.split(marker))
        result = new_result
    
    return [part.strip() for part in result if part.strip()]


def list_of_list_to_csv(data: List[List[str]], file_path: str):
    """将列表的列表保存为CSV文件"""
    import csv
    with open(file_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(data)


def csv_string_to_list(csv_string: str) -> List[List[str]]:
    """将CSV字符串转换为列表"""
    import csv
    import io
    reader = csv.reader(io.StringIO(csv_string))
    return list(reader)


def always_get_an_event_loop() -> asyncio.AbstractEventLoop:
    """总是获取一个事件循环"""
    try:
        return asyncio.get_running_loop()
    except RuntimeError:
        return asyncio.new_event_loop()


def clean_str(input_str: str) -> str:
    """清理字符串"""
    if not isinstance(input_str, str):
        return str(input_str)
    return input_str.strip().replace('\n', ' ').replace('\r', ' ')


def xml_to_json(xml_string: str) -> dict:
    """将XML字符串转换为JSON"""
    try:
        import xml.etree.ElementTree as ET
        root = ET.fromstring(xml_string)
        
        def element_to_dict(element):
            result = {}
            if element.text and element.text.strip():
                result['text'] = element.text.strip()
            for child in element:
                child_data = element_to_dict(child)
                if child.tag in result:
                    if not isinstance(result[child.tag], list):
                        result[child.tag] = [result[child.tag]]
                    result[child.tag].append(child_data)
                else:
                    result[child.tag] = child_data
            return result
        
        return {root.tag: element_to_dict(root)}
    except Exception as e:
        logger.error(f"XML to JSON conversion failed: {e}")
        return {}


def validate_storage_env_requirements(storage_name: str):
    """验证存储环境要求"""
    try:
        from guixiaoxi.kg import STORAGE_ENV_REQUIREMENTS
        
        required_vars = STORAGE_ENV_REQUIREMENTS.get(storage_name, [])
        missing_vars = [var for var in required_vars if var not in os.environ]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables for {storage_name}: {missing_vars}")
    except ImportError:
        logger.warning(f"Could not validate storage requirements for {storage_name}")
