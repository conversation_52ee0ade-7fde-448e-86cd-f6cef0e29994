#!/usr/bin/env python3
"""
GuiXiaoxi RAG 完整服务启动脚本 v3.0
支持API服务、Streamlit Web界面、测试套件、服务监控和自动恢复
"""

import os
import sys
import subprocess
import time
import requests
import argparse
import signal
import threading
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, List

# 配置常量
DEFAULT_API_HOST = "0.0.0.0"
DEFAULT_API_PORT = 8000
DEFAULT_WEB_PORT = 8501
HEALTH_CHECK_TIMEOUT = 5
STARTUP_TIMEOUT = 60
MONITOR_INTERVAL = 30

# 全局变量
api_process: Optional[subprocess.Popen] = None
web_process: Optional[subprocess.Popen] = None
monitor_thread: Optional[threading.Thread] = None
shutdown_event = threading.Event()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/service_launcher.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('service_launcher')


class ServiceManager:
    """服务管理器"""

    def __init__(self, api_host: str = DEFAULT_API_HOST, api_port: int = DEFAULT_API_PORT,
                 web_port: int = DEFAULT_WEB_PORT):
        self.api_host = api_host
        self.api_port = api_port
        self.web_port = web_port
        self.api_url = f"http://{api_host}:{api_port}"
        self.web_url = f"http://{api_host}:{web_port}"
        self.processes: Dict[str, subprocess.Popen] = {}
        self.start_time = datetime.now()

    def check_api_service(self) -> bool:
        """检查API服务状态"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=HEALTH_CHECK_TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                return data.get('success', False)
            return False
        except Exception as e:
            logger.debug(f"API健康检查失败: {e}")
            return False

    def is_api_running(self) -> bool:
        """检查API服务是否运行"""
        return self.check_api_service()

    def is_web_running(self) -> bool:
        """检查Web服务是否运行"""
        return self.check_web_service()

    def check_web_service(self) -> bool:
        """检查Web服务状态"""
        try:
            response = requests.get(f"{self.web_url}", timeout=HEALTH_CHECK_TIMEOUT)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"Web健康检查失败: {e}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        try:
            response = requests.get(f"{self.api_url}/config", timeout=HEALTH_CHECK_TIMEOUT)
            if response.status_code == 200:
                return response.json().get('data', {})
        except:
            pass
        return {}

    def get_knowledge_bases(self) -> List[Dict[str, Any]]:
        """获取知识库列表"""
        try:
            response = requests.get(f"{self.api_url}/knowledge-bases", timeout=HEALTH_CHECK_TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                # 处理新的API响应格式
                if 'knowledge_bases' in data:
                    return data['knowledge_bases']
                elif 'data' in data and 'knowledge_bases' in data['data']:
                    return data['data']['knowledge_bases']
        except:
            pass
        return []


def start_api_service(manager: ServiceManager, background: bool = False) -> bool:
    """启动API服务"""
    global api_process

    logger.info("🚀 启动 GuiXiaoxi RAG API 服务...")
    print("🚀 启动 GuiXiaoxi RAG API 服务...")

    # 检查是否已经运行
    if manager.check_api_service():
        logger.info("✅ API 服务已经在运行")
        print("✅ API 服务已经在运行")
        return True

    try:
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 启动API服务
        current_dir = Path(__file__).parent
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        cmd = [
            sys.executable,
            "start_server.py",
            "--host", manager.api_host,
            "--port", str(manager.api_port)
        ]

        if background:
            # 后台启动，重定向输出到日志文件
            api_log_file = log_dir / "api_service.log"
            with open(api_log_file, 'a', encoding='utf-8') as log_file:
                api_process = subprocess.Popen(
                    cmd,
                    cwd=current_dir,
                    env=env,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    start_new_session=True
                )
        else:
            # 前台启动
            api_process = subprocess.Popen(
                cmd,
                cwd=current_dir,
                env=env
            )

        manager.processes['api'] = api_process

        # 等待服务启动
        logger.info("⏳ 等待 API 服务启动...")
        print("⏳ 等待 API 服务启动...")

        for i in range(STARTUP_TIMEOUT):
            time.sleep(1)
            if manager.check_api_service():
                logger.info("✅ API 服务启动成功")
                print("✅ API 服务启动成功")
                return True

            if i % 5 == 0:  # 每5秒显示一次进度
                print(f"   等待中... ({i+1}/{STARTUP_TIMEOUT})")

        logger.error("❌ API 服务启动超时")
        print("❌ API 服务启动超时")
        return False

    except Exception as e:
        logger.error(f"❌ API 服务启动失败: {e}")
        print(f"❌ API 服务启动失败: {e}")
        return False


def start_web_service(manager: ServiceManager, background: bool = False) -> bool:
    """启动Streamlit Web服务"""
    global web_process

    logger.info("🎨 启动 Streamlit Web 界面...")
    print("🎨 启动 Streamlit Web 界面...")

    try:
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 添加路径
        current_dir = Path(__file__).parent
        env = os.environ.copy()
        env['PYTHONPATH'] = str(current_dir)

        # 设置Streamlit配置环境变量
        env['STREAMLIT_SERVER_HEADLESS'] = 'true'
        env['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
        env['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
        env['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'

        # 检查streamlit应用文件，优先使用最小化版本进行测试
        minimal_app_path = current_dir / "server" / "web" / "minimal_app.py"
        simple_app_path = current_dir / "server" / "web" / "simple_streamlit_app.py"
        streamlit_app_path = current_dir / "server" / "web" / "streamlit_app.py"

        if minimal_app_path.exists():
            app_path = minimal_app_path
            logger.info("使用最小化Streamlit应用")
        elif simple_app_path.exists():
            app_path = simple_app_path
            logger.info("使用简化版Streamlit应用")
        elif streamlit_app_path.exists():
            app_path = streamlit_app_path
            logger.info("使用完整版Streamlit应用")
        else:
            logger.error(f"❌ 未找到Streamlit应用文件")
            print(f"❌ 未找到Streamlit应用文件")
            return False

        cmd = [
            sys.executable,
            "-m", "streamlit",
            "run",
            str(app_path),
            "--server.port", str(manager.web_port),
            "--server.address", manager.api_host,
            "--browser.gatherUsageStats", "false",
            "--logger.level", "warning"
        ]

        # 只在后台模式下添加headless参数
        if background:
            cmd.extend(["--server.headless", "true"])

        logger.info(f"启动命令: {' '.join(cmd)}")

        if background:
            # 后台启动，重定向输出到日志文件
            web_log_file = log_dir / "web_service.log"
            logger.info(f"Web服务日志: {web_log_file}")

            with open(web_log_file, 'w', encoding='utf-8') as log_file:
                log_file.write(f"=== Streamlit Web Service Log - {datetime.now()} ===\n")
                log_file.flush()

                web_process = subprocess.Popen(
                    cmd,
                    cwd=current_dir,
                    env=env,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    start_new_session=True
                )
        else:
            # 前台启动
            web_process = subprocess.Popen(
                cmd,
                cwd=current_dir,
                env=env
            )

        manager.processes['web'] = web_process
        logger.info(f"Web进程已启动，PID: {web_process.pid}")

        if background:
            # 等待服务启动
            logger.info("⏳ 等待 Web 服务启动...")
            print("⏳ 等待 Web 服务启动...")

            for i in range(60):  # 最多等待60秒
                time.sleep(1)

                # 检查进程是否还在运行
                if web_process.poll() is not None:
                    logger.error(f"❌ Web进程异常退出，返回码: {web_process.returncode}")
                    print(f"❌ Web进程异常退出，返回码: {web_process.returncode}")
                    return False

                if manager.check_web_service():
                    logger.info("✅ Web 服务启动成功")
                    print("✅ Web 服务启动成功")
                    return True

                if i % 10 == 0 and i > 0:
                    print(f"   等待中... ({i+1}/60)")

            logger.warning("⚠️ Web 服务启动检查超时，但进程仍在运行")
            print("⚠️ Web 服务启动检查超时，但进程仍在运行")
            return True
        else:
            # 前台模式，等待一下确保启动
            time.sleep(2)
            return True

    except KeyboardInterrupt:
        logger.info("👋 Web 服务被用户停止")
        print("\n👋 Web 服务被用户停止")
        return False
    except Exception as e:
        logger.error(f"❌ Web 服务启动失败: {e}")
        print(f"❌ Web 服务启动失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def monitor_services(manager: ServiceManager):
    """监控服务状态"""
    global monitor_thread

    def monitor_loop():
        logger.info("🔍 启动服务监控...")

        while not shutdown_event.is_set():
            try:
                # 检查API服务
                api_status = manager.check_api_service()
                if 'api' in manager.processes and not api_status:
                    logger.warning("⚠️ API服务异常，尝试重启...")
                    if start_api_service(manager, background=True):
                        logger.info("✅ API服务重启成功")
                    else:
                        logger.error("❌ API服务重启失败")

                # 检查Web服务
                if 'web' in manager.processes:
                    web_process = manager.processes['web']
                    if web_process.poll() is not None:  # 进程已结束
                        logger.warning("⚠️ Web服务进程已结束")

                # 等待下次检查
                shutdown_event.wait(MONITOR_INTERVAL)

            except Exception as e:
                logger.error(f"监控服务异常: {e}")
                shutdown_event.wait(MONITOR_INTERVAL)

    monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
    monitor_thread.start()


def cleanup_services(manager: ServiceManager):
    """清理服务进程"""
    global shutdown_event

    logger.info("🧹 清理服务进程...")
    shutdown_event.set()

    for service_name, process in manager.processes.items():
        try:
            if process and process.poll() is None:
                logger.info(f"停止 {service_name} 服务...")
                print(f"停止 {service_name} 服务...")

                # 首先尝试优雅关闭
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=10)
                    logger.info(f"✅ {service_name} 服务已停止")
                    print(f"✅ {service_name} 服务已停止")
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ {service_name} 服务强制终止")
                    print(f"⚠️ {service_name} 服务强制终止")
                    process.kill()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {service_name} 服务无法终止")
                        print(f"❌ {service_name} 服务无法终止")
            else:
                if process:
                    logger.info(f"✅ {service_name} 服务已经停止")
        except Exception as e:
            logger.error(f"清理 {service_name} 服务失败: {e}")
            print(f"❌ 清理 {service_name} 服务失败: {e}")


def run_tests(test_type: str = "all") -> bool:
    """运行测试套件"""
    print(f"🧪 开始运行测试: {test_type}")

    try:
        # 测试脚本路径
        test_script = Path(__file__).parent / "server" / "tests" / "run_tests.py"
        if not test_script.exists():
            print(f"❌ 测试脚本不存在: {test_script}")
            return False

        cmd = [sys.executable, str(test_script)]

        if test_type == "api":
            cmd.append("--api-only")
        elif test_type == "performance":
            cmd.append("--perf-only")
        elif test_type == "streamlit":
            cmd.append("--streamlit-only")

        # 添加JSON输出
        cmd.append("--save-json")

        print(f"执行命令: {' '.join(cmd)}")

        result = subprocess.run(
            cmd,
            timeout=300  # 5分钟超时
        )

        if result.returncode == 0:
            print("✅ 测试执行成功")
            return True
        else:
            print("❌ 测试执行失败")
            return False

    except subprocess.TimeoutExpired:
        print("❌ 测试执行超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def signal_handler(signum, frame):
    """信号处理器"""
    global shutdown_event

    logger.info(f"收到信号 {signum}，准备退出...")
    print(f"\n收到信号 {signum}，准备退出...")

    # 设置关闭事件
    shutdown_event.set()

    # 不直接退出，让主程序处理清理


def display_service_status(manager: ServiceManager):
    """显示服务状态"""
    print("\n" + "=" * 60)
    print("📊 服务状态检查")
    print("=" * 60)

    # API服务状态
    api_status = manager.check_api_service()
    api_icon = "✅" if api_status else "❌"
    print(f"{api_icon} API 服务: {manager.api_url}")

    if api_status:
        # 获取服务信息
        service_info = manager.get_service_info()
        if service_info:
            print(f"   📚 LLM模型: {service_info.get('llm', {}).get('model', 'Unknown')}")
            print(f"   🔍 嵌入模型: {service_info.get('embedding', {}).get('model', 'Unknown')}")
            print(f"   🌊 流式输出: {'启用' if service_info.get('rag', {}).get('enable_stream') else '禁用'}")

        # 获取知识库信息
        knowledge_bases = manager.get_knowledge_bases()
        enabled_kbs = [kb for kb in knowledge_bases if kb.get('enabled', False)]
        print(f"   📖 知识库: {len(enabled_kbs)}/{len(knowledge_bases)} 个启用")

    # Web服务状态
    web_status = manager.check_web_service()
    web_icon = "✅" if web_status else "❌"
    print(f"{web_icon} Web 界面: {manager.web_url}")

    print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="GuiXiaoxi RAG 完整服务启动器 v3.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_streamlit.py                    # 启动完整服务
  python start_streamlit.py --api-only        # 只启动API服务
  python start_streamlit.py --web-only        # 只启动Web界面
  python start_streamlit.py --background      # 后台启动所有服务
  python start_streamlit.py --monitor         # 启动服务监控
  python start_streamlit.py --test all        # 启动服务并运行所有测试
  python start_streamlit.py --test-only api   # 只运行API测试
  python start_streamlit.py --status          # 显示服务状态
        """
    )

    # 服务控制参数
    parser.add_argument("--api-only", action="store_true", help="只启动 API 服务")
    parser.add_argument("--web-only", action="store_true", help="只启动 Web 界面")
    parser.add_argument("--background", action="store_true", help="后台启动服务")
    parser.add_argument("--monitor", action="store_true", help="启用服务监控")

    # 网络配置参数
    parser.add_argument("--api-host", default=DEFAULT_API_HOST, help=f"API服务主机 (默认: {DEFAULT_API_HOST})")
    parser.add_argument("--api-port", type=int, default=DEFAULT_API_PORT, help=f"API服务端口 (默认: {DEFAULT_API_PORT})")
    parser.add_argument("--web-port", type=int, default=DEFAULT_WEB_PORT, help=f"Web服务端口 (默认: {DEFAULT_WEB_PORT})")

    # 测试参数
    parser.add_argument("--test", choices=["all", "api", "performance", "streamlit"], help="运行测试套件")
    parser.add_argument("--test-only", choices=["all", "api", "performance", "streamlit"], help="只运行测试，不启动服务")

    # 其他参数
    parser.add_argument("--skip-api-check", action="store_true", help="跳过 API 服务检查")
    parser.add_argument("--status", action="store_true", help="显示服务状态")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建服务管理器
    manager = ServiceManager(args.api_host, args.api_port, args.web_port)

    print("🤖 GuiXiaoxi RAG 完整服务启动器 v3.0")
    print("=" * 60)
    print(f"📅 启动时间: {manager.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 API 地址: {manager.api_url}")
    print(f"🎨 Web 地址: {manager.web_url}")
    print("=" * 60)

    # 检查项目结构
    current_dir = Path(__file__).parent
    if not (current_dir / "server").exists():
        logger.error("❌ server 目录不存在，请检查项目结构")
        print("❌ server 目录不存在，请检查项目结构")
        sys.exit(1)

    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    try:
        if args.status:
            # 只显示服务状态
            display_service_status(manager)
            return

        if args.test_only:
            # 只运行测试
            print("🧪 测试模式: 只运行测试")
            test_type = args.test_only

            # 检查API服务是否运行
            if not manager.is_api_running():
                print("⚠️ API服务未运行，正在启动...")
                if not start_api_service(manager, background=True):
                    print("❌ 无法启动API服务，测试终止")
                    sys.exit(1)
                time.sleep(5)  # 等待服务完全启动

            # 运行测试
            success = run_tests(test_type)
            sys.exit(0 if success else 1)

        if args.test:
            # 运行测试（在服务启动后）
            print(f"🧪 将在服务启动后运行测试: {args.test}")

        if args.web_only:
            # 只启动 Web 界面
            logger.info("启动模式: 仅Web服务")
            print("🎯 启动模式: 仅Web服务")

            if start_web_service(manager, background=args.background):
                if not args.background:
                    print("\n按 Ctrl+C 停止服务")
                    try:
                        manager.processes['web'].wait()
                    except KeyboardInterrupt:
                        pass
            return

        if args.api_only:
            # 只启动 API 服务
            logger.info("启动模式: 仅API服务")
            print("🎯 启动模式: 仅API服务")

            if start_api_service(manager, background=args.background):
                if not args.background:
                    print("\n按 Ctrl+C 停止服务")
                    try:
                        manager.processes['api'].wait()
                    except KeyboardInterrupt:
                        pass
                else:
                    print("✅ API 服务已在后台启动")
            else:
                logger.error("❌ API 服务启动失败")
                print("❌ API 服务启动失败")
                sys.exit(1)
            return

        # 启动完整服务
        logger.info("启动模式: 完整服务")
        print("🎯 启动模式: 完整服务")

        success = True

        # 启动 API 服务
        if not args.skip_api_check:
            if not start_api_service(manager, background=args.background):
                logger.error("❌ API 服务启动失败，无法继续")
                print("❌ API 服务启动失败，无法继续")
                sys.exit(1)

        # 启动 Web 服务
        if not start_web_service(manager, background=args.background):
            logger.warning("⚠️ Web 服务启动失败，但API服务正常")
            print("⚠️ Web 服务启动失败，但API服务正常")
            success = False

        # 启动监控（如果需要）
        if args.monitor and args.background:
            monitor_services(manager)

        # 显示服务状态
        display_service_status(manager)

        # 运行测试（如果指定）
        if args.test:
            print(f"\n🧪 开始运行测试: {args.test}")
            test_success = run_tests(args.test)
            if test_success:
                print("✅ 测试完成")
            else:
                print("❌ 测试失败")

        if args.background:
            print("\n🎉 所有服务已在后台启动！")
            print("💡 使用 --status 参数查看服务状态")
            print("💡 查看日志: tail -f logs/*.log")
            print("💡 使用 Ctrl+C 停止所有服务")

            # 后台模式下等待用户中断或关键进程结束
            try:
                while not shutdown_event.is_set():
                    time.sleep(1)
                    # 检查关键进程是否还在运行
                    running_processes = []
                    critical_processes = []

                    for name, process in manager.processes.items():
                        if process and process.poll() is None:
                            running_processes.append(name)
                            # API服务是关键进程
                            if name == 'api':
                                critical_processes.append(name)

                    # 只有当关键进程都停止时才退出
                    # 如果启动了API服务，则API服务是关键进程
                    # 如果只启动了Web服务，则Web服务是关键进程
                    if args.web_only:
                        # 仅Web模式下，Web服务是关键进程
                        if 'web' not in running_processes:
                            logger.warning("Web服务已结束")
                            print("⚠️ Web服务已结束")
                            break
                    elif not critical_processes:
                        logger.warning("关键进程（API服务）已结束")
                        print("⚠️ 关键进程（API服务）已结束")
                        break

                    # 如果只有Web服务停止，给出警告但继续运行
                    if 'api' in running_processes and 'web' not in running_processes:
                        if not hasattr(manager, '_web_warning_shown'):
                            logger.warning("Web服务已停止，但API服务继续运行")
                            print("⚠️ Web服务已停止，但API服务继续运行")
                            manager._web_warning_shown = True

            except KeyboardInterrupt:
                print("\n👋 用户中断，正在停止服务...")
                shutdown_event.set()
        else:
            print("\n🎉 服务启动完成！")
            print(f"📚 API 文档: http://{manager.api_host}:{manager.api_port}/docs")
            print("=" * 60)
            print("按 Ctrl+C 停止服务")
            print()

            # 前台模式等待用户中断
            try:
                if 'web' in manager.processes:
                    # 等待Web进程或用户中断
                    while not shutdown_event.is_set():
                        if manager.processes['web'].poll() is not None:
                            break
                        time.sleep(0.1)
                elif 'api' in manager.processes:
                    # 等待API进程或用户中断
                    while not shutdown_event.is_set():
                        if manager.processes['api'].poll() is not None:
                            break
                        time.sleep(0.1)
                else:
                    # 等待用户中断
                    while not shutdown_event.is_set():
                        time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 用户中断，正在停止服务...")
                shutdown_event.set()

    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

    finally:
        # 清理服务
        cleanup_services(manager)


if __name__ == "__main__":
    main()
