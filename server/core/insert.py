"""
GuiXiaoxi RAG 增强插入模块
支持多知识库文档插入和管理
"""

import os
from pathlib import Path
import shutil
import asyncio
import time
import logging
from typing import List, Optional, Dict, Any, AsyncGenerator
import json

import docx2txt
import textract
import zipfile
import xml.etree.ElementTree as ET
import numpy as np

from guixiaoxi import GuiXiaoxiRAG
from guixiaoxi.llm.openai import openai_embed, openai_complete_if_cache
from guixiaoxi.kg.shared_storage import initialize_pipeline_status
from guixiaoxi.utils import setup_logger, EmbeddingFunc
from server.config.config import config_manager

# 设置日志
setup_logger("guixiaoxi", level="INFO")
logger = logging.getLogger("guixiaoxi")


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.supported_formats = {
            '.txt': self._process_txt,
            '.md': self._process_txt,
            '.docx': self._process_docx,
            '.pdf': self._process_pdf,
            '.json': self._process_json,
            '.xml': self._process_xml,
            '.zip': self._process_zip
        }
    
    def can_process(self, file_path: str) -> bool:
        """检查是否支持处理该文件格式"""
        suffix = Path(file_path).suffix.lower()
        return suffix in self.supported_formats
    
    def process_file(self, file_path: str) -> str:
        """处理文件并返回文本内容"""
        suffix = Path(file_path).suffix.lower()
        
        if suffix not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {suffix}")
        
        try:
            return self.supported_formats[suffix](file_path)
        except Exception as e:
            logger.error(f"处理文件 {file_path} 失败: {e}")
            raise
    
    def _process_txt(self, file_path: str) -> str:
        """处理文本文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _process_docx(self, file_path: str) -> str:
        """处理Word文档"""
        try:
            return docx2txt.process(file_path)
        except Exception as e:
            logger.warning(f"使用docx2txt处理失败，尝试textract: {e}")
            return textract.process(file_path).decode('utf-8')
    
    def _process_pdf(self, file_path: str) -> str:
        """处理PDF文件"""
        return textract.process(file_path).decode('utf-8')
    
    def _process_json(self, file_path: str) -> str:
        """处理JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _process_xml(self, file_path: str) -> str:
        """处理XML文件"""
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        def extract_text(element):
            text = element.text or ""
            for child in element:
                text += extract_text(child)
            return text
        
        return extract_text(root)
    
    def _process_zip(self, file_path: str) -> str:
        """处理ZIP文件"""
        texts = []
        
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            for file_info in zip_file.filelist:
                if file_info.is_dir():
                    continue
                
                file_suffix = Path(file_info.filename).suffix.lower()
                if file_suffix in ['.txt', '.md', '.json', '.xml']:
                    try:
                        content = zip_file.read(file_info.filename).decode('utf-8')
                        texts.append(f"=== {file_info.filename} ===\n{content}\n")
                    except Exception as e:
                        logger.warning(f"处理ZIP中的文件 {file_info.filename} 失败: {e}")
        
        return "\n".join(texts)


class FileManager:
    """文件管理器"""
    
    def __init__(self, temp_dir: str = "temp_uploads"):
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
    
    def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        file_path = self.temp_dir / filename
        
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        return str(file_path)
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        current_time = time.time()
        
        for file_path in self.temp_dir.iterdir():
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_hours * 3600:
                    try:
                        file_path.unlink()
                        logger.info(f"清理临时文件: {file_path}")
                    except Exception as e:
                        logger.error(f"清理临时文件失败 {file_path}: {e}")


class GraphDataConverter:
    """图数据转换器"""
    
    def __init__(self, rag_instance: GuiXiaoxiRAG):
        self.rag = rag_instance
    
    def export_graph_data(self) -> Dict[str, Any]:
        """导出图数据"""
        try:
            # 获取图数据
            graph_data = self.rag.chunk_entity_relation_graph
            
            if not graph_data:
                return {"nodes": [], "edges": [], "stats": {"nodes": 0, "edges": 0}}
            
            # 转换为可视化格式
            nodes = []
            edges = []
            
            # 处理节点
            for node_id, node_data in graph_data.nodes(data=True):
                nodes.append({
                    "id": str(node_id),
                    "label": str(node_data.get("label", node_id)),
                    "type": node_data.get("type", "unknown"),
                    "properties": node_data
                })
            
            # 处理边
            for source, target, edge_data in graph_data.edges(data=True):
                edges.append({
                    "source": str(source),
                    "target": str(target),
                    "label": edge_data.get("label", ""),
                    "weight": edge_data.get("weight", 1.0),
                    "properties": edge_data
                })
            
            return {
                "nodes": nodes,
                "edges": edges,
                "stats": {
                    "nodes": len(nodes),
                    "edges": len(edges)
                }
            }
            
        except Exception as e:
            logger.error(f"导出图数据失败: {e}")
            return {"nodes": [], "edges": [], "error": str(e)}


class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self):
        self.rag_instances: Dict[str, GuiXiaoxiRAG] = {}
        self.document_processor = DocumentProcessor()
        self.file_manager = FileManager(config_manager.api.temp_dir)
        # 不在初始化时创建RAG实例，等待Shared-Data初始化后再创建

    def _initialize_knowledge_bases(self):
        """初始化知识库实例"""
        enabled_kbs = config_manager.get_enabled_knowledge_bases()

        for kb_id, kb_config in enabled_kbs.items():
            if kb_id in self.rag_instances:
                continue  # 已经初始化过的跳过

            try:
                # 确保工作目录存在
                os.makedirs(kb_config.working_dir, exist_ok=True)

                # 创建RAG实例
                rag = GuiXiaoxiRAG(
                    working_dir=kb_config.working_dir,
                    embedding_func=EmbeddingFunc(
                        embedding_dim=config_manager.embedding.embedding_dim,
                        max_token_size=config_manager.embedding.max_token_size,
                        func=lambda texts: openai_embed(
                            texts,
                            model=config_manager.embedding.model,
                            base_url=config_manager.embedding.api_base,
                            api_key=config_manager.embedding.api_key
                        )
                    ),
                    llm_model_func=openai_complete_if_cache,
                    llm_model_name=config_manager.llm.model,
                    llm_model_max_token_size=config_manager.llm.max_token_size,
                    llm_model_kwargs={
                        "temperature": config_manager.llm.temperature,
                        "max_tokens": config_manager.llm.max_tokens
                    }
                )

                self.rag_instances[kb_id] = rag
                logger.info(f"知识库 {kb_id} 初始化成功")

            except Exception as e:
                logger.error(f"初始化知识库 {kb_id} 失败: {e}")
        
    def get_rag_instance(self, kb_id: str) -> Optional[GuiXiaoxiRAG]:
        """获取RAG实例"""
        if kb_id not in self.rag_instances:
            kb_config = config_manager.knowledge_bases.get(kb_id)
            if not kb_config or not kb_config.enabled:
                return None
            
            try:
                # 创建RAG实例
                rag = GuiXiaoxiRAG(
                    working_dir=kb_config.working_dir,
                    embedding_func=EmbeddingFunc(
                        embedding_dim=config_manager.embedding.embedding_dim,
                        max_token_size=config_manager.embedding.max_token_size,
                        func=lambda texts: openai_embed(
                            texts,
                            model=config_manager.embedding.model,
                            base_url=config_manager.embedding.api_base,
                            api_key=config_manager.embedding.api_key
                        )
                    ),
                    llm_model_func=openai_complete_if_cache,
                    llm_model_name=config_manager.llm.model,
                    llm_model_max_token_size=config_manager.llm.max_token_size,
                    llm_model_kwargs={
                        "temperature": config_manager.llm.temperature,
                        "max_tokens": config_manager.llm.max_tokens
                    }
                )

                # 异步初始化存储和管道状态
                asyncio.create_task(self._async_initialize_rag(rag, kb_id))

                self.rag_instances[kb_id] = rag
                logger.info(f"RAG实例 {kb_id} 创建成功，正在异步初始化...")

            except Exception as e:
                logger.error(f"创建RAG实例 {kb_id} 失败: {e}")
                return None
        
        return self.rag_instances.get(kb_id)

    async def _async_initialize_rag(self, rag: GuiXiaoxiRAG, kb_id: str):
        """异步初始化RAG实例"""
        try:
            # 初始化存储
            await rag.initialize_storages()
            # 初始化管道状态
            await initialize_pipeline_status()
            logger.info(f"RAG实例 {kb_id} 异步初始化完成")
        except Exception as e:
            logger.error(f"RAG实例 {kb_id} 异步初始化失败: {e}")

    def reload_knowledge_base(self, kb_id: str) -> bool:
        """重新加载知识库"""
        try:
            # 移除旧实例
            if kb_id in self.rag_instances:
                del self.rag_instances[kb_id]
            
            # 重新创建实例
            rag = self.get_rag_instance(kb_id)
            return rag is not None
            
        except Exception as e:
            logger.error(f"重新加载知识库 {kb_id} 失败: {e}")
            return False
    
    def list_knowledge_bases(self) -> List[Dict[str, Any]]:
        """列出所有知识库"""
        kb_list = []
        
        for kb_id, kb_config in config_manager.knowledge_bases.items():
            kb_info = {
                "id": kb_id,
                "name": kb_config.name,
                "description": kb_config.description,
                "enabled": kb_config.enabled,
                "working_dir": kb_config.working_dir,
                "created_at": kb_config.created_at,
                "updated_at": kb_config.updated_at
            }
            
            # 检查是否有RAG实例
            if kb_id in self.rag_instances:
                kb_info["status"] = "loaded"
            else:
                kb_info["status"] = "not_loaded"
            
            kb_list.append(kb_info)
        
        return kb_list
    
    def get_knowledge_base_info(self, kb_id: str) -> Dict[str, Any]:
        """获取知识库详细信息"""
        kb_config = config_manager.knowledge_bases.get(kb_id)
        if not kb_config:
            return {"error": f"知识库 {kb_id} 不存在"}
        
        info = {
            "id": kb_id,
            "name": kb_config.name,
            "description": kb_config.description,
            "enabled": kb_config.enabled,
            "working_dir": kb_config.working_dir,
            "created_at": kb_config.created_at,
            "updated_at": kb_config.updated_at
        }
        
        # 获取RAG实例信息
        rag = self.get_rag_instance(kb_id)
        if rag:
            info["status"] = "loaded"
            # 可以添加更多统计信息
        else:
            info["status"] = "not_loaded"
        
        return info


class KnowledgeBaseInsertManager:
    """知识库插入管理器"""
    
    def __init__(self):
        self.kb_manager = KnowledgeBaseManager()
        self.document_processor = self.kb_manager.document_processor
        self.file_manager = self.kb_manager.file_manager
    
    async def insert_text(
        self, 
        text: str, 
        kb_id: str, 
        description: str = ""
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """插入文本到知识库"""
        rag = self.kb_manager.get_rag_instance(kb_id)
        if not rag:
            yield {"error": f"知识库 {kb_id} 不可用"}
            return
        
        try:
            yield {"status": "开始插入文本", "progress": 0}
            
            # 插入文本
            await asyncio.to_thread(rag.insert, text)
            
            yield {
                "status": "文本插入完成",
                "progress": 100,
                "kb_id": kb_id,
                "text_length": len(text),
                "description": description
            }
            
        except Exception as e:
            logger.error(f"插入文本到知识库 {kb_id} 失败: {e}")
            yield {"error": str(e)}
    
    async def insert_file(
        self, 
        file_path: str, 
        kb_id: str, 
        description: str = ""
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """插入文件到知识库"""
        rag = self.kb_manager.get_rag_instance(kb_id)
        if not rag:
            yield {"error": f"知识库 {kb_id} 不可用"}
            return
        
        try:
            yield {"status": "开始处理文件", "progress": 10}
            
            # 处理文件
            if not self.document_processor.can_process(file_path):
                yield {"error": f"不支持的文件格式: {Path(file_path).suffix}"}
                return
            
            text_content = self.document_processor.process_file(file_path)
            
            yield {"status": "文件处理完成，开始插入", "progress": 50}
            
            # 插入到知识库
            await asyncio.to_thread(rag.insert, text_content)
            
            yield {
                "status": "文件插入完成",
                "progress": 100,
                "kb_id": kb_id,
                "file_path": file_path,
                "text_length": len(text_content),
                "description": description
            }
            
        except Exception as e:
            logger.error(f"插入文件到知识库 {kb_id} 失败: {e}")
            yield {"error": str(e)}
    
    def list_knowledge_bases(self) -> List[Dict[str, Any]]:
        """列出所有知识库"""
        return self.kb_manager.list_knowledge_bases()
    
    def get_knowledge_base_info(self, kb_id: str) -> Dict[str, Any]:
        """获取知识库信息"""
        return self.kb_manager.get_knowledge_base_info(kb_id)
    
    def reload_knowledge_base(self, kb_id: str) -> bool:
        """重新加载知识库"""
        try:
            return self.kb_manager.reload_knowledge_base(kb_id)
        except Exception as e:
            logger.error(f"知识库 {kb_id} 重新加载失败: {e}")
            return False


# 全局插入管理器实例
insert_manager = KnowledgeBaseInsertManager()
