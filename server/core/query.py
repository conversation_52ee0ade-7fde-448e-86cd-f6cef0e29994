"""
GuiXiaoxi RAG 增强查询模块
支持多知识库查询、流式输出和通用问答
"""

import os
import asyncio
import time
import logging
from typing import List, Optional, Dict, Any, Union, AsyncGenerator
from enum import Enum
import json

import numpy as np

from guixiaoxi import GuiXiaoxiRAG, QueryParam
from guixiaoxi.llm.openai import openai_embed, openai_complete_if_cache
from guixiaoxi.kg.shared_storage import initialize_pipeline_status
from guixiaoxi.utils import setup_logger, EmbeddingFunc
from server.config.config import config_manager


# 创建简化的LLM调用函数，绕过异步装饰器问题
async def simple_llm_call(prompt: str, system_prompt: str = None, **kwargs) -> str:
    """简化的LLM调用函数，直接调用OpenAI API"""
    try:
        model = kwargs.get('model', config_manager.llm.model)
        api_key = kwargs.get('api_key', config_manager.llm.api_key)
        base_url = kwargs.get('base_url', config_manager.llm.api_base)

        # 直接调用openai_complete_if_cache，不经过装饰器
        result = await asyncio.wait_for(
            openai_complete_if_cache(
                model=model,
                prompt=prompt,
                system_prompt=system_prompt,
                history_messages=kwargs.get('history_messages', []),
                api_key=api_key,
                base_url=base_url,
                temperature=kwargs.get('temperature', config_manager.llm.temperature),
                max_tokens=kwargs.get('max_tokens', config_manager.llm.max_tokens),
                timeout=kwargs.get('timeout', 60)
            ),
            timeout=90
        )
        return result
    except Exception as e:
        logger.error(f"简化LLM调用失败: {e}")
        raise

# 创建适配器函数来匹配GuiXiaoxi期望的函数签名
async def llm_model_func_adapter(*args, **kwargs):
    """
    适配器函数，将GuiXiaoxi的调用格式转换为openai_complete_if_cache的格式

    GuiXiaoxi可能以以下方式调用:
    1. func(prompt, system_prompt=..., **kwargs)
    2. func(prompt, system_prompt, history_messages, **kwargs)
    3. 由于partial和装饰器，参数可能在args或kwargs中

    openai_complete_if_cache期望: func(model, prompt, system_prompt=..., **kwargs)
    """
    try:
        # 解析参数
        prompt = None
        system_prompt = None
        history_messages = None
        stream = False

        # 处理位置参数
        if len(args) >= 1:
            prompt = args[0]
        if len(args) >= 2:
            system_prompt = args[1]
        if len(args) >= 3:
            history_messages = args[2]
        if len(args) >= 4:
            stream = args[3]

        # 处理关键字参数，优先级高于位置参数
        if 'prompt' in kwargs:
            prompt = kwargs.pop('prompt')
        if 'system_prompt' in kwargs:
            system_prompt = kwargs.pop('system_prompt')
        if 'history_messages' in kwargs:
            history_messages = kwargs.pop('history_messages')
        if 'stream' in kwargs:
            stream = kwargs.pop('stream')

        # 如果还是没有prompt，尝试其他可能的键名
        if prompt is None:
            for possible_key in ['query', 'content', 'text', 'input']:
                if possible_key in kwargs:
                    prompt = kwargs.pop(possible_key)
                    break

        if prompt is None:
            raise ValueError("No prompt provided to LLM adapter")

        # 从kwargs中获取模型名称，或使用配置中的默认值
        model = kwargs.pop('model', None) or config_manager.llm.model

        # 清理可能干扰的参数
        kwargs.pop('_priority', None)  # 移除priority参数
        kwargs.pop('hashing_kv', None)  # 移除hashing_kv参数

        # 设置默认值
        if history_messages is None:
            history_messages = []

        # 设置API相关参数
        if 'api_key' not in kwargs:
            kwargs['api_key'] = config_manager.llm.api_key
        if 'base_url' not in kwargs:
            kwargs['base_url'] = config_manager.llm.api_base

        # 设置超时参数
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 60  # 60秒超时

        logger.debug(f"LLM调用参数: model={model}, prompt={prompt[:100]}..., system_prompt={system_prompt}, stream={stream}")

        # 使用asyncio.wait_for添加额外的超时保护
        try:
            result = await asyncio.wait_for(
                openai_complete_if_cache(
                    model=model,
                    prompt=prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    stream=stream,
                    **kwargs
                ),
                timeout=90  # 90秒总超时
            )
            return result
        except asyncio.TimeoutError:
            logger.error(f"LLM调用超时 (90秒): prompt={prompt[:100]}...")
            raise TimeoutError("LLM调用超时")

    except Exception as e:
        logger.error(f"LLM调用失败: {e}")
        logger.error(f"位置参数: {args}")
        logger.error(f"关键字参数: {kwargs}")
        raise

# 设置日志
setup_logger("guixiaoxi", level="INFO")
logger = logging.getLogger("guixiaoxi")


class QueryMode(Enum):
    """查询模式枚举"""
    LOCAL = "local"
    GLOBAL = "global"
    HYBRID = "hybrid"
    NAIVE = "naive"
    MIX = "mix"  # 保留mix查询方案
    BYPASS = "bypass" #通用回答模式 


class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self):
        self.rag_instances: Dict[str, GuiXiaoxiRAG] = {}
        self._initialize_knowledge_bases()
    
    def _initialize_knowledge_bases(self):
        """初始化知识库实例"""
        enabled_kbs = config_manager.get_enabled_knowledge_bases()

        for kb_id, kb_config in enabled_kbs.items():
            try:
                # 确保工作目录存在
                os.makedirs(kb_config.working_dir, exist_ok=True)

                # 创建RAG实例
                rag = GuiXiaoxiRAG(
                    working_dir=kb_config.working_dir,
                    embedding_func=EmbeddingFunc(
                        embedding_dim=config_manager.embedding.embedding_dim,
                        max_token_size=config_manager.embedding.max_token_size,
                        func=lambda texts: openai_embed(
                            texts,
                            model=config_manager.embedding.model,
                            base_url=config_manager.embedding.api_base,
                            api_key=config_manager.embedding.api_key
                        )
                    ),
                    llm_model_func=llm_model_func_adapter,
                    llm_model_name=config_manager.llm.model,
                    llm_model_max_token_size=config_manager.llm.max_token_size,
                    llm_model_kwargs={
                        "temperature": config_manager.llm.temperature,
                        "max_tokens": config_manager.llm.max_tokens
                    }
                )

                # 异步初始化存储和管道状态
                asyncio.create_task(self._async_initialize_rag(rag, kb_id))

                self.rag_instances[kb_id] = rag
                logger.info(f"知识库 {kb_id} 创建成功，正在异步初始化...")

            except Exception as e:
                logger.error(f"初始化知识库 {kb_id} 失败: {e}")

    async def _async_initialize_rag(self, rag: GuiXiaoxiRAG, kb_id: str):
        """异步初始化RAG实例"""
        try:
            # 初始化存储
            await rag.initialize_storages()
            # 初始化管道状态
            await initialize_pipeline_status()
            logger.info(f"知识库 {kb_id} 异步初始化完成")
        except Exception as e:
            logger.error(f"知识库 {kb_id} 异步初始化失败: {e}")
    
    def get_rag_instance(self, kb_id: str) -> Optional[GuiXiaoxiRAG]:
        """获取RAG实例"""
        return self.rag_instances.get(kb_id)
    
    def get_all_knowledge_bases(self) -> Dict[str, GuiXiaoxiRAG]:
        """获取所有知识库实例"""
        return self.rag_instances.copy()
    
    def reload_knowledge_base(self, kb_id: str) -> bool:
        """重新加载知识库"""
        try:
            kb_config = config_manager.knowledge_bases.get(kb_id)
            if not kb_config or not kb_config.enabled:
                # 移除已禁用的知识库
                if kb_id in self.rag_instances:
                    del self.rag_instances[kb_id]
                return True
            
            # 重新创建RAG实例
            os.makedirs(kb_config.working_dir, exist_ok=True)
            
            rag = GuiXiaoxiRAG(
                working_dir=kb_config.working_dir,
                embedding_func=EmbeddingFunc(
                    embedding_dim=config_manager.embedding.embedding_dim,
                    max_token_size=config_manager.embedding.max_token_size,
                    func=lambda texts: openai_embed(
                        texts,
                        model=config_manager.embedding.model,
                        base_url=config_manager.embedding.api_base,
                        api_key=config_manager.embedding.api_key
                    )
                ),
                llm_model_func=llm_model_func_adapter,
                llm_model_name=config_manager.llm.model,
                llm_model_max_token_size=config_manager.llm.max_token_size,
                llm_model_kwargs={
                    "temperature": config_manager.llm.temperature,
                    "max_tokens": config_manager.llm.max_tokens
                }
            )
            
            self.rag_instances[kb_id] = rag
            logger.info(f"知识库 {kb_id} 重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"重新加载知识库 {kb_id} 失败: {e}")
            return False


class StreamingQueryHandler:
    """流式查询处理器"""
    
    def __init__(self, rag: GuiXiaoxiRAG):
        self.rag = rag
    
    async def stream_query(
        self,
        query: str,
        mode: QueryMode = QueryMode.HYBRID,
        query_param: QueryParam = None
    ) -> AsyncGenerator[str, None]:
        """流式查询"""
        try:
            # 如果没有提供查询参数，则根据模式创建
            if query_param is None:
                mode_mapping = {
                    QueryMode.LOCAL: "local",
                    QueryMode.GLOBAL: "global",
                    QueryMode.HYBRID: "hybrid",
                    QueryMode.NAIVE: "naive",
                    QueryMode.MIX: "mix",
                    QueryMode.BYPASS: "bypass"
                }
                query_param = QueryParam(mode=mode_mapping.get(mode, "hybrid"), stream=True)
            else:
                query_param.stream = True

            # 执行查询并流式返回结果
            result = await asyncio.to_thread(
                self.rag.query,
                query,
                param=query_param
            )

            # 处理不同类型的结果
            if hasattr(result, '__aiter__'):
                # 异步迭代器（真正的流式输出）
                async for chunk in result:
                    yield str(chunk)
            elif hasattr(result, '__iter__') and not isinstance(result, str):
                # 同步迭代器
                for chunk in result:
                    yield str(chunk)
                    await asyncio.sleep(0.01)
            elif isinstance(result, str):
                # 字符串结果，分块返回
                chunk_size = 50  # 每次返回50个字符
                for i in range(0, len(result), chunk_size):
                    chunk = result[i:i + chunk_size]
                    yield chunk
                    await asyncio.sleep(0.01)  # 小延迟模拟流式效果
            else:
                yield str(result)

        except Exception as e:
            logger.error(f"流式查询失败: {e}")
            yield f"查询失败: {str(e)}"


class EnhancedQueryManager:
    """增强查询管理器"""
    
    def __init__(self):
        self.kb_manager = KnowledgeBaseManager()
        self.query_history: List[Dict[str, Any]] = []
        self.general_qa_rag: Optional[GuiXiaoxiRAG] = None
        
        # 初始化通用问答RAG（如果启用）
        if config_manager.rag.enable_general_qa:
            self._initialize_general_qa()
    
    def _initialize_general_qa(self):
        """初始化通用问答RAG"""
        try:
            # 使用默认知识库或创建临时实例
            default_kb = config_manager.rag.default_kb
            if default_kb and default_kb in self.kb_manager.rag_instances:
                self.general_qa_rag = self.kb_manager.rag_instances[default_kb]
            else:
                # 创建临时通用问答实例
                temp_dir = os.path.join(config_manager.rag.working_dir, "general_qa")
                os.makedirs(temp_dir, exist_ok=True)
                
                self.general_qa_rag = GuiXiaoxiRAG(
                    working_dir=temp_dir,
                    embedding_func=EmbeddingFunc(
                        embedding_dim=config_manager.embedding.embedding_dim,
                        max_token_size=config_manager.embedding.max_token_size,
                        func=openai_embed
                    ),
                    llm_model_func=llm_model_func_adapter,
                    llm_model_name=config_manager.llm.model,
                    llm_model_max_token_size=config_manager.llm.max_token_size,
                    llm_model_kwargs={
                        "temperature": config_manager.llm.temperature,
                        "max_tokens": config_manager.llm.max_tokens
                    }
                )
            
            logger.info("通用问答RAG初始化成功")
            
        except Exception as e:
            logger.error(f"初始化通用问答RAG失败: {e}")
            self.general_qa_rag = None
    
    async def query_single_kb(
        self,
        query: str,
        kb_id: str,
        mode: QueryMode = QueryMode.HYBRID,
        stream: Optional[bool] = None,
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """查询单个知识库"""
        rag = self.kb_manager.get_rag_instance(kb_id)
        if not rag:
            raise ValueError(f"知识库 {kb_id} 不可用")

        # 记录查询历史
        self._add_to_history(query, kb_id, mode.value)

        # 确定是否使用流式输出
        use_stream = stream if stream is not None else config_manager.rag.enable_stream

        # 构建查询参数
        query_param = self._build_query_param(mode, **kwargs)

        if use_stream:
            # 流式查询
            handler = StreamingQueryHandler(rag)
            return handler.stream_query(query, mode, query_param)
        else:
            # 非流式查询
            try:
                result = await asyncio.to_thread(
                    rag.query,
                    query,
                    param=query_param
                )

                return str(result)

            except Exception as e:
                logger.error(f"查询知识库 {kb_id} 失败: {e}")
                raise

    def _build_query_param(self, mode: QueryMode, **kwargs) -> QueryParam:
        """构建查询参数"""
        # 模式映射
        mode_mapping = {
            QueryMode.LOCAL: "local",
            QueryMode.GLOBAL: "global",
            QueryMode.HYBRID: "hybrid",
            QueryMode.NAIVE: "naive",
            QueryMode.MIX: "mix",
            QueryMode.BYPASS: "bypass"
        }

        # 基础参数
        param_dict = {
            "mode": mode_mapping.get(mode, "hybrid"),
            "stream": kwargs.get("stream", False),
            "top_k": kwargs.get("top_k", 60),
            "chunk_top_k": kwargs.get("chunk_top_k", 5),
            "response_type": kwargs.get("response_type", "Multiple Paragraphs"),
            "only_need_context": kwargs.get("only_need_context", False),
            "only_need_prompt": kwargs.get("only_need_prompt", False),
        }

        # 高级参数
        if "conversation_history" in kwargs:
            param_dict["conversation_history"] = kwargs["conversation_history"]
        if "user_prompt" in kwargs:
            param_dict["user_prompt"] = kwargs["user_prompt"]
        if "hl_keywords" in kwargs:
            param_dict["hl_keywords"] = kwargs["hl_keywords"]
        if "ll_keywords" in kwargs:
            param_dict["ll_keywords"] = kwargs["ll_keywords"]

        return QueryParam(**param_dict)
    
    async def query_multiple_kbs(
        self, 
        query: str, 
        kb_ids: List[str], 
        mode: QueryMode = QueryMode.HYBRID,
        stream: Optional[bool] = None
    ) -> Union[Dict[str, str], AsyncGenerator[Dict[str, str], None]]:
        """查询多个知识库"""
        # 记录查询历史
        self._add_to_history(query, kb_ids, mode.value)
        
        # 确定是否使用流式输出
        use_stream = stream if stream is not None else config_manager.rag.enable_stream
        
        if use_stream:
            # 流式查询多个知识库
            return self._stream_query_multiple_kbs(query, kb_ids, mode)
        else:
            # 非流式查询多个知识库
            results = {}
            
            for kb_id in kb_ids:
                try:
                    result = await self.query_single_kb(query, kb_id, mode, stream=False)
                    results[kb_id] = result
                except Exception as e:
                    logger.error(f"查询知识库 {kb_id} 失败: {e}")
                    results[kb_id] = f"查询失败: {str(e)}"
            
            return results
    
    async def _stream_query_multiple_kbs(
        self, 
        query: str, 
        kb_ids: List[str], 
        mode: QueryMode
    ) -> AsyncGenerator[Dict[str, str], None]:
        """流式查询多个知识库"""
        for kb_id in kb_ids:
            try:
                result_generator = await self.query_single_kb(query, kb_id, mode, stream=True)
                
                if hasattr(result_generator, '__aiter__'):
                    async for chunk in result_generator:
                        yield {kb_id: chunk}
                else:
                    yield {kb_id: str(result_generator)}
                    
            except Exception as e:
                logger.error(f"流式查询知识库 {kb_id} 失败: {e}")
                yield {kb_id: f"查询失败: {str(e)}"}
    
    async def general_qa(
        self,
        query: str,
        stream: Optional[bool] = None,
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """通用问答（不使用知识库）- 使用bypass模式"""
        # 记录查询历史
        self._add_to_history(query, "general", "bypass")

        # 确定是否使用流式输出
        use_stream = stream if stream is not None else config_manager.rag.enable_stream

        try:
            # 使用bypass模式进行通用问答
            if use_stream:
                # 流式通用问答
                return self._stream_general_qa(query, **kwargs)
            else:
                # 非流式通用问答 - 使用bypass模式
                query_param = QueryParam(
                    mode="bypass",
                    stream=False,
                    conversation_history=kwargs.get("conversation_history", []),
                    user_prompt=kwargs.get("user_prompt")
                )

                # 使用任意一个RAG实例的bypass模式
                if self.kb_manager.rag_instances:
                    rag = next(iter(self.kb_manager.rag_instances.values()))
                    result = await asyncio.to_thread(
                        rag.query,
                        query,
                        param=query_param
                    )
                    return str(result)
                else:
                    # 如果没有RAG实例，直接使用简化的LLM调用
                    result = await simple_llm_call(
                        prompt=query,
                        temperature=config_manager.llm.temperature,
                        max_tokens=config_manager.llm.max_tokens
                    )
                    return str(result)

        except Exception as e:
            logger.error(f"通用问答失败: {e}")
            raise
    
    async def _stream_general_qa(self, query: str) -> AsyncGenerator[str, None]:
        """流式通用问答"""
        try:
            result = await simple_llm_call(
                prompt=query,
                temperature=config_manager.llm.temperature,
                max_tokens=config_manager.llm.max_tokens
            )

            # 分块返回结果
            result_str = str(result)
            chunk_size = 50
            for i in range(0, len(result_str), chunk_size):
                chunk = result_str[i:i + chunk_size]
                yield chunk
                await asyncio.sleep(0.01)

        except Exception as e:
            logger.error(f"流式通用问答失败: {e}")
            yield f"通用问答失败: {str(e)}"
    
    def _add_to_history(self, query: str, kb_id: Union[str, List[str]], mode: str):
        """添加到查询历史"""
        history_entry = {
            "timestamp": time.time(),
            "query": query,
            "kb_id": kb_id,
            "mode": mode
        }
        
        self.query_history.append(history_entry)
        
        # 限制历史记录数量
        if len(self.query_history) > 1000:
            self.query_history = self.query_history[-500:]
    
    def get_query_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取查询历史"""
        return self.query_history[-limit:] if limit > 0 else self.query_history
    
    def clear_query_history(self):
        """清空查询历史"""
        self.query_history.clear()

    async def query_async(self, query: str, mode: QueryMode = QueryMode.HYBRID,
                         knowledge_base_ids: Optional[List[str]] = None,
                         top_k: Optional[int] = None,
                         enable_stream: bool = False,
                         response_type: Optional[str] = None,
                         user_prompt: Optional[str] = None,
                         conversation_history: Optional[List[Dict[str, Any]]] = None,
                         history_turns: Optional[int] = None,
                         only_need_context: Optional[bool] = None,
                         only_need_prompt: Optional[bool] = None,
                         max_token_for_text_unit: Optional[int] = None,
                         max_token_for_global_context: Optional[int] = None,
                         max_token_for_local_context: Optional[int] = None,
                         chunk_top_k: Optional[int] = None,
                         chunk_rerank_top_k: Optional[int] = None) -> Union[str, AsyncGenerator[str, None]]:
        """
        高级异步查询方法 - 支持GuiXiaoxi的所有查询参数
        """
        try:
            # 构建查询参数对象
            query_param = QueryParam(
                mode=mode.value if hasattr(mode, 'value') else str(mode),
                only_need_context=only_need_context,
                only_need_prompt=only_need_prompt,
                response_type=response_type,
                top_k=top_k or getattr(config_manager.rag, 'top_k', 10),
                chunk_top_k=chunk_top_k,
                chunk_rerank_top_k=chunk_rerank_top_k,
                max_token_for_text_unit=max_token_for_text_unit,
                max_token_for_global_context=max_token_for_global_context,
                max_token_for_local_context=max_token_for_local_context,
                conversation_history=conversation_history,
                history_turns=history_turns,
                user_prompt=user_prompt,
                stream=enable_stream
            )

            # 选择查询的知识库
            if knowledge_base_ids:
                # 查询指定的知识库
                if len(knowledge_base_ids) == 1:
                    kb_id = knowledge_base_ids[0]
                    rag = self.kb_manager.get_rag_instance(kb_id)
                    if not rag:
                        raise ValueError(f"知识库 {kb_id} 不存在或未加载")

                    if enable_stream:
                        return self._stream_single_rag(rag, query, query_param)
                    else:
                        try:
                            return await rag.aquery(query, param=query_param)
                        except Exception as e:
                            logger.error(f"RAG查询失败: {e}")
                            return f"查询失败: {str(e)}"
                else:
                    # 多知识库查询
                    if enable_stream:
                        return self._stream_multiple_rags(knowledge_base_ids, query, query_param)
                    else:
                        return await self._query_multiple_rags(knowledge_base_ids, query, query_param)
            else:
                # 查询所有启用的知识库
                enabled_kbs = [kb_id for kb_id, kb_config in config_manager.knowledge_bases.items() if kb_config.enabled]
                if not enabled_kbs:
                    raise ValueError("没有启用的知识库")

                if len(enabled_kbs) == 1:
                    kb_id = enabled_kbs[0]
                    rag = self.kb_manager.get_rag_instance(kb_id)
                    if not rag:
                        raise ValueError(f"知识库 {kb_id} 不存在或未加载")

                    if enable_stream:
                        return self._stream_single_rag(rag, query, query_param)
                    else:
                        try:
                            return await rag.aquery(query, param=query_param)
                        except Exception as e:
                            logger.error(f"RAG查询失败: {e}")
                            return f"查询失败: {str(e)}"
                else:
                    if enable_stream:
                        return self._stream_multiple_rags(enabled_kbs, query, query_param)
                    else:
                        return await self._query_multiple_rags(enabled_kbs, query, query_param)

        except Exception as e:
            logger.error(f"高级查询失败: {e}")
            raise

    async def query_stream_async(self, query: str, **kwargs) -> AsyncGenerator[str, None]:
        """流式查询的便捷方法"""
        kwargs['enable_stream'] = True
        result = await self.query_async(query, **kwargs)

        if hasattr(result, '__aiter__'):
            async for chunk in result:
                yield chunk
        else:
            # 如果不是异步生成器，直接返回结果
            yield str(result)

    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {}
        
        for kb_id, rag_instance in self.kb_manager.get_all_knowledge_bases().items():
            kb_config = config_manager.knowledge_bases.get(kb_id)
            if not kb_config:
                continue
            
            stats[kb_id] = {
                "name": kb_config.name,
                "description": kb_config.description,
                "enabled": kb_config.enabled,
                "working_dir": kb_config.working_dir,
                "status": "loaded" if rag_instance else "not_loaded"
            }
        
        return stats

    async def _query_multiple_rags(self, kb_ids: List[str], query: str, query_param) -> str:
        """查询多个RAG实例并合并结果"""
        results = {}

        for kb_id in kb_ids:
            try:
                rag = self.kb_manager.get_rag_instance(kb_id)
                if rag:
                    result = await rag.aquery(query, param=query_param)
                    results[kb_id] = result
                else:
                    results[kb_id] = f"知识库 {kb_id} 未加载"
            except Exception as e:
                logger.error(f"查询知识库 {kb_id} 失败: {e}")
                results[kb_id] = f"查询失败: {str(e)}"

        # 合并结果
        if len(results) == 1:
            return list(results.values())[0]
        else:
            combined_result = "多知识库查询结果:\n\n"
            for kb_id, result in results.items():
                combined_result += f"=== {kb_id} ===\n{result}\n\n"
            return combined_result.strip()

    async def _stream_single_rag(self, rag, query: str, query_param) -> AsyncGenerator[str, None]:
        """单个RAG实例的流式查询"""
        try:
            result = await rag.aquery(query, param=query_param)

            if hasattr(result, '__aiter__'):
                async for chunk in result:
                    yield chunk
            else:
                yield str(result)
        except Exception as e:
            logger.error(f"流式查询失败: {e}")
            yield f"查询失败: {str(e)}"

    async def _stream_multiple_rags(self, kb_ids: List[str], query: str, query_param) -> AsyncGenerator[str, None]:
        """多个RAG实例的流式查询"""
        for i, kb_id in enumerate(kb_ids):
            try:
                if i > 0:
                    yield f"\n\n=== {kb_id} ===\n"
                else:
                    yield f"=== {kb_id} ===\n"

                rag = self.kb_manager.get_rag_instance(kb_id)
                if rag:
                    result = await rag.aquery(query, param=query_param)

                    if hasattr(result, '__aiter__'):
                        async for chunk in result:
                            yield chunk
                    else:
                        yield str(result)
                else:
                    yield f"知识库 {kb_id} 未加载"

            except Exception as e:
                logger.error(f"流式查询知识库 {kb_id} 失败: {e}")
                yield f"查询失败: {str(e)}"

    def get_query_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取查询历史"""
        return self.query_history[-limit:] if limit > 0 else self.query_history

    def clear_query_history(self):
        """清空查询历史"""
        self.query_history.clear()
        logger.info("查询历史已清空")

    async def query_async(self, query: str, **kwargs) -> str:
        """异步查询接口 - 支持所有查询模式和参数"""
        mode = kwargs.get("mode", QueryMode.HYBRID)
        kb_ids = kwargs.get("knowledge_base_ids")

        # 移除mode参数避免冲突
        query_kwargs = {k: v for k, v in kwargs.items() if k not in ['mode', 'knowledge_base_ids']}

        if not kb_ids:
            # 查询所有启用的知识库
            enabled_kbs = config_manager.get_enabled_knowledge_bases()
            kb_ids = list(enabled_kbs.keys())

        if mode == QueryMode.BYPASS:
            # 通用问答模式
            return await self.general_qa(query, stream=False, **query_kwargs)
        elif len(kb_ids) == 1:
            # 单知识库查询
            return await self.query_single_kb(
                query=query,
                kb_id=kb_ids[0],
                mode=mode,
                stream=False,
                **query_kwargs
            )
        else:
            # 多知识库查询
            results = await self.query_multiple_kbs(
                query=query,
                kb_ids=kb_ids,
                mode=mode,
                stream=False,
                **query_kwargs
            )
            # 合并多个知识库的结果
            if isinstance(results, dict):
                combined_result = "\n\n".join([
                    f"**{kb_id}**: {result}"
                    for kb_id, result in results.items()
                    if not result.startswith("查询失败")
                ])
                return combined_result or "未找到相关信息"
            return str(results)

    async def query_stream_async(self, query: str, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式查询接口"""
        mode = kwargs.get("mode", QueryMode.HYBRID)
        kb_ids = kwargs.get("knowledge_base_ids")

        if not kb_ids:
            # 查询所有启用的知识库
            enabled_kbs = config_manager.get_enabled_knowledge_bases()
            kb_ids = list(enabled_kbs.keys())

        if mode == QueryMode.BYPASS:
            # 通用问答模式
            async for chunk in self.general_qa(query, stream=True, **kwargs):
                yield chunk
        elif len(kb_ids) == 1:
            # 单知识库查询
            result_generator = await self.query_single_kb(
                query=query,
                kb_id=kb_ids[0],
                mode=mode,
                stream=True,
                **kwargs
            )
            if hasattr(result_generator, '__aiter__'):
                async for chunk in result_generator:
                    yield chunk
            else:
                yield str(result_generator)
        else:
            # 多知识库流式查询
            async for chunk_dict in self._stream_query_multiple_kbs(query, kb_ids, mode):
                for kb_id, chunk in chunk_dict.items():
                    yield f"[{kb_id}] {chunk}"


# 全局查询管理器实例
query_manager = EnhancedQueryManager()
