"""
GuiXiaoxi RAG 完整功能 Streamlit 应用 v3.0
支持所有查询模式、知识库管理、文档上传、系统监控等完整功能
"""

import streamlit as st
import requests
import json
import time
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
import io
import asyncio
from pathlib import Path
import base64
import zipfile
import tempfile

# 配置页面
st.set_page_config(
    page_title="GuiXiaoxi RAG 智能问答系统 v3.0",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
API_BASE_URL = "http://localhost:8000"
SESSION_STATE_KEYS = [
    'query_history', 'chat_messages', 'knowledge_bases',
    'service_status', 'last_update', 'selected_kbs', 'graph_stats',
    'query_modes', 'query_templates', 'system_config'
]

# 查询模式配置
QUERY_MODES = {
    "local": {
        "name": "🎯 本地实体查询",
        "description": "基于实体相似性检索，适用于查询特定实体的详细信息",
        "icon": "🎯",
        "color": "#FF6B6B"
    },
    "global": {
        "name": "🌐 全局关系查询",
        "description": "基于关系图谱检索，适用于查询实体间的关系和连接",
        "icon": "🌐",
        "color": "#4ECDC4"
    },
    "hybrid": {
        "name": "🔄 混合查询",
        "description": "结合本地和全局信息，适用于大多数复杂查询场景",
        "icon": "🔄",
        "color": "#45B7D1"
    },
    "naive": {
        "name": "📄 简单向量检索",
        "description": "基于文档相似性，适用于简单的文档检索和问答",
        "icon": "📄",
        "color": "#96CEB4"
    },
    "mix": {
        "name": "🔀 混合检索模式",
        "description": "结合知识图谱和向量检索，适用于综合多种检索策略",
        "icon": "🔀",
        "color": "#FFEAA7"
    },
    "bypass": {
        "name": "💭 直接LLM查询",
        "description": "跳过RAG检索，直接使用LLM回答，适用于通用对话",
        "icon": "💭",
        "color": "#DDA0DD"
    }
}

# 初始化会话状态
for key in SESSION_STATE_KEYS:
    if key not in st.session_state:
        if key == 'query_history':
            st.session_state[key] = []
        elif key == 'chat_messages':
            st.session_state[key] = []
        elif key == 'knowledge_bases':
            st.session_state[key] = {}
        elif key == 'selected_kbs':
            st.session_state[key] = []
        else:
            st.session_state[key] = None


def check_api_status():
    """检查API服务状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, None
    except:
        return False, None


def load_knowledge_bases():
    """加载知识库列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/knowledge-bases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            # 处理不同的API响应格式
            if 'knowledge_bases' in data:
                if isinstance(data['knowledge_bases'], dict):
                    # 新格式：{kb_id: kb_info}
                    st.session_state.knowledge_bases = data['knowledge_bases']
                else:
                    # 旧格式：[{id: kb_id, ...}]
                    st.session_state.knowledge_bases = {
                        kb['id']: kb for kb in data['knowledge_bases']
                        if 'id' in kb
                    }
            return True
        return False
    except Exception as e:
        st.error(f"加载知识库失败: {e}")
        return False


def load_query_modes():
    """加载查询模式信息"""
    try:
        response = requests.get(f"{API_BASE_URL}/query/modes", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                st.session_state.query_modes = data.get('data', {}).get('modes', [])
                return True
        return False
    except:
        return False


def load_query_templates():
    """加载查询模板"""
    try:
        response = requests.get(f"{API_BASE_URL}/query/templates", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                st.session_state.query_templates = data.get('data', {}).get('templates', {})
                return True
        return False
    except:
        return False


def load_graph_stats():
    """加载图统计信息"""
    try:
        response = requests.get(f"{API_BASE_URL}/graph/stats", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                st.session_state.graph_stats = data.get('data', {})
                return True
        return False
    except:
        return False


def get_config():
    """获取系统配置"""
    try:
        response = requests.get(f"{API_BASE_URL}/config", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except:
        return None


def sidebar():
    """侧边栏"""
    with st.sidebar:
        st.title("🤖 GuiXiaoxi RAG v3.0")

        # 服务状态检查
        status_ok, status_data = check_api_status()
        if status_ok:
            st.success("✅ API 服务正常")
            if status_data:
                config = status_data.get('data', {}).get('config', {})
                st.caption(f"LLM: {config.get('llm_model', 'Unknown')}")
                st.caption(f"嵌入: {config.get('embedding_model', 'Unknown')}")
        else:
            st.error("❌ API 服务异常")
            st.stop()

        st.divider()

        # 功能选择
        page = st.selectbox(
            "选择功能",
            [
                "💬 智能问答",
                "🔍 高级查询",
                "📚 知识库管理",
                "📊 系统状态",
                "📈 数据分析",
                "🧪 测试工具",
                "⚙️ 系统设置"
            ]
        )
        
        # 知识库选择（仅在问答页面显示）
        if page == "💬 智能问答":
            st.subheader("📚 知识库选择")
            
            # 加载知识库
            if st.button("🔄 刷新知识库"):
                load_knowledge_bases()
            
            if not st.session_state.knowledge_bases:
                load_knowledge_bases()
            
            # 显示知识库选择
            enabled_kbs = {
                kb_id: kb for kb_id, kb in st.session_state.knowledge_bases.items()
                if kb.get('enabled', False)
            }
            
            if enabled_kbs:
                kb_options = ["全部知识库"] + [
                    f"{kb['name']} ({kb_id})" 
                    for kb_id, kb in enabled_kbs.items()
                ]
                
                selected_option = st.selectbox(
                    "选择查询范围",
                    kb_options,
                    key="kb_selector"
                )
                
                if selected_option == "全部知识库":
                    st.session_state.selected_kbs = list(enabled_kbs.keys())
                else:
                    # 提取知识库ID
                    kb_id = selected_option.split("(")[-1].rstrip(")")
                    st.session_state.selected_kbs = [kb_id]
                
                # 显示选中的知识库信息
                st.caption(f"已选择 {len(st.session_state.selected_kbs)} 个知识库")
            else:
                st.warning("没有可用的知识库")
        
        st.divider()
        
        # 系统信息
        config = get_config()
        if config:
            st.subheader("🔧 系统配置")
            st.caption(f"流式输出: {'✅' if config.get('rag', {}).get('enable_stream') else '❌'}")
            st.caption(f"通用问答: {'✅' if config.get('rag', {}).get('enable_general_qa') else '❌'}")
        
        return page


def chat_page():
    """智能问答页面"""
    st.header("💬 智能问答")

    # 查询模式选择
    col1, col2, col3 = st.columns(3)

    with col1:
        # 使用更丰富的模式选择界面
        mode_options = list(QUERY_MODES.keys())
        mode_labels = [QUERY_MODES[mode]["name"] for mode in mode_options]

        selected_mode_index = st.selectbox(
            "查询模式",
            range(len(mode_options)),
            format_func=lambda x: mode_labels[x],
            help="选择不同的查询模式以获得最佳结果"
        )
        query_mode = mode_options[selected_mode_index]

        # 显示模式描述
        mode_info = QUERY_MODES[query_mode]
        st.caption(f"{mode_info['icon']} {mode_info['description']}")

    with col2:
        enable_stream = st.checkbox("启用流式输出", value=True,
                                   help="实时显示生成过程")
        use_general_qa = st.checkbox("通用问答模式", value=query_mode=="bypass",
                                   help="不使用知识库，直接回答问题")

    with col3:
        # 高级参数
        with st.expander("🔧 高级参数"):
            top_k = st.slider("检索数量 (top_k)", 1, 100, 10,
                            help="检索的相关内容数量")
            response_type = st.selectbox("响应格式",
                                       ["Multiple Paragraphs", "Single Paragraph", "Bullet Points"],
                                       help="选择回答的格式风格")
    
    # 查询输入
    query = st.text_area(
        "请输入您的问题:",
        height=100,
        placeholder="例如：计算机学院有哪些专业？"
    )
    
    # 查询按钮
    if st.button("🚀 开始查询", type="primary", disabled=not query.strip()):
        if use_general_qa:
            # 通用问答
            with st.spinner("正在思考中..."):
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/query/general",
                        json={
                            "query": query,
                            "stream": enable_stream
                        },
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        st.success("✅ 查询完成")
                        st.markdown("### 📝 回答")
                        st.markdown(result.get("result", "没有返回结果"))
                    else:
                        st.error(f"查询失败: {response.text}")
                        
                except Exception as e:
                    st.error(f"查询异常: {e}")
        else:
            # 知识库查询
            if not st.session_state.selected_kbs:
                st.warning("请先选择知识库")
                return
            
            with st.spinner("正在查询知识库..."):
                try:
                    # 使用新的高级查询API
                    query_data = {
                        "query": query,
                        "mode": query_mode,
                        "stream": enable_stream,
                        "top_k": 10,
                        "response_type": "detailed"
                    }

                    # 如果选择了特定知识库，添加到查询参数中
                    if len(st.session_state.selected_kbs) == 1:
                        query_data["kb_id"] = st.session_state.selected_kbs[0]

                    response = requests.post(
                        f"{API_BASE_URL}/query/advanced",
                        json=query_data,
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        st.success("✅ 查询完成")

                        # 显示结果 - 适配新的API响应格式
                        if result.get("success"):
                            data = result.get("data", {})
                            response_text = data.get("response", "没有返回结果")

                            st.markdown("### 📝 查询结果")
                            st.markdown(response_text)

                            # 显示查询信息
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.caption(f"🔍 模式: {data.get('mode', 'Unknown')}")
                            with col2:
                                st.caption(f"📚 知识库: {data.get('knowledge_bases', 'Unknown')}")
                            with col3:
                                st.caption(f"⏱️ 时间: {data.get('timestamp', 'Unknown')}")
                        else:
                            st.error(f"查询失败: {result.get('message', '未知错误')}")
                    else:
                        st.error(f"查询失败: {response.text}")
                        
                except Exception as e:
                    st.error(f"查询异常: {e}")
    
    # 查询历史
    if st.button("📜 查看查询历史"):
        try:
            response = requests.get(f"{API_BASE_URL}/query/history?limit=20")
            if response.status_code == 200:
                history = response.json().get("history", [])
                if history:
                    st.markdown("### 📜 最近查询历史")
                    for item in reversed(history[-10:]):  # 显示最近10条
                        with st.expander(f"🕐 {datetime.fromtimestamp(item['timestamp']).strftime('%H:%M:%S')} - {item['query'][:50]}..."):
                            st.markdown(f"**查询**: {item['query']}")
                            st.markdown(f"**知识库**: {item['kb_id']}")
                            st.markdown(f"**模式**: {item['mode']}")
                            st.markdown(f"**耗时**: {item['query_time']:.2f}秒")
                            st.markdown(f"**结果**: {item['result'][:200]}...")
                else:
                    st.info("暂无查询历史")
        except Exception as e:
            st.error(f"获取历史失败: {e}")


def knowledge_base_page():
    """知识库管理页面"""
    st.header("📚 知识库管理")
    
    # 刷新知识库列表
    if st.button("🔄 刷新知识库列表"):
        load_knowledge_bases()
        st.rerun()
    
    if not st.session_state.knowledge_bases:
        load_knowledge_bases()
    
    # 知识库列表
    if st.session_state.knowledge_bases:
        st.subheader("📋 知识库列表")
        
        # 创建表格数据
        kb_data = []
        for kb_id, kb in st.session_state.knowledge_bases.items():
            kb_data.append({
                "ID": kb_id,
                "名称": kb.get('name', ''),
                "描述": kb.get('description', ''),
                "状态": "✅ 启用" if kb.get('enabled') else "❌ 禁用",
                "加载状态": kb.get('status', 'unknown'),
                "工作目录": kb.get('working_dir', '')
            })
        
        df = pd.DataFrame(kb_data)
        st.dataframe(df, use_container_width=True)
        
        # 知识库操作
        st.subheader("🔧 知识库操作")
        
        selected_kb = st.selectbox(
            "选择知识库",
            list(st.session_state.knowledge_bases.keys()),
            format_func=lambda x: f"{st.session_state.knowledge_bases[x].get('name', x)} ({x})"
        )
        
        if selected_kb:
            kb_info = st.session_state.knowledge_bases[selected_kb]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if kb_info.get('enabled'):
                    if st.button("❌ 禁用知识库"):
                        try:
                            response = requests.post(f"{API_BASE_URL}/knowledge-bases/{selected_kb}/disable")
                            if response.status_code == 200:
                                st.success("知识库已禁用")
                                st.rerun()
                            else:
                                st.error("禁用失败")
                        except Exception as e:
                            st.error(f"操作失败: {e}")
                else:
                    if st.button("✅ 启用知识库"):
                        try:
                            response = requests.post(f"{API_BASE_URL}/knowledge-bases/{selected_kb}/enable")
                            if response.status_code == 200:
                                st.success("知识库已启用")
                                st.rerun()
                            else:
                                st.error("启用失败")
                        except Exception as e:
                            st.error(f"操作失败: {e}")
            
            with col2:
                if st.button("📄 查看详情"):
                    try:
                        response = requests.get(f"{API_BASE_URL}/knowledge-bases/{selected_kb}")
                        if response.status_code == 200:
                            details = response.json()
                            st.json(details)
                    except Exception as e:
                        st.error(f"获取详情失败: {e}")
            
            with col3:
                if selected_kb != "default" and st.button("🗑️ 删除知识库", type="secondary"):
                    if st.checkbox(f"确认删除 {selected_kb}"):
                        try:
                            response = requests.delete(f"{API_BASE_URL}/knowledge-bases/{selected_kb}")
                            if response.status_code == 200:
                                st.success("知识库已删除")
                                st.rerun()
                            else:
                                st.error("删除失败")
                        except Exception as e:
                            st.error(f"删除失败: {e}")
    
    # 文档上传
    st.subheader("📤 文档上传")
    
    target_kb = st.selectbox(
        "目标知识库",
        [kb_id for kb_id, kb in st.session_state.knowledge_bases.items() if kb.get('enabled')],
        format_func=lambda x: f"{st.session_state.knowledge_bases[x].get('name', x)} ({x})"
    )
    
    uploaded_file = st.file_uploader(
        "选择文档文件",
        type=['txt', 'md', 'json', 'docx', 'pdf'],
        help="支持 TXT, MD, JSON, DOCX, PDF 格式"
    )
    
    description = st.text_input("文档描述（可选）")
    
    if uploaded_file and target_kb:
        if st.button("📤 上传并插入", type="primary"):
            with st.spinner("正在处理文档..."):
                try:
                    files = {"file": uploaded_file}
                    data = {"description": description}
                    
                    response = requests.post(
                        f"{API_BASE_URL}/insert/file/{target_kb}",
                        files=files,
                        data=data,
                        timeout=120
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        st.success("✅ 文档上传成功")
                        st.json(result)
                    else:
                        st.error(f"上传失败: {response.text}")
                        
                except Exception as e:
                    st.error(f"上传异常: {e}")


def advanced_query_page():
    """高级查询页面"""
    st.header("🔍 高级查询")

    # 查询模式选择
    col1, col2 = st.columns(2)

    with col1:
        # 使用更丰富的模式选择界面
        mode_options = list(QUERY_MODES.keys())
        mode_labels = [QUERY_MODES[mode]["name"] for mode in mode_options]

        selected_mode_index = st.selectbox(
            "查询模式",
            range(len(mode_options)),
            format_func=lambda x: mode_labels[x],
            index=2,  # 默认选择hybrid
            help="选择不同的查询模式以获得最佳结果"
        )
        query_mode = mode_options[selected_mode_index]

        # 显示模式描述
        mode_info = QUERY_MODES[query_mode]
        st.info(f"{mode_info['icon']} {mode_info['description']}")

        # 知识库选择
        if st.session_state.knowledge_bases:
            kb_options = list(st.session_state.knowledge_bases.keys())
            selected_kbs = st.multiselect(
                "选择知识库",
                kb_options,
                default=kb_options[:1] if kb_options else [],
                help="选择要查询的知识库，留空则查询所有启用的知识库"
            )
        else:
            selected_kbs = []

    with col2:
        # 检索参数
        st.subheader("🔧 检索参数")
        top_k = st.slider("检索数量 (top_k)", 1, 100, 10)
        chunk_top_k = st.slider("文本块数量 (chunk_top_k)", 1, 20, 5)

        # 响应参数
        response_type = st.selectbox(
            "响应格式",
            ["Multiple Paragraphs", "Single Paragraph", "Bullet Points", "List", "Table"]
        )

        # 输出控制
        only_need_context = st.checkbox("仅返回上下文", help="只返回检索到的相关内容，不生成回答")
        only_need_prompt = st.checkbox("仅返回提示词", help="只返回生成的提示词，不生成回答")

    # 查询输入
    st.subheader("💭 查询内容")
    query = st.text_area(
        "请输入您的问题:",
        height=100,
        placeholder="例如：计算机学院的研究方向有哪些？"
    )

    # 执行查询
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🚀 执行查询", type="primary", use_container_width=True):
            if query.strip():
                execute_advanced_query(
                    query=query,
                    mode=query_mode,
                    knowledge_base_ids=selected_kbs,
                    top_k=top_k,
                    chunk_top_k=chunk_top_k,
                    response_type=response_type,
                    only_need_context=only_need_context,
                    only_need_prompt=only_need_prompt
                )
            else:
                st.warning("请输入查询内容")

    with col2:
        if st.button("🌊 流式查询", use_container_width=True):
            if query.strip():
                execute_stream_query(query, query_mode, selected_kbs)
            else:
                st.warning("请输入查询内容")

    with col3:
        if st.button("🔄 批量查询", use_container_width=True):
            st.info("批量查询功能开发中...")


def execute_advanced_query(query: str, **kwargs):
    """执行高级查询"""
    try:
        with st.spinner("正在查询..."):
            payload = {
                "query": query,
                **kwargs
            }

            response = requests.post(
                f"{API_BASE_URL}/query/advanced",
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('data', {})

                    st.success("✅ 查询成功")

                    # 显示结果
                    if kwargs.get('only_need_context'):
                        st.subheader("📄 检索上下文")
                        st.write(data.get('context', ''))
                    elif kwargs.get('only_need_prompt'):
                        st.subheader("📝 生成提示词")
                        st.code(data.get('prompt', ''), language='text')
                    else:
                        st.subheader("💡 查询结果")
                        st.write(data.get('response', ''))

                    # 显示元数据
                    with st.expander("📊 查询详情"):
                        st.json({
                            "模式": data.get('mode'),
                            "知识库": data.get('knowledge_bases'),
                            "查询时间": data.get('timestamp'),
                            "参数": {k: v for k, v in kwargs.items() if k not in ['query']}
                        })
                else:
                    st.error(f"查询失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"请求失败: {response.status_code}")

    except Exception as e:
        st.error(f"查询异常: {e}")


def execute_stream_query(query: str, mode: str, kb_ids: List[str]):
    """执行流式查询"""
    try:
        payload = {
            "query": query,
            "mode": mode,
            "knowledge_base_ids": kb_ids
        }

        response = requests.post(
            f"{API_BASE_URL}/query/stream",
            json=payload,
            stream=True,
            timeout=60
        )

        if response.status_code == 200:
            st.subheader("🌊 流式查询结果")

            # 创建占位符
            result_placeholder = st.empty()
            full_response = ""

            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    try:
                        chunk_data = json.loads(line.decode('utf-8'))
                        if chunk_data.get('type') == 'chunk':
                            full_response += chunk_data.get('content', '')
                            result_placeholder.write(full_response)
                        elif chunk_data.get('type') == 'error':
                            st.error(f"流式查询错误: {chunk_data.get('error')}")
                            break
                    except json.JSONDecodeError:
                        continue
        else:
            st.error(f"流式查询失败: {response.status_code}")

    except Exception as e:
        st.error(f"流式查询异常: {e}")


def system_status_page():
    """系统状态页面"""
    st.header("📊 系统状态")

    # 服务状态
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("🔧 服务状态")
        status_ok, status_data = check_api_status()
        if status_ok:
            st.success("✅ API 服务正常")
            if status_data:
                config = status_data.get('data', {}).get('config', {})
                st.write(f"**LLM模型**: {config.get('llm_model', 'Unknown')}")
                st.write(f"**嵌入模型**: {config.get('embedding_model', 'Unknown')}")
                st.write(f"**流式输出**: {'启用' if config.get('stream_enabled') else '禁用'}")
        else:
            st.error("❌ API 服务异常")

    with col2:
        st.subheader("📚 知识库状态")
        if st.session_state.knowledge_bases:
            total_kbs = len(st.session_state.knowledge_bases)
            enabled_kbs = sum(1 for kb in st.session_state.knowledge_bases.values() if kb.get('enabled'))
            st.metric("总知识库数", total_kbs)
            st.metric("启用知识库", enabled_kbs)
            st.metric("启用率", f"{enabled_kbs/total_kbs*100:.1f}%" if total_kbs > 0 else "0%")
        else:
            st.warning("无知识库数据")

    with col3:
        st.subheader("📈 图谱统计")
        if hasattr(st.session_state, 'graph_stats') and st.session_state.graph_stats:
            stats = st.session_state.graph_stats
            st.metric("总节点数", stats.get('total_nodes', 0))
            st.metric("总边数", stats.get('total_edges', 0))
            st.metric("已加载知识库", stats.get('loaded_kbs', 0))
        else:
            st.info("正在加载图谱统计...")
            load_graph_stats()


def test_tools_page():
    """测试工具页面"""
    st.header("🧪 测试工具")

    # 查询模式测试
    st.subheader("🔍 查询模式测试")

    test_query = st.text_input("测试查询", value="计算机学院院长是谁？")

    if st.button("🚀 测试所有查询模式"):
        if test_query.strip():
            for mode_key, mode_info in QUERY_MODES.items():
                with st.expander(f"{mode_info['icon']} {mode_info['name']}"):
                    try:
                        with st.spinner(f"测试 {mode_key} 模式..."):
                            payload = {
                                "query": test_query,
                                "mode": mode_key,
                                "knowledge_base_ids": ["cs_college"] if mode_key != "bypass" else None,
                                "top_k": 5
                            }

                            start_time = time.time()
                            response = requests.post(
                                f"{API_BASE_URL}/query/advanced",
                                json=payload,
                                timeout=30
                            )
                            query_time = time.time() - start_time

                            if response.status_code == 200:
                                result = response.json()
                                if result.get('success'):
                                    st.success(f"✅ 成功 ({query_time:.2f}s)")
                                    st.write(result.get('data', {}).get('response', '')[:200] + "...")
                                else:
                                    st.error(f"❌ 失败: {result.get('message')}")
                            else:
                                st.error(f"❌ HTTP {response.status_code}")
                    except Exception as e:
                        st.error(f"❌ 异常: {e}")
        else:
            st.warning("请输入测试查询")


def main():
    """主函数"""
    # 初始化数据加载
    if not st.session_state.knowledge_bases:
        load_knowledge_bases()
        load_query_modes()
        load_query_templates()
        load_graph_stats()

    # 侧边栏
    page = sidebar()

    # 主页面路由
    if page == "💬 智能问答":
        chat_page()
    elif page == "🔍 高级查询":
        advanced_query_page()
    elif page == "📚 知识库管理":
        knowledge_base_page()
    elif page == "📊 系统状态":
        system_status_page()
    elif page == "📈 数据分析":
        st.header("📈 数据分析")
        st.info("数据分析页面开发中...")
    elif page == "🧪 测试工具":
        test_tools_page()
    elif page == "⚙️ 系统设置":
        st.header("⚙️ 系统设置")
        st.info("系统设置页面开发中...")


if __name__ == "__main__":
    main()
