#!/usr/bin/env python3
"""
最小化的Streamlit应用 - 用于测试启动问题
"""

import streamlit as st
import requests
import json
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="GuiXiaoxi RAG",
    page_icon="🤖",
    layout="wide"
)

# 全局变量
API_BASE_URL = "http://localhost:8000"

def main():
    """主函数"""
    st.title("🤖 GuiXiaoxi RAG 系统")
    st.write("欢迎使用 GuiXiaoxi RAG 智能问答系统")
    
    # 显示当前时间
    st.write(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # API状态检查
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=3)
        if response.status_code == 200:
            st.success("✅ API 服务正常")
        else:
            st.error("❌ API 服务异常")
    except:
        st.warning("⚠️ 无法连接到API服务")
    
    # 简单的查询界面
    st.subheader("💬 智能问答")
    
    query = st.text_input("请输入您的问题:", placeholder="例如：你好")
    
    if st.button("提交查询"):
        if query:
            try:
                payload = {"query": query, "mode": "bypass"}
                response = requests.post(
                    f"{API_BASE_URL}/query/advanced",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        st.success("查询成功！")
                        st.write(result.get('data', {}).get('response', ''))
                    else:
                        st.error(f"查询失败: {result.get('message', '未知错误')}")
                else:
                    st.error(f"请求失败: {response.status_code}")
            except Exception as e:
                st.error(f"查询异常: {e}")
        else:
            st.warning("请输入查询内容")

if __name__ == "__main__":
    main()
