#!/usr/bin/env python3
"""
GuiXiaoxi RAG 完整 Streamlit 应用
实现server中的所有端点功能
"""

import streamlit as st
import requests
import json
import time
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import io
import traceback

# 配置页面
st.set_page_config(
    page_title="GuiXiaoxi RAG 完整系统",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
API_BASE_URL = "http://localhost:8000"

# 样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.section-header {
    font-size: 1.5rem;
    color: #ff7f0e;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
.status-success {
    color: #2ca02c;
    font-weight: bold;
}
.status-error {
    color: #d62728;
    font-weight: bold;
}
.info-box {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

class APIClient:
    """API客户端类"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
    
    def get(self, endpoint: str, **kwargs) -> requests.Response:
        """GET请求"""
        return requests.get(f"{self.base_url}{endpoint}", **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> requests.Response:
        """POST请求"""
        return requests.post(f"{self.base_url}{endpoint}", **kwargs)
    
    def put(self, endpoint: str, **kwargs) -> requests.Response:
        """PUT请求"""
        return requests.put(f"{self.base_url}{endpoint}", **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> requests.Response:
        """DELETE请求"""
        return requests.delete(f"{self.base_url}{endpoint}", **kwargs)

# 初始化API客户端
@st.cache_resource
def get_api_client():
    return APIClient(API_BASE_URL)

api = get_api_client()

def check_api_status():
    """检查API状态"""
    try:
        response = api.get("/health", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, None
    except Exception as e:
        return False, str(e)

def get_system_config():
    """获取系统配置"""
    try:
        response = api.get("/config", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except:
        return None

def get_knowledge_bases():
    """获取知识库列表"""
    try:
        response = api.get("/knowledge-bases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data
        else:
            st.warning(f"获取知识库列表失败: HTTP {response.status_code}")
            # 返回默认知识库信息
            return {
                "success": True,
                "data": [
                    {
                        "id": "cs_college",
                        "description": "计算机学院知识库",
                        "status": "active",
                        "entity_count": "未知",
                        "relationship_count": "未知",
                        "document_count": "未知"
                    }
                ]
            }
    except Exception as e:
        st.warning(f"连接知识库服务失败: {e}")
        # 返回默认知识库信息
        return {
            "success": True,
            "data": [
                {
                    "id": "cs_college",
                    "description": "计算机学院知识库",
                    "status": "active",
                    "entity_count": "未知",
                    "relationship_count": "未知",
                    "document_count": "未知"
                }
            ]
        }

def get_query_modes():
    """获取查询模式"""
    try:
        response = api.get("/query/modes", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except:
        return None

def main():
    """主函数"""
    # 页面标题
    st.markdown('<h1 class="main-header">🤖 GuiXiaoxi RAG 完整系统</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("🔧 系统控制")
        
        # API状态检查
        status_ok, status_data = check_api_status()
        if status_ok:
            st.success("✅ API 服务正常")
            if status_data and 'data' in status_data:
                config = status_data['data'].get('config', {})
                st.caption(f"LLM: {config.get('llm_model', 'Unknown')}")
        else:
            st.error("❌ API 服务异常")
            st.caption(f"错误: {status_data}")
        
        # 页面选择
        page = st.selectbox(
            "选择功能页面",
            [
                "🏠 系统概览",
                "💬 智能问答",
                "📊 流式查询",
                "📚 知识库管理",
                "📁 文件管理",
                "🔍 系统监控",
                "⚙️ 系统配置"
            ]
        )
    
    # 根据选择显示不同页面
    if page == "🏠 系统概览":
        show_system_overview()
    elif page == "💬 智能问答":
        show_query_interface()
    elif page == "📊 流式查询":
        show_streaming_query()
    elif page == "📚 知识库管理":
        show_knowledge_base_management()
    elif page == "📁 文件管理":
        show_file_management()
    elif page == "🔍 系统监控":
        show_system_monitoring()
    elif page == "⚙️ 系统配置":
        show_system_configuration()

def show_system_overview():
    """显示系统概览"""
    st.markdown('<h2 class="section-header">📊 系统概览</h2>', unsafe_allow_html=True)
    
    # 获取系统信息
    config_data = get_system_config()
    kb_data = get_knowledge_bases()
    modes_data = get_query_modes()
    
    # 系统状态卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("API状态", "正常" if check_api_status()[0] else "异常")
    
    with col2:
        kb_count = len(kb_data.get('data', [])) if kb_data else 0
        st.metric("知识库数量", kb_count)
    
    with col3:
        modes_count = len(modes_data.get('data', [])) if modes_data else 0
        st.metric("查询模式", modes_count)
    
    with col4:
        st.metric("当前时间", datetime.now().strftime("%H:%M:%S"))
    
    # 系统配置信息
    if config_data:
        st.subheader("🔧 系统配置")
        config_df = pd.DataFrame([
            {"配置项": "LLM模型", "值": config_data.get('data', {}).get('llm_model', 'N/A')},
            {"配置项": "嵌入模型", "值": config_data.get('data', {}).get('embedding_model', 'N/A')},
            {"配置项": "流式查询", "值": "启用" if config_data.get('data', {}).get('stream_enabled') else "禁用"},
            {"配置项": "通用问答", "值": "启用" if config_data.get('data', {}).get('general_qa_enabled') else "禁用"},
        ])
        st.dataframe(config_df, use_container_width=True)
    
    # 知识库信息
    if kb_data:
        st.subheader("📚 知识库状态")
        kb_list = kb_data.get('data', [])
        if kb_list:
            kb_df = pd.DataFrame(kb_list)
            st.dataframe(kb_df, use_container_width=True)
        else:
            st.info("暂无知识库")

def show_query_interface():
    """显示查询界面"""
    st.markdown('<h2 class="section-header">💬 智能问答</h2>', unsafe_allow_html=True)
    
    # 获取查询模式和知识库
    modes_data = get_query_modes()
    kb_data = get_knowledge_bases()
    
    # 查询参数设置
    col1, col2 = st.columns(2)
    
    with col1:
        # 查询模式选择
        if modes_data and 'data' in modes_data and isinstance(modes_data['data'], list):
            try:
                mode_options = {mode['name']: mode['description'] for mode in modes_data['data']}
                selected_mode = st.selectbox(
                    "查询模式",
                    options=list(mode_options.keys()),
                    help="选择查询模式"
                )
                st.caption(f"说明: {mode_options.get(selected_mode, '')}")
            except (KeyError, TypeError):
                # 如果数据格式不正确，使用默认选项
                selected_mode = st.selectbox(
                    "查询模式",
                    ["bypass", "hybrid", "local", "global", "naive", "mix"]
                )
        else:
            selected_mode = st.selectbox(
                "查询模式",
                ["bypass", "hybrid", "local", "global", "naive", "mix"]
            )
    
    with col2:
        # 知识库选择
        if kb_data and 'data' in kb_data:
            kb_options = [kb['id'] for kb in kb_data['data']]
            selected_kbs = st.multiselect(
                "知识库",
                options=kb_options,
                default=kb_options[:1] if kb_options else [],
                help="选择要查询的知识库"
            )
        else:
            selected_kbs = st.multiselect(
                "知识库",
                ["cs_college"],
                default=["cs_college"]
            )
    
    # 高级参数
    with st.expander("🔧 高级参数"):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            response_type = st.selectbox(
                "响应类型",
                ["Multiple Paragraphs", "Single Paragraph", "Bullet Points", "Single Sentence"]
            )
        
        with col2:
            top_k = st.slider("Top K", 1, 20, 10, help="返回结果数量")
        
        with col3:
            max_tokens = st.slider("最大Token数", 100, 4000, 1000, help="响应最大长度")
    
    # 查询输入
    query = st.text_area(
        "请输入您的问题:",
        height=100,
        placeholder="例如：计算机学院院长是谁？"
    )
    
    # 执行查询
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🚀 执行查询", type="primary"):
            if query.strip():
                execute_query(query, selected_mode, selected_kbs, response_type, top_k, max_tokens)
            else:
                st.warning("请输入查询内容")
    
    with col2:
        if st.button("🌊 流式查询"):
            if query.strip():
                execute_streaming_query(query, selected_mode, selected_kbs)
            else:
                st.warning("请输入查询内容")
    
    with col3:
        if st.button("🔄 清空结果"):
            st.rerun()

def execute_query(query: str, mode: str, kb_ids: List[str], response_type: str, top_k: int, max_tokens: int):
    """执行查询"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    try:
        status_text.text("🔄 准备查询请求...")
        progress_bar.progress(10)

        payload = {
            "query": query,
            "mode": mode,
            "response_type": response_type,
            "top_k": top_k,
            "max_tokens": max_tokens
        }

        if kb_ids and mode != "bypass":
            payload["knowledge_base_ids"] = kb_ids

        status_text.text("📡 发送查询请求...")
        progress_bar.progress(30)

        # 增加超时时间并添加重试机制
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                timeout = 90 if attempt == 0 else 120  # 第一次90秒，重试时120秒
                response = api.post("/query/advanced", json=payload, timeout=timeout)
                break
            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    status_text.text(f"⏰ 请求超时，正在重试 ({attempt + 1}/{max_retries})...")
                    progress_bar.progress(20 + attempt * 10)
                    time.sleep(2)
                    continue
                else:
                    raise

        progress_bar.progress(70)
        status_text.text("📊 处理响应数据...")

        if response.status_code == 200:
            result = response.json()
            progress_bar.progress(90)

            if result.get('success'):
                progress_bar.progress(100)
                status_text.empty()
                progress_bar.empty()

                st.success("✅ 查询成功")
                data = result.get('data', {})

                # 显示结果
                st.subheader("💡 查询结果")
                response_text = data.get('response', '')
                if response_text:
                    st.markdown(response_text)
                else:
                    st.warning("未获得有效响应")

                # 显示元数据
                with st.expander("📊 查询详情"):
                    metadata = {
                        "查询模式": data.get('mode', mode),
                        "知识库": data.get('knowledge_bases', kb_ids),
                        "响应类型": data.get('response_type', response_type),
                        "处理时间": f"{data.get('processing_time', 0):.2f}秒",
                        "查询时间": data.get('timestamp', 'N/A')
                    }
                    st.json(metadata)
            else:
                progress_bar.empty()
                status_text.empty()
                error_msg = result.get('message', '未知错误')
                st.error(f"❌ 查询失败: {error_msg}")

                # 显示详细错误信息
                if 'error_details' in result:
                    with st.expander("🔍 错误详情"):
                        st.json(result['error_details'])
        else:
            progress_bar.empty()
            status_text.empty()
            st.error(f"❌ 请求失败: HTTP {response.status_code}")

            # 显示响应内容以便调试
            if response.text:
                with st.expander("🔍 响应详情"):
                    st.text(response.text[:1000])  # 只显示前1000字符

    except requests.exceptions.Timeout:
        progress_bar.empty()
        status_text.empty()
        st.error("⏰ 请求超时，请稍后重试或检查网络连接")
    except requests.exceptions.ConnectionError:
        progress_bar.empty()
        status_text.empty()
        st.error("🔌 连接失败，请检查API服务是否正常运行")
    except Exception as e:
        progress_bar.empty()
        status_text.empty()
        st.error(f"❌ 查询异常: {e}")

        # 显示详细错误信息用于调试
        with st.expander("🔍 详细错误信息"):
            import traceback
            st.text(traceback.format_exc())

def execute_streaming_query(query: str, mode: str, kb_ids: List[str]):
    """执行流式查询"""
    st.subheader("🌊 流式查询结果")

    # 创建占位符
    result_placeholder = st.empty()
    status_placeholder = st.empty()

    try:
        payload = {
            "query": query,
            "mode": mode
        }

        if kb_ids and mode != "bypass":
            payload["knowledge_base_ids"] = kb_ids

        status_placeholder.info("🔄 正在连接流式查询...")

        # 发送流式请求
        response = api.post("/query/stream", json=payload, stream=True, timeout=120)

        if response.status_code == 200:
            accumulated_text = ""
            status_placeholder.info("📡 正在接收数据流...")

            for line in response.iter_lines():
                if line:
                    try:
                        # 解析SSE数据
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_text = line_text[6:]  # 移除 'data: ' 前缀

                            # 处理特殊的结束标记
                            if data_text.strip() == '[DONE]':
                                status_placeholder.success("✅ 流式查询完成")
                                break

                            try:
                                data = json.loads(data_text)

                                if data.get('type') == 'chunk':
                                    chunk = data.get('content', '')
                                    accumulated_text += chunk
                                    result_placeholder.markdown(accumulated_text)
                                elif data.get('type') == 'delta':
                                    # 处理OpenAI格式的增量数据
                                    delta = data.get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        accumulated_text += content
                                        result_placeholder.markdown(accumulated_text)
                                elif data.get('type') == 'end':
                                    status_placeholder.success("✅ 流式查询完成")
                                    break
                                elif data.get('type') == 'error':
                                    status_placeholder.error(f"❌ 查询错误: {data.get('error')}")
                                    break
                            except json.JSONDecodeError:
                                # 可能是纯文本流，直接添加
                                accumulated_text += data_text
                                result_placeholder.markdown(accumulated_text)
                    except Exception as line_error:
                        # 忽略单行解析错误，继续处理下一行
                        continue

            if not accumulated_text:
                status_placeholder.warning("⚠️ 未收到任何响应数据")
        else:
            st.error(f"流式查询失败: HTTP {response.status_code}")
            if response.text:
                st.error(f"错误详情: {response.text}")

    except Exception as e:
        st.error(f"流式查询异常: {e}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")

def show_streaming_query():
    """显示流式查询页面"""
    st.markdown('<h2 class="section-header">📊 流式查询</h2>', unsafe_allow_html=True)

    st.info("流式查询可以实时显示AI的思考过程，提供更好的用户体验。")

    # 查询参数
    col1, col2 = st.columns(2)

    with col1:
        mode = st.selectbox("查询模式", ["bypass", "hybrid", "local", "global"])

    with col2:
        kb_ids = st.multiselect("知识库", ["cs_college"], default=["cs_college"])

    query = st.text_area("请输入问题:", placeholder="例如：请详细介绍计算机学院的发展历程")

    if st.button("🌊 开始流式查询", type="primary"):
        if query.strip():
            execute_streaming_query(query, mode, kb_ids)
        else:
            st.warning("请输入查询内容")

def show_knowledge_base_management():
    """显示知识库管理页面"""
    st.markdown('<h2 class="section-header">📚 知识库管理</h2>', unsafe_allow_html=True)

    # 获取知识库列表
    kb_data = get_knowledge_bases()

    if kb_data and 'data' in kb_data:
        st.subheader("📋 知识库列表")

        for kb in kb_data['data']:
            with st.expander(f"📚 {kb.get('id', 'Unknown')} - {kb.get('description', 'No description')}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**ID**: {kb.get('id')}")
                    st.write(f"**描述**: {kb.get('description', 'N/A')}")
                    st.write(f"**状态**: {kb.get('status', 'N/A')}")

                with col2:
                    st.write(f"**实体数量**: {kb.get('entity_count', 0)}")
                    st.write(f"**关系数量**: {kb.get('relationship_count', 0)}")
                    st.write(f"**文档数量**: {kb.get('document_count', 0)}")

                # 知识库操作
                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button(f"🔄 重建索引", key=f"rebuild_{kb.get('id')}"):
                        rebuild_knowledge_base(kb.get('id'))

                with col2:
                    if st.button(f"📊 查看统计", key=f"stats_{kb.get('id')}"):
                        show_kb_statistics(kb.get('id'))

                with col3:
                    if st.button(f"🗑️ 删除", key=f"delete_{kb.get('id')}"):
                        delete_knowledge_base(kb.get('id'))
    else:
        st.warning("无法获取知识库信息")

    # 创建新知识库
    st.subheader("➕ 创建新知识库")

    with st.form("create_kb_form"):
        new_kb_id = st.text_input("知识库ID")
        new_kb_desc = st.text_area("描述")

        if st.form_submit_button("创建知识库"):
            if new_kb_id:
                create_knowledge_base(new_kb_id, new_kb_desc)
            else:
                st.error("请输入知识库ID")

def show_file_management():
    """显示文件管理页面"""
    st.markdown('<h2 class="section-header">📁 文件管理</h2>', unsafe_allow_html=True)

    # 文件上传
    st.subheader("📤 文件上传")

    uploaded_files = st.file_uploader(
        "选择要上传的文件",
        accept_multiple_files=True,
        type=['txt', 'pdf', 'docx', 'md']
    )

    if uploaded_files:
        kb_id = st.selectbox("选择目标知识库", ["cs_college", "general"])

        if st.button("🚀 上传并处理文件"):
            upload_files(uploaded_files, kb_id)

    # 文件列表
    st.subheader("📋 已上传文件")

    try:
        response = api.get("/files", timeout=10)
        if response.status_code == 200:
            files_data = response.json()
            if files_data.get('success') and files_data.get('data'):
                files_df = pd.DataFrame(files_data['data'])
                st.dataframe(files_df, use_container_width=True)
            else:
                st.info("暂无文件")
        else:
            st.error("无法获取文件列表")
    except Exception as e:
        st.error(f"获取文件列表失败: {e}")

def show_system_monitoring():
    """显示系统监控页面"""
    st.markdown('<h2 class="section-header">🔍 系统监控</h2>', unsafe_allow_html=True)

    # 实时状态监控
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 系统状态")

        # 获取系统状态
        status_ok, status_data = check_api_status()

        if status_ok:
            st.success("✅ API服务正常")

            # 显示详细状态
            if status_data and 'data' in status_data:
                data = status_data['data']
                st.metric("查询管理器", "正常" if data.get('query_manager') else "异常")
                st.metric("插入管理器", "正常" if data.get('insert_manager') else "异常")
                st.metric("服务运行时间", f"{data.get('timestamp', 0):.1f}秒")
        else:
            st.error("❌ API服务异常")

    with col2:
        st.subheader("📈 性能指标")

        # 这里可以添加性能监控图表
        st.info("性能监控功能开发中...")

    # 日志查看
    st.subheader("📜 系统日志")

    if st.button("🔄 刷新日志"):
        try:
            # 这里可以添加日志获取API
            st.text_area("系统日志", "日志功能开发中...", height=200)
        except Exception as e:
            st.error(f"获取日志失败: {e}")

def show_system_configuration():
    """显示系统配置页面"""
    st.markdown('<h2 class="section-header">⚙️ 系统配置</h2>', unsafe_allow_html=True)

    # 获取当前配置
    config_data = get_system_config()

    if config_data and 'data' in config_data:
        st.subheader("🔧 当前配置")

        config = config_data['data']

        # 显示配置表格
        config_items = [
            {"配置项": "LLM模型", "当前值": config.get('llm_model', 'N/A'), "类型": "文本"},
            {"配置项": "嵌入模型", "当前值": config.get('embedding_model', 'N/A'), "类型": "文本"},
            {"配置项": "流式查询", "当前值": config.get('stream_enabled', False), "类型": "布尔"},
            {"配置项": "通用问答", "当前值": config.get('general_qa_enabled', False), "类型": "布尔"},
        ]

        config_df = pd.DataFrame(config_items)
        st.dataframe(config_df, use_container_width=True)

        # 配置修改
        st.subheader("✏️ 修改配置")
        st.warning("配置修改功能需要管理员权限，请谨慎操作。")

        with st.form("config_form"):
            new_llm_model = st.text_input("LLM模型", value=config.get('llm_model', ''))
            new_embedding_model = st.text_input("嵌入模型", value=config.get('embedding_model', ''))
            new_stream_enabled = st.checkbox("启用流式查询", value=config.get('stream_enabled', False))
            new_general_qa = st.checkbox("启用通用问答", value=config.get('general_qa_enabled', False))

            if st.form_submit_button("💾 保存配置"):
                update_config({
                    'llm_model': new_llm_model,
                    'embedding_model': new_embedding_model,
                    'stream_enabled': new_stream_enabled,
                    'general_qa_enabled': new_general_qa
                })
    else:
        st.error("无法获取系统配置")

# 辅助函数
def rebuild_knowledge_base(kb_id: str):
    """重建知识库"""
    try:
        response = api.post(f"/knowledge-bases/{kb_id}/rebuild", timeout=60)
        if response.status_code == 200:
            st.success(f"知识库 {kb_id} 重建成功")
        else:
            st.error(f"重建失败: HTTP {response.status_code}")
    except Exception as e:
        st.error(f"重建异常: {e}")

def show_kb_statistics(kb_id: str):
    """显示知识库统计"""
    try:
        response = api.get(f"/knowledge-bases/{kb_id}/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            st.json(stats)
        else:
            st.error("获取统计信息失败")
    except Exception as e:
        st.error(f"获取统计异常: {e}")

def delete_knowledge_base(kb_id: str):
    """删除知识库"""
    if st.confirm(f"确定要删除知识库 {kb_id} 吗？此操作不可恢复！"):
        try:
            response = api.delete(f"/knowledge-bases/{kb_id}", timeout=30)
            if response.status_code == 200:
                st.success(f"知识库 {kb_id} 删除成功")
                st.rerun()
            else:
                st.error(f"删除失败: HTTP {response.status_code}")
        except Exception as e:
            st.error(f"删除异常: {e}")

def create_knowledge_base(kb_id: str, description: str):
    """创建知识库"""
    try:
        payload = {"id": kb_id, "description": description}
        response = api.post("/knowledge-bases", json=payload, timeout=30)
        if response.status_code == 200:
            st.success(f"知识库 {kb_id} 创建成功")
            st.rerun()
        else:
            st.error(f"创建失败: HTTP {response.status_code}")
    except Exception as e:
        st.error(f"创建异常: {e}")

def upload_files(files, kb_id: str):
    """上传文件"""
    try:
        files_data = []
        for file in files:
            files_data.append(('files', (file.name, file.getvalue(), file.type)))

        data = {'knowledge_base_id': kb_id}
        response = api.post("/files/upload", files=files_data, data=data, timeout=120)

        if response.status_code == 200:
            st.success("文件上传成功")
        else:
            st.error(f"上传失败: HTTP {response.status_code}")
    except Exception as e:
        st.error(f"上传异常: {e}")

def update_config(config: Dict[str, Any]):
    """更新配置"""
    try:
        response = api.put("/config", json=config, timeout=30)
        if response.status_code == 200:
            st.success("配置更新成功")
            st.rerun()
        else:
            st.error(f"更新失败: HTTP {response.status_code}")
    except Exception as e:
        st.error(f"更新异常: {e}")

if __name__ == "__main__":
    main()
