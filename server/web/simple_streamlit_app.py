#!/usr/bin/env python3
"""
GuiXiaoxi RAG 简化 Streamlit 应用
用于测试和调试
"""

import streamlit as st
import requests
import json
import time
from datetime import datetime

# 配置页面
st.set_page_config(
    page_title="GuiXiaoxi RAG 测试界面",
    page_icon="🤖",
    layout="wide"
)

# 全局配置
API_BASE_URL = "http://localhost:8000"

def check_api_status():
    """检查API状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, None
    except:
        return False, None

def main():
    """主函数"""
    st.title("🤖 GuiXiaoxi RAG 测试界面")
    
    # 检查API状态
    status_ok, status_data = check_api_status()
    
    if status_ok:
        st.success("✅ API 服务正常")
        if status_data:
            config = status_data.get('data', {}).get('config', {})
            st.caption(f"LLM: {config.get('llm_model', 'Unknown')}")
    else:
        st.error("❌ API 服务异常")
        st.stop()
    
    st.divider()
    
    # 查询界面
    st.subheader("💬 智能问答")
    
    # 查询模式选择
    col1, col2 = st.columns(2)
    
    with col1:
        query_mode = st.selectbox(
            "查询模式",
            ["bypass", "hybrid", "local", "global", "naive", "mix"],
            help="选择查询模式"
        )
    
    with col2:
        if query_mode != "bypass":
            kb_ids = st.multiselect(
                "知识库",
                ["cs_college"],
                default=["cs_college"],
                help="选择要查询的知识库"
            )
        else:
            kb_ids = []
    
    # 查询输入
    query = st.text_area(
        "请输入您的问题:",
        height=100,
        placeholder="例如：计算机学院院长是谁？"
    )
    
    # 执行查询
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🚀 执行查询", type="primary"):
            if query.strip():
                with st.spinner("正在查询..."):
                    try:
                        payload = {
                            "query": query,
                            "mode": query_mode
                        }
                        
                        if kb_ids:
                            payload["knowledge_base_ids"] = kb_ids
                        
                        response = requests.post(
                            f"{API_BASE_URL}/query/advanced",
                            json=payload,
                            timeout=60
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                st.success("✅ 查询成功")
                                data = result.get('data', {})
                                
                                # 显示结果
                                st.subheader("💡 查询结果")
                                st.write(data.get('response', ''))
                                
                                # 显示元数据
                                with st.expander("📊 查询详情"):
                                    st.json({
                                        "模式": data.get('mode'),
                                        "知识库": data.get('knowledge_bases'),
                                        "查询时间": data.get('timestamp')
                                    })
                            else:
                                st.error(f"查询失败: {result.get('message', '未知错误')}")
                        else:
                            st.error(f"请求失败: {response.status_code}")
                            
                    except Exception as e:
                        st.error(f"查询异常: {e}")
            else:
                st.warning("请输入查询内容")
    
    with col2:
        if st.button("🌊 流式查询"):
            if query.strip():
                st.info("流式查询功能开发中...")
            else:
                st.warning("请输入查询内容")
    
    # 系统信息
    st.divider()
    st.subheader("📊 系统信息")
    
    if st.button("刷新状态"):
        st.rerun()
    
    # 显示当前时间
    st.caption(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示API状态详情
    if status_data:
        with st.expander("API 详细信息"):
            st.json(status_data)

if __name__ == "__main__":
    main()
