#!/usr/bin/env python3
"""
GuiXiaoxi RAG 测试应用
用于验证API功能和流式查询
"""

import streamlit as st
import requests
import json
import time

# 配置页面
st.set_page_config(
    page_title="GuiXiaoxi RAG 测试",
    page_icon="🧪",
    layout="wide"
)

# 全局配置
API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def test_query_modes():
    """测试查询模式获取"""
    try:
        response = requests.get(f"{API_BASE_URL}/query/modes", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def test_simple_query(query: str, mode: str = "bypass"):
    """测试简单查询"""
    try:
        payload = {
            "query": query,
            "mode": mode
        }
        
        response = requests.post(
            f"{API_BASE_URL}/query/advanced",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            return True, response.json()
        return False, f"HTTP {response.status_code}: {response.text[:200]}"
    except Exception as e:
        return False, str(e)

def test_streaming_query(query: str, mode: str = "bypass"):
    """测试流式查询"""
    try:
        payload = {
            "query": query,
            "mode": mode
        }
        
        response = requests.post(
            f"{API_BASE_URL}/query/stream",
            json=payload,
            stream=True,
            timeout=60
        )
        
        if response.status_code == 200:
            return True, response
        return False, f"HTTP {response.status_code}: {response.text[:200]}"
    except Exception as e:
        return False, str(e)

def main():
    """主函数"""
    st.title("🧪 GuiXiaoxi RAG 测试应用")
    
    # 侧边栏
    with st.sidebar:
        st.header("🔧 测试控制")
        
        # API状态检查
        if st.button("🔍 检查API状态"):
            with st.spinner("检查中..."):
                success, data = test_api_health()
                if success:
                    st.success("✅ API服务正常")
                    st.json(data)
                else:
                    st.error(f"❌ API服务异常: {data}")
        
        # 查询模式测试
        if st.button("📋 获取查询模式"):
            with st.spinner("获取中..."):
                success, data = test_query_modes()
                if success:
                    st.success("✅ 查询模式获取成功")
                    st.json(data)
                else:
                    st.error(f"❌ 获取失败: {data}")
    
    # 主要内容区域
    tab1, tab2, tab3 = st.tabs(["🔍 简单查询", "🌊 流式查询", "📊 测试结果"])
    
    with tab1:
        st.header("🔍 简单查询测试")
        
        col1, col2 = st.columns(2)
        
        with col1:
            query = st.text_area("查询内容:", value="你好", height=100)
            mode = st.selectbox("查询模式:", ["bypass", "hybrid", "local", "global", "naive", "mix"])
        
        with col2:
            if st.button("🚀 执行查询", type="primary"):
                if query.strip():
                    with st.spinner("查询中..."):
                        success, result = test_simple_query(query, mode)
                        
                        if success:
                            st.success("✅ 查询成功")
                            
                            if isinstance(result, dict) and result.get('success'):
                                data = result.get('data', {})
                                response_text = data.get('response', '')
                                
                                st.subheader("💡 查询结果")
                                st.write(response_text)
                                
                                with st.expander("📊 详细信息"):
                                    st.json(data)
                            else:
                                st.error(f"查询失败: {result.get('message', '未知错误')}")
                        else:
                            st.error(f"❌ 查询失败: {result}")
                else:
                    st.warning("请输入查询内容")
    
    with tab2:
        st.header("🌊 流式查询测试")
        
        col1, col2 = st.columns(2)
        
        with col1:
            stream_query = st.text_area("流式查询内容:", value="请介绍一下人工智能", height=100)
            stream_mode = st.selectbox("流式查询模式:", ["bypass", "hybrid", "local", "global", "naive", "mix"])
        
        with col2:
            if st.button("🌊 开始流式查询", type="primary"):
                if stream_query.strip():
                    st.subheader("🌊 流式查询结果")
                    
                    # 创建占位符
                    result_placeholder = st.empty()
                    status_placeholder = st.empty()
                    
                    try:
                        success, response = test_streaming_query(stream_query, stream_mode)
                        
                        if success:
                            accumulated_text = ""
                            status_placeholder.info("📡 正在接收数据流...")
                            
                            for line in response.iter_lines():
                                if line:
                                    try:
                                        line_text = line.decode('utf-8')
                                        if line_text.startswith('data: '):
                                            data_text = line_text[6:]
                                            
                                            if data_text.strip() == '[DONE]':
                                                status_placeholder.success("✅ 流式查询完成")
                                                break
                                            
                                            try:
                                                data = json.loads(data_text)
                                                
                                                if data.get('type') == 'delta':
                                                    delta = data.get('delta', {})
                                                    content = delta.get('content', '')
                                                    if content:
                                                        accumulated_text += content
                                                        result_placeholder.markdown(accumulated_text)
                                                elif data.get('type') == 'end':
                                                    status_placeholder.success("✅ 流式查询完成")
                                                    break
                                                elif data.get('type') == 'error':
                                                    status_placeholder.error(f"❌ 查询错误: {data.get('error')}")
                                                    break
                                            except json.JSONDecodeError:
                                                # 可能是纯文本流
                                                accumulated_text += data_text
                                                result_placeholder.markdown(accumulated_text)
                                    except Exception:
                                        continue
                            
                            if not accumulated_text:
                                status_placeholder.warning("⚠️ 未收到任何响应数据")
                        else:
                            st.error(f"❌ 流式查询失败: {response}")
                            
                    except Exception as e:
                        st.error(f"❌ 流式查询异常: {e}")
                else:
                    st.warning("请输入查询内容")
    
    with tab3:
        st.header("📊 测试结果汇总")
        
        if st.button("🔄 运行完整测试"):
            st.subheader("测试进度")
            progress_bar = st.progress(0)
            
            # 测试1: API健康检查
            st.write("1. 测试API健康状态...")
            success, data = test_api_health()
            if success:
                st.success("✅ API健康检查通过")
            else:
                st.error(f"❌ API健康检查失败: {data}")
            progress_bar.progress(25)
            
            # 测试2: 查询模式获取
            st.write("2. 测试查询模式获取...")
            success, data = test_query_modes()
            if success:
                st.success("✅ 查询模式获取成功")
            else:
                st.error(f"❌ 查询模式获取失败: {data}")
            progress_bar.progress(50)
            
            # 测试3: 简单查询
            st.write("3. 测试简单查询...")
            success, data = test_simple_query("你好", "bypass")
            if success:
                st.success("✅ 简单查询测试通过")
            else:
                st.error(f"❌ 简单查询测试失败: {data}")
            progress_bar.progress(75)
            
            # 测试4: 流式查询连接
            st.write("4. 测试流式查询连接...")
            try:
                success, response = test_streaming_query("测试", "bypass")
                if success:
                    st.success("✅ 流式查询连接成功")
                else:
                    st.error(f"❌ 流式查询连接失败: {response}")
            except Exception as e:
                st.error(f"❌ 流式查询测试异常: {e}")
            
            progress_bar.progress(100)
            st.success("🎉 测试完成！")

if __name__ == "__main__":
    main()
