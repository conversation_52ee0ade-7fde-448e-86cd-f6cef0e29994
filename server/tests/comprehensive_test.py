#!/usr/bin/env python3
"""
GuiXiaoxi RAG API 综合测试脚本
测试所有 API 端点的功能和性能
"""

import requests
import json
import time
import sys
from typing import Dict, Any, List
from datetime import datetime


class GuiXiaoxiAPITester:
    """GuiXiaoxi API 综合测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        self.start_time = time.time()
    
    def log_test(self, test_name: str, success: bool, message: str, duration: float = 0, data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        print(f"{status} {test_name}: {message}{duration_str}")
        
        if data and isinstance(data, dict) and len(str(data)) < 200:
            print(f"   📊 数据: {data}")
    
    def test_health_check(self):
        """测试健康检查"""
        try:
            start = time.time()
            response = self.session.get(f"{self.base_url}/health")
            duration = time.time() - start
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("健康检查", True, "服务运行正常", duration, data.get('data'))
                return True
            else:
                self.log_test("健康检查", False, f"HTTP {response.status_code}", duration)
                return False
        except Exception as e:
            self.log_test("健康检查", False, f"连接失败: {e}")
            return False
    
    def test_single_query(self):
        """测试单个查询"""
        test_queries = [
            {
                "query": "计算机科学与技术学院的院长是谁？",
                "mode": "mix",
                "description": "院长查询"
            }
        ]
        
        success_count = 0
        for i, test_case in enumerate(test_queries, 1):
            try:
                start = time.time()
                response = self.session.post(
                    f"{self.base_url}/query",
                    json={
                        "query": test_case["query"],
                        "mode": test_case["mode"]
                    }
                )
                duration = time.time() - start
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result.get('result', '')
                    answer_preview = answer[:100] + "..." if len(answer) > 100 else answer
                    self.log_test(
                        f"查询{i}: {test_case['description']}", 
                        True, 
                        f"查询成功 (模式: {test_case['mode']})", 
                        duration
                    )
                    print(f"   📝 回答预览: {answer_preview}")
                    success_count += 1
                else:
                    self.log_test(
                        f"查询{i}: {test_case['description']}", 
                        False, 
                        f"HTTP {response.status_code}", 
                        duration
                    )
            except Exception as e:
                self.log_test(f"查询{i}: {test_case['description']}", False, f"请求失败: {e}")
        
        return success_count == len(test_queries)
    
    def test_graph_stats(self):
        """测试图统计"""
        try:
            start = time.time()
            response = self.session.get(f"{self.base_url}/graph/stats")
            duration = time.time() - start
            
            if response.status_code == 200:
                stats = response.json()
                data = stats.get('data', {})
                nodes_count = data.get('nodes_count', 'N/A')
                edges_count = data.get('edges_count', 'N/A')
                self.log_test("图统计", True, f"节点: {nodes_count}, 边: {edges_count}", duration)
                return True
            else:
                self.log_test("图统计", False, f"HTTP {response.status_code}", duration)
                return False
        except Exception as e:
            self.log_test("图统计", False, f"请求失败: {e}")
            return False
    
    def run_basic_tests(self):
        """运行基础测试"""
        print("🚀 开始 GuiXiaoxi RAG API 基础测试")
        print("=" * 50)
        
        # 基础连接测试
        if not self.test_health_check():
            print("❌ 服务连接失败，终止测试")
            return False
        
        # 查询功能测试
        print("\n🔍 查询功能测试:")
        self.test_single_query()
        
        # 图数据测试
        print("\n📊 图数据测试:")
        self.test_graph_stats()
        
        # 生成测试报告
        self.generate_report()
        
        return True
    
    def generate_report(self):
        """生成测试报告"""
        total_time = time.time() - self.start_time
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%")
        print(f"总耗时: {total_time:.2f} 秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")


def main():
    """主函数"""
    tester = GuiXiaoxiAPITester()
    success = tester.run_basic_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
