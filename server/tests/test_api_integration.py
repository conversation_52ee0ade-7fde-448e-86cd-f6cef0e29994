#!/usr/bin/env python3
"""
GuiXiaoxi RAG API 集成测试
测试所有API端点和功能
"""

import requests
import json
import time
import tempfile
import os
from pathlib import Path
from typing import Dict, Any, List

class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def check_service(self) -> bool:
        """检查服务是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def test_health_endpoint(self) -> Dict[str, Any]:
        """测试健康检查端点"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return {
                "endpoint": "/health",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/health",
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_config_endpoint(self) -> Dict[str, Any]:
        """测试配置端点"""
        try:
            response = self.session.get(f"{self.base_url}/config", timeout=5)
            return {
                "endpoint": "/config",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/config",
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_knowledge_bases_endpoint(self) -> Dict[str, Any]:
        """测试知识库列表端点"""
        try:
            response = self.session.get(f"{self.base_url}/knowledge-bases", timeout=10)
            return {
                "endpoint": "/knowledge-bases",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/knowledge-bases",
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_query_modes_endpoint(self) -> Dict[str, Any]:
        """测试查询模式端点"""
        try:
            response = self.session.get(f"{self.base_url}/query/modes", timeout=5)
            return {
                "endpoint": "/query/modes",
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/query/modes",
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_basic_query(self, query: str, mode: str = "hybrid") -> Dict[str, Any]:
        """测试基础查询端点"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "kb_ids": ["cs_college"]
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/query",
                json=payload,
                timeout=30
            )
            query_time = time.time() - start_time
            
            return {
                "endpoint": "/query",
                "mode": mode,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "query_time": query_time,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/query",
                "mode": mode,
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_advanced_query(self, query: str, mode: str = "hybrid", **kwargs) -> Dict[str, Any]:
        """测试高级查询端点"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "knowledge_base_ids": kwargs.get("knowledge_base_ids", ["cs_college"]),
                "top_k": kwargs.get("top_k", 10),
                "response_type": kwargs.get("response_type", "Multiple Paragraphs"),
                **kwargs
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/query/advanced",
                json=payload,
                timeout=60
            )
            query_time = time.time() - start_time
            
            return {
                "endpoint": "/query/advanced",
                "mode": mode,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "query_time": query_time,
                "response": response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                "endpoint": "/query/advanced",
                "mode": mode,
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_stream_query(self, query: str, mode: str = "hybrid") -> Dict[str, Any]:
        """测试流式查询端点"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "knowledge_base_ids": ["cs_college"] if mode != "bypass" else None
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/query/stream",
                json=payload,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                chunks = []
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            chunks.append(chunk_data)
                            if len(chunks) >= 5:  # 限制收集的块数量
                                break
                        except json.JSONDecodeError:
                            continue
                
                query_time = time.time() - start_time
                return {
                    "endpoint": "/query/stream",
                    "mode": mode,
                    "status_code": response.status_code,
                    "success": True,
                    "query_time": query_time,
                    "chunks_count": len(chunks),
                    "chunks": chunks[:3]  # 只返回前3个块
                }
            else:
                return {
                    "endpoint": "/query/stream",
                    "mode": mode,
                    "status_code": response.status_code,
                    "success": False,
                    "response": response.text
                }
                
        except Exception as e:
            return {
                "endpoint": "/query/stream",
                "mode": mode,
                "status_code": None,
                "success": False,
                "error": str(e)
            }
    
    def test_all_query_modes(self, query: str) -> Dict[str, Dict[str, Any]]:
        """测试所有查询模式"""
        modes = ["local", "global", "hybrid", "naive", "mix", "bypass"]
        results = {}
        
        for mode in modes:
            print(f"测试查询模式: {mode}")
            result = self.test_advanced_query(query, mode)
            results[mode] = result
            time.sleep(1)  # 避免请求过快
        
        return results
    
    def test_file_upload(self, kb_id: str = "cs_college") -> Dict[str, Any]:
        """测试文件上传端点"""
        try:
            # 创建临时测试文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write("这是一个测试文档。\n包含一些测试内容用于验证文件上传功能。")
                temp_file_path = f.name
            
            try:
                with open(temp_file_path, 'rb') as f:
                    files = {'file': ('test.txt', f, 'text/plain')}
                    data = {'description': 'API测试上传文件'}
                    
                    response = requests.post(
                        f"{self.base_url}/insert/file/{kb_id}",
                        files=files,
                        data=data,
                        timeout=60
                    )
                
                return {
                    "endpoint": f"/insert/file/{kb_id}",
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": response.json() if response.status_code == 200 else response.text
                }
            finally:
                # 清理临时文件
                os.unlink(temp_file_path)
                
        except Exception as e:
            return {
                "endpoint": f"/insert/file/{kb_id}",
                "status_code": None,
                "success": False,
                "error": str(e)
            }


def run_comprehensive_test():
    """运行全面的API测试"""
    print("🧪 开始GuiXiaoxi RAG API全面测试")
    print("=" * 60)
    
    tester = APITester()
    
    # 检查服务状态
    if not tester.check_service():
        print("❌ API服务未运行，请先启动服务")
        return
    
    print("✅ API服务正常运行")
    print()
    
    # 测试结果收集
    all_results = {}
    
    # 1. 基础端点测试
    print("📋 测试基础端点...")
    basic_endpoints = [
        ("健康检查", tester.test_health_endpoint),
        ("系统配置", tester.test_config_endpoint),
        ("知识库列表", tester.test_knowledge_bases_endpoint),
        ("查询模式", tester.test_query_modes_endpoint)
    ]
    
    for name, test_func in basic_endpoints:
        result = test_func()
        all_results[name] = result
        status = "✅" if result["success"] else "❌"
        print(f"  {status} {name}: {result['status_code']}")
    
    print()
    
    # 2. 查询功能测试
    print("🔍 测试查询功能...")
    test_query = "计算机学院院长是谁？"
    
    # 基础查询测试
    basic_query_result = tester.test_basic_query(test_query)
    all_results["基础查询"] = basic_query_result
    status = "✅" if basic_query_result["success"] else "❌"
    print(f"  {status} 基础查询: {basic_query_result['status_code']}")
    
    # 高级查询测试
    advanced_query_result = tester.test_advanced_query(test_query)
    all_results["高级查询"] = advanced_query_result
    status = "✅" if advanced_query_result["success"] else "❌"
    print(f"  {status} 高级查询: {advanced_query_result['status_code']}")
    
    # 流式查询测试
    stream_query_result = tester.test_stream_query(test_query, "bypass")
    all_results["流式查询"] = stream_query_result
    status = "✅" if stream_query_result["success"] else "❌"
    print(f"  {status} 流式查询: {stream_query_result['status_code']}")
    
    print()
    
    # 3. 查询模式测试
    print("🔄 测试所有查询模式...")
    mode_results = tester.test_all_query_modes(test_query)
    all_results["查询模式"] = mode_results
    
    for mode, result in mode_results.items():
        status = "✅" if result["success"] else "❌"
        time_info = f"({result.get('query_time', 0):.2f}s)" if result.get('query_time') else ""
        print(f"  {status} {mode.upper()}: {result['status_code']} {time_info}")
    
    print()
    
    # 4. 文件上传测试
    print("📤 测试文件上传...")
    upload_result = tester.test_file_upload()
    all_results["文件上传"] = upload_result
    status = "✅" if upload_result["success"] else "❌"
    print(f"  {status} 文件上传: {upload_result['status_code']}")
    
    print()
    
    # 5. 生成测试报告
    print("📊 测试报告")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict) and "success" in results:
            total_tests += 1
            if results["success"]:
                passed_tests += 1
        elif isinstance(results, dict):
            # 查询模式测试结果
            for mode_result in results.values():
                total_tests += 1
                if mode_result.get("success"):
                    passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试结果良好！")
    elif success_rate >= 60:
        print("⚠️ 测试结果一般，需要优化")
    else:
        print("❌ 测试结果较差，需要修复")
    
    return all_results


if __name__ == "__main__":
    run_comprehensive_test()
