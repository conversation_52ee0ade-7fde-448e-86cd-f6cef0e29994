#!/usr/bin/env python3
"""
GuiXiaoxi RAG v2.0 系统测试脚本
测试多知识库、流式输出和通用问答功能
"""

import requests
import json
import time
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

API_BASE_URL = "http://localhost:8000"


def test_api_health():
    """测试 API 健康状态"""
    print("🔍 测试 API 健康状态...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API 服务正常")
            print(f"   状态: {data.get('status')}")
            print(f"   LLM 模型: {data.get('config', {}).get('llm_model')}")
            print(f"   流式输出: {data.get('config', {}).get('stream_enabled')}")
            return True
        else:
            print(f"❌ API 服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        return False


def test_knowledge_bases():
    """测试知识库管理"""
    print("\n📚 测试知识库管理...")
    try:
        response = requests.get(f"{API_BASE_URL}/knowledge-bases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            kbs = data.get('knowledge_bases', [])
            print(f"✅ 发现 {len(kbs)} 个知识库:")
            for kb in kbs:
                status = "✅ 启用" if kb.get('enabled') else "❌ 禁用"
                print(f"   - {kb.get('name')} ({kb.get('kb_id')}): {status}")
            return True
        else:
            print(f"❌ 获取知识库失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 知识库测试失败: {e}")
        return False


def test_insert_text():
    """测试文本插入"""
    print("\n📝 测试文本插入...")
    test_text = """
    贵州大学计算机科学与技术学院成立于1978年，是贵州省最早设立的计算机专业院系之一。
    学院现有计算机科学与技术、软件工程、网络工程、数据科学与大数据技术等专业。
    学院拥有一支高水平的师资队伍，承担多项国家级和省级科研项目。
    """
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/insert/text",
            json={
                "text": test_text,
                "kb_id": "default",
                "description": "测试文档"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文本插入成功")
            print(f"   知识库: {result.get('kb_name')}")
            print(f"   文本长度: {result.get('text_length')}")
            print(f"   插入耗时: {result.get('insert_time', 0):.2f}秒")
            return True
        else:
            print(f"❌ 文本插入失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文本插入异常: {e}")
        return False


def test_single_kb_query():
    """测试单知识库查询"""
    print("\n🔍 测试单知识库查询...")
    try:
        response = requests.post(
            f"{API_BASE_URL}/query",
            json={
                "query": "计算机学院有哪些专业？",
                "kb_ids": ["default"],
                "mode": "hybrid",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 单知识库查询成功")
            print(f"   查询结果: {result.get('result', '')[:100]}...")
            return True
        else:
            print(f"❌ 单知识库查询失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 单知识库查询异常: {e}")
        return False


def test_multi_kb_query():
    """测试多知识库查询"""
    print("\n🔍 测试多知识库查询...")
    try:
        response = requests.post(
            f"{API_BASE_URL}/query",
            json={
                "query": "计算机学院的历史",
                "kb_ids": ["default", "cs_college"],
                "mode": "hybrid",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('results', {})
            print(f"✅ 多知识库查询成功，查询了 {len(results)} 个知识库")
            for kb_id, kb_result in results.items():
                print(f"   {kb_id}: {str(kb_result)[:50]}...")
            return True
        else:
            print(f"❌ 多知识库查询失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 多知识库查询异常: {e}")
        return False


def test_general_qa():
    """测试通用问答"""
    print("\n💬 测试通用问答...")
    try:
        response = requests.post(
            f"{API_BASE_URL}/query/general",
            json={
                "query": "什么是人工智能？",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 通用问答成功")
            print(f"   回答: {result.get('result', '')[:100]}...")
            return True
        else:
            print(f"❌ 通用问答失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 通用问答异常: {e}")
        return False


def test_config():
    """测试配置获取"""
    print("\n⚙️ 测试配置获取...")
    try:
        response = requests.get(f"{API_BASE_URL}/config", timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 配置获取成功")
            print(f"   LLM 模型: {config.get('llm', {}).get('model')}")
            print(f"   嵌入模型: {config.get('embedding', {}).get('model')}")
            print(f"   流式输出: {config.get('rag', {}).get('enable_stream')}")
            print(f"   通用问答: {config.get('rag', {}).get('enable_general_qa')}")
            print(f"   知识库数量: {len(config.get('knowledge_bases', {}))}")
            return True
        else:
            print(f"❌ 配置获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置获取异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🤖 GuiXiaoxi RAG v2.0 系统测试")
    print("=" * 50)
    
    tests = [
        ("API 健康检查", test_api_health),
        ("知识库管理", test_knowledge_bases),
        ("配置获取", test_config),
        ("文本插入", test_insert_text),
        ("单知识库查询", test_single_kb_query),
        ("多知识库查询", test_multi_kb_query),
        ("通用问答", test_general_qa),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 避免请求过快
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
            break
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
