#!/usr/bin/env python3
"""
GuiXiaoxi RAG 部署验证脚本
验证服务部署是否成功并生成部署报告
"""

import requests
import json
import time
import sys
from datetime import datetime


def verify_deployment(base_url="http://localhost:8000"):
    """验证部署状态"""
    print("🚀 GuiXiaoxi RAG 部署验证")
    print("=" * 50)
    
    session = requests.Session()
    results = []
    
    # 1. 健康检查
    try:
        response = session.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务健康检查: 通过")
            print(f"   📊 服务状态: {data.get('data', {})}")
            results.append(("健康检查", True))
        else:
            print(f"❌ 服务健康检查: HTTP {response.status_code}")
            results.append(("健康检查", False))
            return False
    except Exception as e:
        print(f"❌ 服务健康检查: 连接失败 - {e}")
        results.append(("健康检查", False))
        return False
    
    # 2. 查询功能测试
    try:
        start_time = time.time()
        response = session.post(
            f"{base_url}/query",
            json={"query": "计算机科学与技术学院的院长是谁？", "mode": "mix"},
            timeout=60
        )
        duration = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('result', '')
            print(f"✅ 查询功能: 正常 ({duration:.2f}s)")
            print(f"   📝 答案预览: {answer[:100]}...")
            results.append(("查询功能", True))
        else:
            print(f"❌ 查询功能: HTTP {response.status_code}")
            results.append(("查询功能", False))
    except Exception as e:
        print(f"❌ 查询功能: 失败 - {e}")
        results.append(("查询功能", False))
    
    # 3. 图数据验证
    try:
        response = session.get(f"{base_url}/graph/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            data = stats.get('data', {})
            nodes = data.get('nodes_count', 0)
            edges = data.get('edges_count', 0)
            
            if nodes > 0 and edges > 0:
                print(f"✅ 图数据: 正常 (节点: {nodes}, 边: {edges})")
                results.append(("图数据", True))
            else:
                print("❌ 图数据: 数据为空")
                results.append(("图数据", False))
        else:
            print(f"❌ 图数据: HTTP {response.status_code}")
            results.append(("图数据", False))
    except Exception as e:
        print(f"❌ 图数据: 失败 - {e}")
        results.append(("图数据", False))
    
    # 4. API 端点测试
    endpoints = [
        ("/config", "配置端点"),
        ("/query/templates", "查询模板"),
    ]
    
    api_success = 0
    for endpoint, name in endpoints:
        try:
            response = session.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: 可访问")
                api_success += 1
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: 失败 - {e}")
    
    results.append(("API端点", api_success == len(endpoints)))
    
    # 生成报告
    total_tests = len(results)
    successful_tests = sum(1 for _, success in results if success)
    success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    print("\n" + "=" * 50)
    print("📊 验证报告")
    print("=" * 50)
    print(f"总验证项: {total_tests}")
    print(f"成功: {successful_tests}")
    print(f"失败: {total_tests - successful_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 部署验证通过！服务可以正常使用。")
        print(f"🌐 服务地址: {base_url}")
        print(f"📚 API 文档: {base_url}/docs")
        return True
    else:
        print("\n❌ 部署验证失败，需要检查问题。")
        return False


if __name__ == "__main__":
    success = verify_deployment()
    sys.exit(0 if success else 1)
