#!/usr/bin/env python3
"""
GuiXiaoxi RAG 性能测试
测试查询响应时间、并发性能等
"""

import asyncio
import aiohttp
import time
import statistics
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import requests

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_queries = [
            "计算机学院院长是谁？",
            "计算机学院有哪些专业？",
            "人工智能专业的课程有哪些？",
            "软件工程专业的就业方向？",
            "计算机学院的研究方向？",
            "数据科学专业的培养目标？",
            "网络安全专业的核心课程？",
            "计算机学院的师资力量如何？"
        ]
    
    def check_service(self) -> bool:
        """检查服务是否可用"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def single_query_test(self, query: str, mode: str = "hybrid") -> Dict[str, Any]:
        """单次查询测试"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "knowledge_base_ids": ["cs_college"] if mode != "bypass" else None,
                "top_k": 10
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/query/advanced",
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            return {
                "query": query,
                "mode": mode,
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "response_size": len(response.content) if response.content else 0
            }
        except Exception as e:
            return {
                "query": query,
                "mode": mode,
                "success": False,
                "error": str(e),
                "response_time": None
            }
    
    def response_time_test(self, iterations: int = 10) -> Dict[str, Any]:
        """响应时间测试"""
        print(f"🕐 响应时间测试 ({iterations} 次查询)")
        
        results = []
        for i in range(iterations):
            query = self.test_queries[i % len(self.test_queries)]
            result = self.single_query_test(query)
            results.append(result)
            
            if result["success"]:
                print(f"  查询 {i+1}: {result['response_time']:.2f}s")
            else:
                print(f"  查询 {i+1}: 失败")
        
        # 计算统计信息
        successful_results = [r for r in results if r["success"] and r["response_time"]]
        
        if successful_results:
            response_times = [r["response_time"] for r in successful_results]
            stats = {
                "total_queries": iterations,
                "successful_queries": len(successful_results),
                "success_rate": len(successful_results) / iterations * 100,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times)
            }
            
            if len(response_times) > 1:
                stats["std_response_time"] = statistics.stdev(response_times)
            
            return stats
        else:
            return {
                "total_queries": iterations,
                "successful_queries": 0,
                "success_rate": 0,
                "error": "所有查询都失败了"
            }
    
    def concurrent_test(self, concurrent_users: int = 5, queries_per_user: int = 3) -> Dict[str, Any]:
        """并发测试"""
        print(f"🚀 并发测试 ({concurrent_users} 并发用户，每用户 {queries_per_user} 查询)")
        
        def user_queries(user_id: int) -> List[Dict[str, Any]]:
            """单个用户的查询"""
            results = []
            for i in range(queries_per_user):
                query = self.test_queries[(user_id * queries_per_user + i) % len(self.test_queries)]
                result = self.single_query_test(query)
                result["user_id"] = user_id
                results.append(result)
            return results
        
        start_time = time.time()
        all_results = []
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            future_to_user = {
                executor.submit(user_queries, user_id): user_id 
                for user_id in range(concurrent_users)
            }
            
            for future in as_completed(future_to_user):
                user_id = future_to_user[future]
                try:
                    user_results = future.result()
                    all_results.extend(user_results)
                    print(f"  用户 {user_id} 完成")
                except Exception as e:
                    print(f"  用户 {user_id} 失败: {e}")
        
        total_time = time.time() - start_time
        
        # 计算统计信息
        successful_results = [r for r in all_results if r["success"] and r["response_time"]]
        
        if successful_results:
            response_times = [r["response_time"] for r in successful_results]
            total_queries = len(all_results)
            
            stats = {
                "concurrent_users": concurrent_users,
                "queries_per_user": queries_per_user,
                "total_queries": total_queries,
                "successful_queries": len(successful_results),
                "success_rate": len(successful_results) / total_queries * 100,
                "total_test_time": total_time,
                "queries_per_second": total_queries / total_time,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times)
            }
            
            return stats
        else:
            return {
                "concurrent_users": concurrent_users,
                "queries_per_user": queries_per_user,
                "total_queries": len(all_results),
                "successful_queries": 0,
                "success_rate": 0,
                "error": "所有查询都失败了"
            }
    
    def mode_performance_test(self) -> Dict[str, Dict[str, Any]]:
        """查询模式性能测试"""
        print("🔄 查询模式性能测试")
        
        modes = ["local", "global", "hybrid", "naive", "mix", "bypass"]
        test_query = "计算机学院有哪些专业？"
        results = {}
        
        for mode in modes:
            print(f"  测试模式: {mode}")
            mode_results = []
            
            # 每个模式测试5次
            for i in range(5):
                result = self.single_query_test(test_query, mode)
                mode_results.append(result)
            
            # 计算该模式的统计信息
            successful_results = [r for r in mode_results if r["success"] and r["response_time"]]
            
            if successful_results:
                response_times = [r["response_time"] for r in successful_results]
                results[mode] = {
                    "success_rate": len(successful_results) / len(mode_results) * 100,
                    "avg_response_time": statistics.mean(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times)
                }
            else:
                results[mode] = {
                    "success_rate": 0,
                    "error": "所有查询都失败了"
                }
        
        return results
    
    def stream_performance_test(self) -> Dict[str, Any]:
        """流式查询性能测试"""
        print("🌊 流式查询性能测试")
        
        query = "详细介绍计算机学院的发展历史和现状"
        
        try:
            payload = {
                "query": query,
                "mode": "hybrid",
                "knowledge_base_ids": ["cs_college"]
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/query/stream",
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code == 200:
                chunks = []
                first_chunk_time = None
                
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            if chunk_data.get('type') == 'chunk':
                                if first_chunk_time is None:
                                    first_chunk_time = time.time()
                                chunks.append(chunk_data)
                        except json.JSONDecodeError:
                            continue
                
                total_time = time.time() - start_time
                time_to_first_chunk = first_chunk_time - start_time if first_chunk_time else None
                
                return {
                    "success": True,
                    "total_chunks": len(chunks),
                    "total_time": total_time,
                    "time_to_first_chunk": time_to_first_chunk,
                    "chunks_per_second": len(chunks) / total_time if total_time > 0 else 0
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "error": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }


def run_performance_tests():
    """运行性能测试套件"""
    print("⚡ GuiXiaoxi RAG 性能测试套件")
    print("=" * 60)
    
    tester = PerformanceTester()
    
    # 检查服务状态
    if not tester.check_service():
        print("❌ API服务未运行，请先启动服务")
        return
    
    print("✅ API服务正常运行")
    print()
    
    # 1. 响应时间测试
    response_time_stats = tester.response_time_test(10)
    print("\n📊 响应时间统计:")
    if "error" not in response_time_stats:
        print(f"  成功率: {response_time_stats['success_rate']:.1f}%")
        print(f"  平均响应时间: {response_time_stats['avg_response_time']:.2f}s")
        print(f"  最小响应时间: {response_time_stats['min_response_time']:.2f}s")
        print(f"  最大响应时间: {response_time_stats['max_response_time']:.2f}s")
        print(f"  中位数响应时间: {response_time_stats['median_response_time']:.2f}s")
    else:
        print(f"  错误: {response_time_stats['error']}")
    
    print()
    
    # 2. 并发测试
    concurrent_stats = tester.concurrent_test(3, 2)  # 3个并发用户，每用户2个查询
    print("\n📊 并发测试统计:")
    if "error" not in concurrent_stats:
        print(f"  成功率: {concurrent_stats['success_rate']:.1f}%")
        print(f"  总测试时间: {concurrent_stats['total_test_time']:.2f}s")
        print(f"  查询吞吐量: {concurrent_stats['queries_per_second']:.2f} queries/s")
        print(f"  平均响应时间: {concurrent_stats['avg_response_time']:.2f}s")
    else:
        print(f"  错误: {concurrent_stats['error']}")
    
    print()
    
    # 3. 查询模式性能测试
    mode_stats = tester.mode_performance_test()
    print("\n📊 查询模式性能统计:")
    for mode, stats in mode_stats.items():
        if "error" not in stats:
            print(f"  {mode.upper()}: {stats['success_rate']:.1f}% 成功率, {stats['avg_response_time']:.2f}s 平均时间")
        else:
            print(f"  {mode.upper()}: 失败")
    
    print()
    
    # 4. 流式查询性能测试
    stream_stats = tester.stream_performance_test()
    print("📊 流式查询统计:")
    if stream_stats["success"]:
        print(f"  总块数: {stream_stats['total_chunks']}")
        print(f"  总时间: {stream_stats['total_time']:.2f}s")
        if stream_stats['time_to_first_chunk']:
            print(f"  首块时间: {stream_stats['time_to_first_chunk']:.2f}s")
        print(f"  块速率: {stream_stats['chunks_per_second']:.2f} chunks/s")
    else:
        print(f"  失败: {stream_stats.get('error', '未知错误')}")
    
    print("\n🎯 性能测试完成！")


if __name__ == "__main__":
    run_performance_tests()
