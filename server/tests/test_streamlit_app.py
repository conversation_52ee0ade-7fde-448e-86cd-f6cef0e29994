#!/usr/bin/env python3
"""
GuiXiaoxi RAG Streamlit 应用测试
测试所有页面功能和API集成
"""

import requests
import json
import time
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from server.web.streamlit_app import (
    check_api_status, load_knowledge_bases, load_query_modes,
    load_query_templates, load_graph_stats, QUERY_MODES
)

class TestStreamlitApp:
    """Streamlit应用测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_base_url = "http://localhost:8000"
        self.test_query = "计算机学院院长是谁？"
        
    def test_api_status_check(self):
        """测试API状态检查"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "data": {
                "config": {
                    "llm_model": "qwen14b",
                    "embedding_model": "embedding_qwen"
                }
            }
        }
        
        with patch('requests.get', return_value=mock_response):
            status_ok, status_data = check_api_status()
            assert status_ok is True
            assert status_data is not None
            assert status_data["success"] is True
    
    def test_load_knowledge_bases(self):
        """测试知识库加载"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "knowledge_bases": {
                "cs_college": {
                    "name": "计算机学院",
                    "description": "计算机学院知识库",
                    "enabled": True,
                    "status": "loaded"
                }
            }
        }
        
        with patch('requests.get', return_value=mock_response):
            with patch('streamlit.session_state', {}):
                result = load_knowledge_bases()
                assert result is True
    
    def test_query_modes_config(self):
        """测试查询模式配置"""
        assert len(QUERY_MODES) == 6
        assert "local" in QUERY_MODES
        assert "global" in QUERY_MODES
        assert "hybrid" in QUERY_MODES
        assert "naive" in QUERY_MODES
        assert "mix" in QUERY_MODES
        assert "bypass" in QUERY_MODES
        
        # 检查每个模式的必要字段
        for mode_key, mode_info in QUERY_MODES.items():
            assert "name" in mode_info
            assert "description" in mode_info
            assert "icon" in mode_info
            assert "color" in mode_info


class TestAPIIntegration:
    """API集成测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_base_url = "http://localhost:8000"
        self.timeout = 30
        
    def test_health_endpoint(self):
        """测试健康检查端点"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            assert response.status_code == 200
            data = response.json()
            assert data.get("success") is True
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")
    
    def test_knowledge_bases_endpoint(self):
        """测试知识库端点"""
        try:
            response = requests.get(f"{self.api_base_url}/knowledge-bases", timeout=self.timeout)
            assert response.status_code == 200
            data = response.json()
            assert "knowledge_bases" in data
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")
    
    def test_query_modes_endpoint(self):
        """测试查询模式端点"""
        try:
            response = requests.get(f"{self.api_base_url}/query/modes", timeout=self.timeout)
            assert response.status_code == 200
            data = response.json()
            assert data.get("success") is True
            assert "modes" in data.get("data", {})
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")
    
    def test_advanced_query_endpoint(self):
        """测试高级查询端点"""
        try:
            payload = {
                "query": "测试查询",
                "mode": "hybrid",
                "knowledge_base_ids": ["cs_college"],
                "top_k": 5
            }
            
            response = requests.post(
                f"{self.api_base_url}/query/advanced",
                json=payload,
                timeout=self.timeout
            )
            
            # 接受200或500状态码（可能有配置问题）
            assert response.status_code in [200, 500]
            
            if response.status_code == 200:
                data = response.json()
                assert "success" in data
                
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")
    
    def test_stream_query_endpoint(self):
        """测试流式查询端点"""
        try:
            payload = {
                "query": "测试流式查询",
                "mode": "bypass"  # 使用bypass模式避免知识库依赖
            }
            
            response = requests.post(
                f"{self.api_base_url}/query/stream",
                json=payload,
                stream=True,
                timeout=self.timeout
            )
            
            assert response.status_code == 200
            
            # 检查是否返回流式数据
            chunks_received = 0
            for line in response.iter_lines():
                if line:
                    try:
                        chunk_data = json.loads(line.decode('utf-8'))
                        chunks_received += 1
                        if chunks_received >= 3:  # 收到几个块就停止
                            break
                    except json.JSONDecodeError:
                        continue
            
            assert chunks_received > 0
            
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")


class TestStreamlitPages:
    """Streamlit页面功能测试"""
    
    def test_chat_page_elements(self):
        """测试智能问答页面元素"""
        # 这里可以添加Streamlit页面元素测试
        # 由于Streamlit测试框架的限制，主要测试逻辑功能
        pass
    
    def test_advanced_query_page_elements(self):
        """测试高级查询页面元素"""
        # 测试查询模式选项
        mode_options = list(QUERY_MODES.keys())
        assert len(mode_options) == 6
        assert "hybrid" in mode_options
    
    def test_knowledge_base_page_elements(self):
        """测试知识库管理页面元素"""
        pass


class TestQueryModes:
    """查询模式测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_base_url = "http://localhost:8000"
        self.test_query = "计算机学院有哪些专业？"
        
    def test_all_query_modes(self):
        """测试所有查询模式"""
        try:
            # 检查API服务状态
            health_response = requests.get(f"{self.api_base_url}/health", timeout=5)
            if health_response.status_code != 200:
                pytest.skip("API服务未运行")
            
            results = {}
            
            for mode_key in QUERY_MODES.keys():
                try:
                    payload = {
                        "query": self.test_query,
                        "mode": mode_key,
                        "top_k": 5
                    }
                    
                    # 对于非bypass模式，添加知识库
                    if mode_key != "bypass":
                        payload["knowledge_base_ids"] = ["cs_college"]
                    
                    start_time = time.time()
                    response = requests.post(
                        f"{self.api_base_url}/query/advanced",
                        json=payload,
                        timeout=30
                    )
                    query_time = time.time() - start_time
                    
                    results[mode_key] = {
                        "status_code": response.status_code,
                        "query_time": query_time,
                        "success": response.status_code == 200
                    }
                    
                    if response.status_code == 200:
                        data = response.json()
                        results[mode_key]["api_success"] = data.get("success", False)
                    
                except Exception as e:
                    results[mode_key] = {
                        "status_code": None,
                        "error": str(e),
                        "success": False
                    }
            
            # 输出测试结果
            print("\n查询模式测试结果:")
            for mode_key, result in results.items():
                mode_name = QUERY_MODES[mode_key]["name"]
                status = "✅" if result["success"] else "❌"
                print(f"{status} {mode_name}: {result}")
            
            # 至少有一个模式应该成功
            success_count = sum(1 for r in results.values() if r["success"])
            assert success_count > 0, "至少应有一个查询模式成功"
            
        except requests.exceptions.RequestException:
            pytest.skip("API服务未运行")


def run_streamlit_tests():
    """运行Streamlit应用测试"""
    print("🧪 开始Streamlit应用测试...")

    # 简单的测试运行器（不使用pytest）
    test_classes = [TestStreamlitApp, TestAPIIntegration, TestStreamlitPages, TestQueryModes]

    total_tests = 0
    passed_tests = 0

    for test_class in test_classes:
        print(f"\n运行测试类: {test_class.__name__}")
        test_instance = test_class()

        # 获取测试方法
        test_methods = [method for method in dir(test_instance) if method.startswith('test_')]

        for method_name in test_methods:
            total_tests += 1
            try:
                if hasattr(test_instance, 'setup_method'):
                    test_instance.setup_method()

                method = getattr(test_instance, method_name)
                method()

                print(f"  ✅ {method_name}")
                passed_tests += 1
            except Exception as e:
                print(f"  ❌ {method_name}: {e}")

    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
    return passed_tests == total_tests


if __name__ == "__main__":
    run_streamlit_tests()
