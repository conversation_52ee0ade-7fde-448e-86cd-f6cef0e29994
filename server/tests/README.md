# GuiXiaoxi RAG 测试套件

这是GuiXiaoxi RAG系统的完整测试套件，包含API集成测试、性能测试、Streamlit应用测试等。

## 📁 文件结构

```
server/tests/
├── README.md                    # 测试说明文档
├── test_config.py              # 测试配置和参数
├── run_tests.py                # 测试运行器（主入口）
├── test_api_integration.py     # API集成测试
├── test_performance.py         # 性能测试
├── test_streamlit_app.py       # Streamlit应用测试
└── reports/                    # 测试报告目录
    ├── test_report_*.txt       # 文本格式测试报告
    └── test_results_*.json     # JSON格式测试结果
```

## 🚀 快速开始

### 1. 环境准备

确保GuiXiaoxi RAG API服务正在运行：

```bash
# 启动API服务
python start_server.py

# 或使用完整服务启动器
python start_streamlit.py --api-only
```

### 2. 运行测试

#### 运行所有测试
```bash
cd server/tests
python run_tests.py
```

#### 运行特定测试
```bash
# 只运行API集成测试
python run_tests.py --api-only

# 只运行性能测试
python run_tests.py --perf-only

# 只运行Streamlit测试
python run_tests.py --streamlit-only
```

#### 保存JSON格式结果
```bash
python run_tests.py --save-json
```

### 3. 单独运行测试模块

```bash
# API集成测试
python test_api_integration.py

# 性能测试
python test_performance.py

# Streamlit应用测试
python test_streamlit_app.py

# 查看测试配置
python test_config.py
```

## 🧪 测试模块说明

### 1. API集成测试 (`test_api_integration.py`)

测试所有API端点的功能和集成：

- **基础端点测试**：健康检查、配置、知识库列表等
- **查询功能测试**：基础查询、高级查询、流式查询
- **查询模式测试**：测试所有6种查询模式（local, global, hybrid, naive, mix, bypass）
- **文件上传测试**：测试文档上传和处理功能

**主要功能**：
- 自动检测API服务状态
- 全面测试所有端点
- 生成详细的测试报告
- 支持超时和错误处理

### 2. 性能测试 (`test_performance.py`)

测试系统的性能指标：

- **响应时间测试**：测量单次查询的响应时间
- **并发测试**：测试多用户并发查询性能
- **查询模式性能**：比较不同查询模式的性能
- **流式查询性能**：测试流式输出的性能指标

**性能指标**：
- 平均响应时间
- 最小/最大响应时间
- 成功率
- 并发吞吐量
- 流式输出速率

### 3. Streamlit应用测试 (`test_streamlit_app.py`)

测试Streamlit Web界面的功能：

- **页面功能测试**：测试各个页面的基本功能
- **API集成测试**：测试Web界面与API的集成
- **用户交互测试**：测试用户界面元素

### 4. 测试配置 (`test_config.py`)

定义测试参数和配置：

- **API配置**：服务地址、超时设置等
- **测试数据**：测试查询、知识库、模式等
- **性能标准**：可接受的响应时间和成功率
- **环境检查**：自动检查测试环境

## 📊 测试报告

### 报告格式

测试完成后会生成两种格式的报告：

1. **文本报告** (`test_report_*.txt`)：人类可读的详细报告
2. **JSON结果** (`test_results_*.json`)：机器可读的结构化数据

### 报告内容

- 测试执行时间和统计信息
- 各测试套件的成功/失败状态
- 详细的错误信息和诊断数据
- 性能指标和基准对比
- 系统健康状况评估

## 🔧 配置说明

### 环境变量

```bash
# API服务地址（默认：http://localhost:8000）
export TEST_API_URL=http://localhost:8000

# 测试超时时间（秒）
export TEST_TIMEOUT=30
```

### 测试参数调整

编辑 `test_config.py` 文件来调整测试参数：

```python
TEST_CONFIG = {
    "performance": {
        "response_time_iterations": 10,    # 响应时间测试次数
        "concurrent_users": 5,             # 并发用户数
        "acceptable_response_time": 10.0,  # 可接受响应时间（秒）
        "acceptable_success_rate": 80.0    # 可接受成功率（%）
    }
}
```

## 🐛 故障排除

### 常见问题

1. **API服务未运行**
   ```
   ❌ API服务未运行，请先启动服务
   ```
   解决：启动GuiXiaoxi RAG API服务

2. **知识库未加载**
   ```
   ❌ 知识库加载失败
   ```
   解决：检查知识库配置和数据文件

3. **查询超时**
   ```
   查询异常: HTTPConnectionPool timeout
   ```
   解决：增加超时时间或检查服务性能

4. **权限错误**
   ```
   Permission denied: reports/
   ```
   解决：确保有写入reports目录的权限

### 调试模式

使用详细输出运行测试：

```bash
python run_tests.py --verbose
```

## 📈 性能基准

### 推荐性能指标

- **响应时间**：< 10秒（复杂查询）
- **成功率**：> 80%
- **并发性能**：支持5个并发用户
- **流式输出**：首块延迟 < 2秒

### 性能优化建议

1. **查询优化**：使用合适的查询模式
2. **缓存策略**：启用结果缓存
3. **资源配置**：调整内存和CPU分配
4. **网络优化**：使用本地部署减少延迟

## 🤝 贡献指南

### 添加新测试

1. 在相应的测试文件中添加测试函数
2. 更新测试配置（如需要）
3. 运行测试确保通过
4. 更新文档说明

### 测试最佳实践

- 使用描述性的测试名称
- 添加适当的错误处理
- 包含必要的断言检查
- 提供清晰的失败信息

## 📞 支持

如果遇到问题或需要帮助：

1. 检查测试日志和错误信息
2. 查看API服务日志
3. 确认系统配置正确
4. 参考故障排除指南

---

**注意**：测试需要GuiXiaoxi RAG API服务正常运行。请确保在运行测试前启动相关服务。
