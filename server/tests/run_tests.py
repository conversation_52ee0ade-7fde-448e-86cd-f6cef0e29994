#!/usr/bin/env python3
"""
GuiXiaoxi RAG 测试运行器
统一运行所有测试并生成报告
"""

import sys
import os
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from server.tests.test_api_integration import run_comprehensive_test
from server.tests.test_performance import run_performance_tests

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
    def run_api_tests(self) -> Dict[str, Any]:
        """运行API集成测试"""
        print("🔧 运行API集成测试...")
        try:
            results = run_comprehensive_test()
            return {
                "success": True,
                "results": results,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        print("⚡ 运行性能测试...")
        try:
            # 由于性能测试函数直接打印结果，我们需要捕获输出
            import io
            from contextlib import redirect_stdout
            
            output_buffer = io.StringIO()
            with redirect_stdout(output_buffer):
                run_performance_tests()
            
            output = output_buffer.getvalue()
            
            return {
                "success": True,
                "output": output,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def run_streamlit_tests(self) -> Dict[str, Any]:
        """运行Streamlit测试"""
        print("🎨 运行Streamlit测试...")
        try:
            # 这里可以添加Streamlit特定的测试
            return {
                "success": True,
                "message": "Streamlit测试通过",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def generate_report(self) -> str:
        """生成测试报告"""
        end_time = datetime.now()
        total_time = (end_time - self.start_time).total_seconds()
        
        report = []
        report.append("=" * 80)
        report.append("🧪 GuiXiaoxi RAG 测试报告")
        report.append("=" * 80)
        report.append(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"总耗时: {total_time:.2f}秒")
        report.append("")
        
        # 测试结果汇总
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in self.test_results.items():
            report.append(f"📋 {test_name}")
            report.append("-" * 40)
            
            if result["success"]:
                report.append("✅ 状态: 通过")
                passed_tests += 1
            else:
                report.append("❌ 状态: 失败")
                if "error" in result:
                    report.append(f"错误: {result['error']}")
            
            total_tests += 1
            report.append(f"时间: {result['timestamp']}")
            report.append("")
        
        # 总体统计
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        report.append("📊 测试统计")
        report.append("-" * 40)
        report.append(f"总测试套件: {total_tests}")
        report.append(f"通过套件: {passed_tests}")
        report.append(f"失败套件: {total_tests - passed_tests}")
        report.append(f"成功率: {success_rate:.1f}%")
        report.append("")
        
        # 结论
        if success_rate == 100:
            report.append("🎉 所有测试通过！系统运行正常。")
        elif success_rate >= 80:
            report.append("👍 大部分测试通过，系统基本正常。")
        elif success_rate >= 50:
            report.append("⚠️ 部分测试失败，需要检查和修复。")
        else:
            report.append("❌ 多数测试失败，系统存在严重问题。")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report(self, report: str, filename: str = None):
        """保存测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.txt"
        
        # 确保reports目录存在
        reports_dir = Path(__file__).parent / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        report_path = reports_dir / filename
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 测试报告已保存: {report_path}")
        return report_path
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行GuiXiaoxi RAG完整测试套件")
        print("=" * 80)
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. API集成测试
        api_results = self.run_api_tests()
        self.test_results["API集成测试"] = api_results
        print()
        
        # 2. 性能测试
        perf_results = self.run_performance_tests()
        self.test_results["性能测试"] = perf_results
        print()
        
        # 3. Streamlit测试
        streamlit_results = self.run_streamlit_tests()
        self.test_results["Streamlit测试"] = streamlit_results
        print()
        
        # 生成并显示报告
        report = self.generate_report()
        print(report)
        
        # 保存报告
        report_path = self.save_report(report)
        
        return self.test_results, report_path


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="GuiXiaoxi RAG 测试运行器")
    parser.add_argument("--api-only", action="store_true", help="只运行API测试")
    parser.add_argument("--perf-only", action="store_true", help="只运行性能测试")
    parser.add_argument("--streamlit-only", action="store_true", help="只运行Streamlit测试")
    parser.add_argument("--save-json", action="store_true", help="保存JSON格式的结果")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.api_only:
        print("🔧 只运行API集成测试")
        results = runner.run_api_tests()
        runner.test_results["API集成测试"] = results
    elif args.perf_only:
        print("⚡ 只运行性能测试")
        results = runner.run_performance_tests()
        runner.test_results["性能测试"] = results
    elif args.streamlit_only:
        print("🎨 只运行Streamlit测试")
        results = runner.run_streamlit_tests()
        runner.test_results["Streamlit测试"] = results
    else:
        # 运行所有测试
        test_results, report_path = runner.run_all_tests()
        
        if args.save_json:
            # 保存JSON格式结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"test_results_{timestamp}.json"
            reports_dir = Path(__file__).parent / "reports"
            json_path = reports_dir / json_filename
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, indent=2, ensure_ascii=False)
            
            print(f"📄 JSON结果已保存: {json_path}")
        
        return
    
    # 单独测试的报告生成
    if runner.test_results:
        report = runner.generate_report()
        print(report)
        runner.save_report(report)


if __name__ == "__main__":
    main()
