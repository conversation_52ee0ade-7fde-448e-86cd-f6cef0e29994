#!/usr/bin/env python3
"""
GuiXiaoxi RAG API 测试脚本
"""

import requests
import json
import time
from typing import Dict, Any


class GuiXiaoxiAPIClient:
    """GuiXiaoxi API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        return response.json()
    
    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        response = self.session.get(f"{self.base_url}/config")
        return response.json()
    
    def insert_text(self, texts: list) -> Dict[str, Any]:
        """插入文本"""
        response = self.session.post(
            f"{self.base_url}/insert/text",
            json={"texts": texts}
        )
        return response.json()
    
    def insert_from_directory(self, directory_name: str) -> Dict[str, Any]:
        """从目录插入文档"""
        response = self.session.post(
            f"{self.base_url}/insert/directory",
            data={"directory_name": directory_name}
        )
        return response.json()
    
    def query(self, query: str, mode: str = "mix") -> Dict[str, Any]:
        """单个查询"""
        response = self.session.post(
            f"{self.base_url}/query",
            json={"query": query, "mode": mode}
        )
        return response.json()
    
    def batch_query(self, queries: list, mode: str = "mix") -> Dict[str, Any]:
        """批量查询"""
        response = self.session.post(
            f"{self.base_url}/query/batch",
            json={"queries": queries, "mode": mode}
        )
        return response.json()
    
    def get_query_templates(self) -> Dict[str, Any]:
        """获取查询模板"""
        response = self.session.get(f"{self.base_url}/query/templates")
        return response.json()
    
    def export_graph(self) -> Dict[str, Any]:
        """导出图数据"""
        response = self.session.get(f"{self.base_url}/graph/export")
        return response.json()
    
    def get_graph_stats(self) -> Dict[str, Any]:
        """获取图统计"""
        response = self.session.get(f"{self.base_url}/graph/stats")
        return response.json()
    
    def clear_knowledge_base(self) -> Dict[str, Any]:
        """清空知识库"""
        response = self.session.delete(f"{self.base_url}/insert/clear")
        return response.json()


def test_api():
    """测试 API 功能"""
    client = GuiXiaoxiAPIClient()

    print("🚀 开始测试 GuiXiaoxi RAG API")
    print("=" * 50)

    # 1. 健康检查
    print("1. 健康检查...")
    try:
        health = client.health_check()
        print(f"   ✅ 服务状态: {health.get('message', 'Unknown')}")
        print(f"   📊 服务详情: {health.get('data', {})}")
    except Exception as e:
        print(f"   ❌ 健康检查失败: {e}")
        print("   请确保服务已启动在 http://localhost:8000")
        return
    
    # 2. 获取配置
    print("\n2. 获取配置...")
    try:
        config = client.get_config()
        if config.get('success'):
            print("   ✅ 配置获取成功")
        else:
            print(f"   ❌ 配置获取失败: {config.get('message')}")
    except Exception as e:
        print(f"   ❌ 配置获取异常: {e}")
    
    # 3. 获取查询模板
    print("\n3. 获取查询模板...")
    try:
        templates = client.get_query_templates()
        if templates.get('success'):
            print("   ✅ 查询模板获取成功")
            for key, template in templates.get('data', {}).items():
                print(f"      {key}: {template}")
        else:
            print(f"   ❌ 查询模板获取失败: {templates.get('message')}")
    except Exception as e:
        print(f"   ❌ 查询模板获取异常: {e}")
    
    # 4. 插入测试文本
    print("\n4. 插入测试文本...")
    test_texts = [
        "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
        "机器学习是人工智能的一个子领域，专注于算法的设计，这些算法可以从数据中学习并做出预测或决策。",
        "深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。"
    ]
    
    try:
        result = client.insert_text(test_texts)
        if result.get('success'):
            print(f"   ✅ 文本插入成功: {result.get('message')}")
        else:
            print(f"   ❌ 文本插入失败: {result.get('message')}")
    except Exception as e:
        print(f"   ❌ 文本插入异常: {e}")
    
    # 等待处理完成
    print("\n   ⏳ 等待文档处理完成...")
    time.sleep(5)
    
    # 5. 测试查询
    print("\n5. 测试查询...")
    test_queries = [
        "什么是人工智能？",
        "机器学习和深度学习的关系是什么？",
        "人工智能有哪些应用领域？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n   查询 {i}: {query}")
        try:
            result = client.query(query, mode="mix")
            if result.get('success'):
                answer = result.get('result', '无结果')
                print(f"   ✅ 查询成功")
                print(f"   📝 回答: {answer[:200]}{'...' if len(answer) > 200 else ''}")
            else:
                print(f"   ❌ 查询失败: {result.get('message')}")
        except Exception as e:
            print(f"   ❌ 查询异常: {e}")
    
    # 6. 批量查询测试
    print("\n6. 批量查询测试...")
    try:
        result = client.batch_query(test_queries[:2], mode="local")
        if result.get('success'):
            print(f"   ✅ 批量查询成功，处理了 {len(result.get('results', []))} 个查询")
        else:
            print(f"   ❌ 批量查询失败: {result.get('message')}")
    except Exception as e:
        print(f"   ❌ 批量查询异常: {e}")
    
    # 7. 获取图统计
    print("\n7. 获取图统计...")
    try:
        stats = client.get_graph_stats()
        if stats.get('success'):
            data = stats.get('data', {})
            print(f"   ✅ 图统计获取成功")
            print(f"      节点数: {data.get('nodes_count', 'N/A')}")
            print(f"      边数: {data.get('edges_count', 'N/A')}")
            print(f"      XML文件存在: {data.get('xml_file_exists', False)}")
            print(f"      JSON文件存在: {data.get('json_file_exists', False)}")
        else:
            print(f"   ❌ 图统计获取失败: {stats.get('message')}")
    except Exception as e:
        print(f"   ❌ 图统计获取异常: {e}")
    
    # 8. 导出图数据（可选）
    print("\n8. 导出图数据...")
    try:
        result = client.export_graph()
        if result.get('success'):
            graph_data = result.get('graph_data', {})
            nodes_count = len(graph_data.get('nodes', []))
            edges_count = len(graph_data.get('edges', []))
            print(f"   ✅ 图数据导出成功: {nodes_count} 个节点, {edges_count} 条边")
        else:
            print(f"   ❌ 图数据导出失败: {result.get('message')}")
    except Exception as e:
        print(f"   ❌ 图数据导出异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API 测试完成！")


if __name__ == "__main__":
    test_api()
