#!/usr/bin/env python3
"""
GuiXiaoxi RAG 测试配置
定义测试参数和设置
"""

import os
from pathlib import Path

# 测试配置
TEST_CONFIG = {
    # API服务配置
    "api": {
        "base_url": os.getenv("TEST_API_URL", "http://localhost:8000"),
        "timeout": 30,
        "max_retries": 3
    },
    
    # 测试数据配置
    "test_data": {
        "queries": [
            "计算机学院院长是谁？",
            "计算机学院有哪些专业？",
            "人工智能专业的课程有哪些？",
            "软件工程专业的就业方向？",
            "计算机学院的研究方向？",
            "数据科学专业的培养目标？",
            "网络安全专业的核心课程？",
            "计算机学院的师资力量如何？",
            "计算机学院的历史发展？",
            "如何申请计算机专业？"
        ],
        "knowledge_bases": ["cs_college", "default"],
        "query_modes": ["local", "global", "hybrid", "naive", "mix", "bypass"]
    },
    
    # 性能测试配置
    "performance": {
        "response_time_iterations": 10,
        "concurrent_users": 5,
        "queries_per_user": 3,
        "mode_test_iterations": 5,
        "acceptable_response_time": 10.0,  # 秒
        "acceptable_success_rate": 80.0    # 百分比
    },
    
    # 文件上传测试配置
    "file_upload": {
        "test_files": [
            {
                "name": "test_document.txt",
                "content": "这是一个测试文档。\n包含一些测试内容用于验证文件上传功能。\n测试内容包括中文和英文。",
                "type": "text/plain"
            },
            {
                "name": "test_markdown.md", 
                "content": "# 测试Markdown文档\n\n这是一个**测试**文档。\n\n- 列表项1\n- 列表项2\n\n## 子标题\n\n测试内容。",
                "type": "text/markdown"
            }
        ],
        "target_kb": "cs_college"
    },
    
    # 报告配置
    "reporting": {
        "save_detailed_results": True,
        "save_json_results": True,
        "reports_dir": "reports",
        "include_response_content": False  # 是否在报告中包含完整响应内容
    }
}

# 测试环境检查
def check_test_environment():
    """检查测试环境"""
    checks = {
        "api_service": False,
        "knowledge_bases": False,
        "test_data": False
    }
    
    try:
        import requests
        response = requests.get(f"{TEST_CONFIG['api']['base_url']}/health", timeout=5)
        checks["api_service"] = response.status_code == 200
    except:
        pass
    
    try:
        response = requests.get(f"{TEST_CONFIG['api']['base_url']}/knowledge-bases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            checks["knowledge_bases"] = len(data.get("knowledge_bases", {})) > 0
    except:
        pass
    
    # 检查测试数据
    checks["test_data"] = len(TEST_CONFIG["test_data"]["queries"]) > 0
    
    return checks

# 测试结果评估标准
EVALUATION_CRITERIA = {
    "api_integration": {
        "min_success_rate": 80.0,
        "critical_endpoints": ["/health", "/knowledge-bases", "/query/advanced"]
    },
    "performance": {
        "max_avg_response_time": 10.0,
        "min_success_rate": 80.0,
        "max_concurrent_response_time": 15.0
    },
    "query_modes": {
        "min_working_modes": 4,  # 至少4个模式应该工作
        "critical_modes": ["hybrid", "bypass"]  # 关键模式必须工作
    }
}

# 测试数据生成器
class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_test_queries(count: int = 10):
        """生成测试查询"""
        base_queries = TEST_CONFIG["test_data"]["queries"]
        if count <= len(base_queries):
            return base_queries[:count]
        
        # 如果需要更多查询，重复使用基础查询
        queries = []
        for i in range(count):
            queries.append(base_queries[i % len(base_queries)])
        return queries
    
    @staticmethod
    def generate_test_file(filename: str, content: str):
        """生成测试文件"""
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(
            mode='w', 
            suffix=Path(filename).suffix,
            delete=False
        )
        temp_file.write(content)
        temp_file.close()
        return temp_file.name

# 测试工具函数
def get_test_config():
    """获取测试配置"""
    return TEST_CONFIG

def get_evaluation_criteria():
    """获取评估标准"""
    return EVALUATION_CRITERIA

def setup_test_environment():
    """设置测试环境"""
    # 创建报告目录
    reports_dir = Path(__file__).parent / TEST_CONFIG["reporting"]["reports_dir"]
    reports_dir.mkdir(exist_ok=True)
    
    # 检查环境
    env_checks = check_test_environment()
    
    return env_checks

if __name__ == "__main__":
    print("🔧 GuiXiaoxi RAG 测试配置")
    print("=" * 50)
    
    # 显示配置信息
    print("📋 测试配置:")
    print(f"  API地址: {TEST_CONFIG['api']['base_url']}")
    print(f"  测试查询数: {len(TEST_CONFIG['test_data']['queries'])}")
    print(f"  查询模式数: {len(TEST_CONFIG['test_data']['query_modes'])}")
    print(f"  知识库: {TEST_CONFIG['test_data']['knowledge_bases']}")
    print()
    
    # 检查环境
    print("🔍 环境检查:")
    env_checks = check_test_environment()
    for check_name, status in env_checks.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {check_name}")
    
    print()
    
    # 显示评估标准
    print("📊 评估标准:")
    criteria = get_evaluation_criteria()
    for category, standards in criteria.items():
        print(f"  {category}:")
        for key, value in standards.items():
            print(f"    {key}: {value}")
    
    print("\n✅ 测试配置加载完成")
