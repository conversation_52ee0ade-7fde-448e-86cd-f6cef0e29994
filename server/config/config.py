"""
GuiXiaoxi RAG 配置管理模块
支持多知识库配置和流式输出配置
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class LLMConfig:
    """LLM 配置"""
    api_base: str = "http://localhost:8100/v1"
    api_key: str = ""
    model: str = "qwen14b"
    max_token_size: int = 2048
    max_tokens: int = 4096
    stream: bool = True
    temperature: float = 0.7


@dataclass
class EmbeddingConfig:
    """嵌入模型配置"""
    api_base: str = "http://localhost:8200/v1"
    api_key: str = ""
    model: str = "embedding_qwen"
    embedding_dim: int = 2560
    max_token_size: int = 2048


@dataclass
class KnowledgeBaseConfig:
    """知识库配置"""
    name: str
    description: str
    working_dir: str
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


@dataclass
class RAGConfig:
    """RAG 配置"""
    working_dir: str = "./knowledgeBase"
    default_kb: str = "default"
    language: str = "中文"
    max_gleaning: int = 1
    force_llm_summary_on_merge: bool = True
    enable_stream: bool = True
    enable_general_qa: bool = True


@dataclass
class APIConfig:
    """API 配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    max_upload_size: int = 104857600  # 100MB
    temp_dir: str = "temp_uploads"
    enable_stream: bool = True


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_dir: str = "/mnt/Jim/project/gui_xiaoxi/logs"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5

    @property
    def file_path(self) -> str:
        """获取当天的日志文件路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        return os.path.join(self.log_dir, f"{today}.log")


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.llm = LLMConfig()
        self.embedding = EmbeddingConfig()
        self.rag = RAGConfig()
        self.api = APIConfig()
        self.logging = LoggingConfig()
        self.knowledge_bases: Dict[str, KnowledgeBaseConfig] = {}
        
        # 加载配置
        self.load_config()
        
        # 确保目录存在
        self._ensure_directories()
    
    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.save_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载各个配置部分
            if 'llm' in config_data:
                self.llm = LLMConfig(**config_data['llm'])
            
            if 'embedding' in config_data:
                self.embedding = EmbeddingConfig(**config_data['embedding'])
            
            if 'rag' in config_data:
                self.rag = RAGConfig(**config_data['rag'])
            
            if 'api' in config_data:
                self.api = APIConfig(**config_data['api'])
            
            if 'logging' in config_data:
                self.logging = LoggingConfig(**config_data['logging'])
            
            # 加载知识库配置
            if 'knowledge_bases' in config_data:
                for kb_id, kb_config in config_data['knowledge_bases'].items():
                    self.knowledge_bases[kb_id] = KnowledgeBaseConfig(**kb_config)
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            # 使用默认配置
            self._load_default_knowledge_bases()
    
    def save_config(self):
        """保存配置文件"""
        config_data = {
            'llm': self.llm.__dict__,
            'embedding': self.embedding.__dict__,
            'rag': self.rag.__dict__,
            'api': self.api.__dict__,
            'logging': self.logging.__dict__,
            'knowledge_bases': {
                kb_id: kb_config.__dict__ 
                for kb_id, kb_config in self.knowledge_bases.items()
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def _load_default_knowledge_bases(self):
        """加载默认知识库配置"""
        self.knowledge_bases = {
            "default": KnowledgeBaseConfig(
                name="默认知识库",
                description="系统默认知识库",
                working_dir="./knowledgeBase/default",
                enabled=True
            ),
            "cs_college": KnowledgeBaseConfig(
                name="计算机学院",
                description="贵州大学计算机科学与技术学院知识库",
                working_dir="./knowledgeBase/cs_college",
                enabled=True
            ),
            "general": KnowledgeBaseConfig(
                name="通用知识",
                description="通用知识库",
                working_dir="./knowledgeBase/general",
                enabled=True
            ),
            "custom": KnowledgeBaseConfig(
                name="自定义知识库",
                description="用户自定义知识库",
                working_dir="./knowledgeBase/custom",
                enabled=False
            )
        }
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        # 创建日志目录
        log_dir = Path(self.logging.log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建临时上传目录
        Path(self.api.temp_dir).mkdir(parents=True, exist_ok=True)
        
        # 创建知识库目录
        Path(self.rag.working_dir).mkdir(parents=True, exist_ok=True)
        
        # 创建各个知识库目录
        for kb_config in self.knowledge_bases.values():
            Path(kb_config.working_dir).mkdir(parents=True, exist_ok=True)
    
    def get_enabled_knowledge_bases(self) -> Dict[str, KnowledgeBaseConfig]:
        """获取启用的知识库"""
        return {
            kb_id: kb_config 
            for kb_id, kb_config in self.knowledge_bases.items() 
            if kb_config.enabled
        }
    
    def add_knowledge_base(self, kb_id: str, name: str, description: str, 
                          working_dir: str = None) -> bool:
        """添加新知识库"""
        if kb_id in self.knowledge_bases:
            return False
        
        if working_dir is None:
            working_dir = f"./knowledgeBase/{kb_id}"
        
        self.knowledge_bases[kb_id] = KnowledgeBaseConfig(
            name=name,
            description=description,
            working_dir=working_dir,
            enabled=True,
            created_at=datetime.now().isoformat()
        )
        
        # 创建目录
        Path(working_dir).mkdir(parents=True, exist_ok=True)
        
        # 保存配置
        self.save_config()
        return True
    
    def remove_knowledge_base(self, kb_id: str) -> bool:
        """删除知识库"""
        if kb_id not in self.knowledge_bases or kb_id == self.rag.default_kb:
            return False
        
        del self.knowledge_bases[kb_id]
        self.save_config()
        return True
    
    def enable_knowledge_base(self, kb_id: str) -> bool:
        """启用知识库"""
        if kb_id not in self.knowledge_bases:
            return False
        
        self.knowledge_bases[kb_id].enabled = True
        self.knowledge_bases[kb_id].updated_at = datetime.now().isoformat()
        self.save_config()
        return True
    
    def disable_knowledge_base(self, kb_id: str) -> bool:
        """禁用知识库"""
        if kb_id not in self.knowledge_bases or kb_id == self.rag.default_kb:
            return False
        
        self.knowledge_bases[kb_id].enabled = False
        self.knowledge_bases[kb_id].updated_at = datetime.now().isoformat()
        self.save_config()
        return True
    
    def setup_logging(self):
        """设置日志"""
        # 配置根日志记录器
        logging.basicConfig(
            level=getattr(logging, self.logging.level.upper()),
            format=self.logging.format,
            handlers=[]
        )
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(self.logging.format))
        logging.getLogger().addHandler(console_handler)
        
        # 添加文件处理器
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            self.logging.file_path,
            maxBytes=self.logging.max_file_size,
            backupCount=self.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter(self.logging.format))
        logging.getLogger().addHandler(file_handler)


# 全局配置管理器实例
config_manager = ConfigManager()
