#!/usr/bin/env python3
"""
GuiXiaoxi RAG 使用演示脚本
展示如何通过 API 和 Web 界面使用系统
"""

import requests
import json
import time
from datetime import datetime


def demo_api_usage():
    """演示 API 使用"""
    print("🚀 GuiXiaoxi RAG API 使用演示")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 健康检查
    print("1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 服务状态: {data['message']}")
            print(f"   📊 详细信息: {data['data']}")
        else:
            print(f"   ❌ 健康检查失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 获取系统统计
    print("\n2. 获取知识图谱统计...")
    try:
        response = requests.get(f"{base_url}/graph/stats")
        if response.status_code == 200:
            stats = response.json()
            data = stats['data']
            print(f"   📊 知识节点: {data.get('nodes_count', 0)}")
            print(f"   📊 关系边: {data.get('edges_count', 0)}")
            print(f"   📁 数据文件: {'✅' if data.get('json_file_exists') else '❌'}")
        else:
            print(f"   ❌ 获取统计失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3. 单个查询演示
    print("\n3. 单个查询演示...")
    test_query = "计算机科学与技术学院的院长是谁？"
    
    print(f"\n   查询: {test_query}")
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/query",
            json={"query": test_query, "mode": "mix"},
            timeout=30
        )
        duration = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            answer = result['result']
            print(f"   ✅ 查询成功 ({duration:.2f}s)")
            print(f"   📝 回答: {answer[:200]}...")
        else:
            print(f"   ❌ 查询失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 查询异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API 演示完成！")


def show_web_guide():
    """显示 Web 界面使用指南"""
    print("\n🎨 GuiXiaoxi RAG Web 界面使用指南")
    print("=" * 50)
    
    print("📱 Web 界面功能:")
    print("   🏠 首页概览 - 系统状态和快速查询")
    print("   💬 智能问答 - ChatGPT 风格的对话界面")
    print("   📚 批量查询 - 同时处理多个问题")
    print("   📄 文档管理 - 上传和管理知识库文档")
    print("   📊 知识图谱 - 可视化数据分析")
    print("   ⚙️ 系统管理 - 服务监控和维护")
    
    print("\n🌐 访问地址:")
    print("   Web 界面: http://localhost:8501")
    print("   API 文档: http://localhost:8000/docs")
    
    print("\n💡 使用建议:")
    print("   1. 首次使用建议从首页开始，了解系统状态")
    print("   2. 智能问答页面适合日常查询使用")
    print("   3. 批量查询适合处理大量问题")
    print("   4. 文档管理用于维护知识库内容")
    print("   5. 知识图谱页面可以分析数据结构")
    print("   6. 系统管理页面用于监控和维护")


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例")
    print("=" * 50)
    
    examples = {
        "学院信息查询": [
            "计算机科学与技术学院的院长是谁？",
            "学院有哪些专业？",
            "学院的联系方式是什么？"
        ],
        "专业相关查询": [
            "人工智能专业的培养目标是什么？",
            "软件工程专业的主要课程有哪些？",
            "数据科学专业的就业方向如何？"
        ],
        "学术政策查询": [
            "硕博连读的申请条件是什么？",
            "研究生毕业要求有哪些？",
            "学术论文发表有什么要求？"
        ]
    }
    
    for category, queries in examples.items():
        print(f"\n📋 {category}:")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. {query}")
    
    print("\n💡 查询技巧:")
    print("   - 问题要具体明确，避免过于宽泛")
    print("   - 可以使用关键词组合查询")
    print("   - 不同查询模式适用于不同类型的问题")
    print("   - 可以进行多轮对话深入了解")


def main():
    """主函数"""
    print("🤖 GuiXiaoxi RAG 系统使用演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # API 使用演示
    demo_api_usage()
    
    # Web 界面介绍
    show_web_guide()
    
    # 使用示例
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n🚀 开始使用:")
    print("   1. 访问 Web 界面: http://localhost:8501")
    print("   2. 查看 API 文档: http://localhost:8000/docs")
    print("   3. 阅读使用指南: STREAMLIT_GUIDE.md")
    print("\n📞 如有问题，请查看文档或联系技术支持。")


if __name__ == "__main__":
    main()
