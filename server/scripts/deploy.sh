#!/bin/bash

# GuiXiaoxi RAG 部署脚本
# 用于自动化部署 GuiXiaoxi RAG 服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_DIR="/mnt/Jim/project/gui_xiaoxi"
CONDA_ENV="lightrag312"
SERVICE_NAME="guixiaoxi-rag"
SERVICE_PORT="8000"
SERVICE_HOST="0.0.0.0"

# 检查 conda 环境
check_conda_env() {
    log_info "检查 conda 环境..."
    
    if ! command -v conda &> /dev/null; then
        log_error "Conda 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! conda env list | grep -q "$CONDA_ENV"; then
        log_error "Conda 环境 '$CONDA_ENV' 不存在"
        log_info "请先创建环境: conda create -n $CONDA_ENV python=3.12"
        exit 1
    fi
    
    log_success "Conda 环境检查通过"
}

# 检查依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    cd "$PROJECT_DIR"
    
    # 激活 conda 环境并检查依赖
    source /root/miniconda3/etc/profile.d/conda.sh
    conda activate "$CONDA_ENV"
    
    # 检查关键依赖
    python -c "import fastapi, uvicorn, requests" 2>/dev/null || {
        log_warning "缺少关键依赖，正在安装..."
        pip install -r requirements.txt
    }
    
    log_success "依赖检查完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "$PROJECT_DIR/config.json" ]; then
        log_error "配置文件 config.json 不存在"
        exit 1
    fi
    
    log_success "配置文件检查通过"
}

# 检查端口占用
check_port() {
    log_info "检查端口 $SERVICE_PORT..."
    
    if netstat -tuln 2>/dev/null | grep -q ":$SERVICE_PORT " || ss -tuln 2>/dev/null | grep -q ":$SERVICE_PORT "; then
        log_warning "端口 $SERVICE_PORT 已被占用"
        
        # 尝试停止现有服务
        if pgrep -f "start_server.py" > /dev/null; then
            log_info "停止现有服务..."
            pkill -f "start_server.py"
            sleep 2
        fi
    fi
    
    log_success "端口检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    cd "$PROJECT_DIR"
    
    mkdir -p output
    mkdir -p temp_uploads
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 运行测试
run_tests() {
    log_info "运行服务测试..."
    
    cd "$PROJECT_DIR"
    
    # 激活环境
    source /root/miniconda3/etc/profile.d/conda.sh
    conda activate "$CONDA_ENV"
    
    # 启动服务（后台）
    python start_server.py --host "$SERVICE_HOST" --port "$SERVICE_PORT" &
    SERVER_PID=$!
    
    # 等待服务启动
    sleep 10
    
    # 运行健康检查
    if curl -f "http://localhost:$SERVICE_PORT/health" > /dev/null 2>&1; then
        log_success "服务健康检查通过"
    else
        log_error "服务健康检查失败"
        kill $SERVER_PID 2>/dev/null || true
        exit 1
    fi
    
    # 停止测试服务
    kill $SERVER_PID 2>/dev/null || true
    sleep 2
    
    log_success "测试完成"
}

# 启动开发服务
start_dev_service() {
    log_info "启动开发服务..."
    
    cd "$PROJECT_DIR"
    
    # 激活环境
    source /root/miniconda3/etc/profile.d/conda.sh
    conda activate "$CONDA_ENV"
    
    log_success "正在启动 GuiXiaoxi RAG 服务..."
    log_info "服务地址: http://localhost:$SERVICE_PORT"
    log_info "API 文档: http://localhost:$SERVICE_PORT/docs"
    log_info "按 Ctrl+C 停止服务"
    
    # 启动服务
    python start_server.py --host "$SERVICE_HOST" --port "$SERVICE_PORT"
}

# 显示服务信息
show_service_info() {
    log_info "服务信息:"
    echo "  - 服务地址: http://localhost:$SERVICE_PORT"
    echo "  - API 文档: http://localhost:$SERVICE_PORT/docs"
    echo "  - 健康检查: http://localhost:$SERVICE_PORT/health"
    echo ""
    log_info "测试命令:"
    echo "  - 运行测试: cd $PROJECT_DIR && python comprehensive_test.py"
    echo "  - 健康检查: curl http://localhost:$SERVICE_PORT/health"
}

# 主函数
main() {
    log_info "开始部署 GuiXiaoxi RAG 服务..."
    
    # 执行部署步骤
    check_conda_env
    check_dependencies
    check_config
    check_port
    create_directories
    run_tests
    show_service_info
    
    log_success "GuiXiaoxi RAG 服务部署完成！"
}

# 处理命令行参数
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "start")
        start_dev_service
        ;;
    "test")
        log_info "运行服务测试..."
        cd "$PROJECT_DIR"
        source /root/miniconda3/etc/profile.d/conda.sh
        conda activate "$CONDA_ENV"
        python comprehensive_test.py
        ;;
    "help")
        echo "用法: $0 [deploy|start|test|help]"
        echo ""
        echo "命令说明:"
        echo "  deploy  - 检查环境并部署服务（默认）"
        echo "  start   - 启动开发服务"
        echo "  test    - 运行测试"
        echo "  help    - 显示帮助信息"
        ;;
    *)
        log_error "未知命令: $1"
        echo "使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac
