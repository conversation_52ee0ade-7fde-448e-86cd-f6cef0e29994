"""
GuiXiaoxi RAG API 异常处理
定义自定义异常和异常处理器
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.exception_handlers import http_exception_handler
import logging

logger = logging.getLogger("guixiaoxi_api")


class GuiXiaoxiException(Exception):
    """GuiXiaoxi 基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class KnowledgeBaseException(GuiXiaoxiException):
    """知识库相关异常"""
    
    def __init__(self, message: str, kb_id: str = None, **kwargs):
        super().__init__(message, error_code="KNOWLEDGE_BASE_ERROR", **kwargs)
        if kb_id:
            self.details["kb_id"] = kb_id


class DocumentProcessingException(GuiXiaoxiException):
    """文档处理异常"""
    
    def __init__(self, message: str, file_path: str = None, **kwargs):
        super().__init__(message, error_code="DOCUMENT_PROCESSING_ERROR", **kwargs)
        if file_path:
            self.details["file_path"] = file_path


class QueryException(GuiXiaoxiException):
    """查询异常"""
    
    def __init__(self, message: str, query: str = None, **kwargs):
        super().__init__(message, error_code="QUERY_ERROR", **kwargs)
        if query:
            self.details["query"] = query


class ConfigurationException(GuiXiaoxiException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)
        if config_key:
            self.details["config_key"] = config_key


class ValidationException(GuiXiaoxiException):
    """验证异常"""
    
    def __init__(self, message: str, field: str = None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        if field:
            self.details["field"] = field


def create_error_response(
    success: bool = False,
    message: str = "操作失败",
    error_code: str = "UNKNOWN_ERROR",
    details: Optional[Dict[str, Any]] = None,
    status_code: int = 500
) -> JSONResponse:
    """创建标准错误响应"""
    
    error_data = {
        "error_code": error_code,
        "details": details or {}
    }
    
    response_data = {
        "success": success,
        "message": message,
        "data": error_data
    }
    
    return JSONResponse(
        status_code=status_code,
        content=response_data
    )


async def guixiaoxi_exception_handler(request: Request, exc: GuiXiaoxiException) -> JSONResponse:
    """GuiXiaoxi 自定义异常处理器"""
    
    logger.error(f"GuiXiaoxi异常: {exc.error_code} - {exc.message}", exc_info=True)
    
    # 根据异常类型确定HTTP状态码
    status_code_map = {
        "KNOWLEDGE_BASE_ERROR": 404,
        "DOCUMENT_PROCESSING_ERROR": 400,
        "QUERY_ERROR": 400,
        "CONFIGURATION_ERROR": 500,
        "VALIDATION_ERROR": 422,
        "UNKNOWN_ERROR": 500
    }
    
    status_code = status_code_map.get(exc.error_code, 500)
    
    return create_error_response(
        success=False,
        message=exc.message,
        error_code=exc.error_code,
        details=exc.details,
        status_code=status_code
    )


async def http_exception_handler_custom(request: Request, exc: HTTPException) -> JSONResponse:
    """自定义HTTP异常处理器"""
    
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    # 将HTTPException转换为标准格式
    error_code_map = {
        400: "BAD_REQUEST",
        401: "UNAUTHORIZED",
        403: "FORBIDDEN",
        404: "NOT_FOUND",
        405: "METHOD_NOT_ALLOWED",
        422: "VALIDATION_ERROR",
        429: "RATE_LIMIT_EXCEEDED",
        500: "INTERNAL_SERVER_ERROR"
    }
    
    error_code = error_code_map.get(exc.status_code, "HTTP_ERROR")
    
    return create_error_response(
        success=False,
        message=str(exc.detail),
        error_code=error_code,
        details={"status_code": exc.status_code},
        status_code=exc.status_code
    )


async def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """验证异常处理器"""
    
    logger.error(f"验证异常: {str(exc)}", exc_info=True)
    
    # 处理Pydantic验证错误
    if hasattr(exc, 'errors'):
        validation_errors = []
        for error in exc.errors():
            validation_errors.append({
                "field": ".".join(str(loc) for loc in error.get("loc", [])),
                "message": error.get("msg", ""),
                "type": error.get("type", "")
            })
        
        return create_error_response(
            success=False,
            message="请求参数验证失败",
            error_code="VALIDATION_ERROR",
            details={"validation_errors": validation_errors},
            status_code=422
        )
    
    return create_error_response(
        success=False,
        message="请求参数验证失败",
        error_code="VALIDATION_ERROR",
        details={"error": str(exc)},
        status_code=422
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}", exc_info=True)
    
    return create_error_response(
        success=False,
        message="服务器内部错误，请稍后重试",
        error_code="INTERNAL_SERVER_ERROR",
        details={
            "exception_type": type(exc).__name__,
            "exception_message": str(exc)
        },
        status_code=500
    )


def setup_exception_handlers(app):
    """设置异常处理器"""
    
    # 自定义异常处理器
    app.add_exception_handler(GuiXiaoxiException, guixiaoxi_exception_handler)
    
    # HTTP异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler_custom)
    
    # 验证异常处理器
    from fastapi.exceptions import RequestValidationError
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 通用异常处理器
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("异常处理器设置完成")
