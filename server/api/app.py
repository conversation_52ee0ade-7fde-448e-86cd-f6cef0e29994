"""
GuiXiaoxi RAG 增强 FastAPI 应用
支持多知识库、流式输出和通用问答
"""

import os
import asyncio
import logging
import json
from typing import List, Optional, Dict, Any, Union
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field

from server.core.insert import insert_manager
from server.core.query import query_manager, QueryMode
from server.config.config import config_manager
from server.api.middleware import setup_middleware
from server.api.exceptions import setup_exception_handlers
from guixiaoxi.kg.shared_storage import initialize_share_data, initialize_pipeline_status

# 设置日志
config_manager.setup_logging()
logger = logging.getLogger("guixiaoxi_api")

# 全局变量
app_initialized = False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global app_initialized
    
    # 启动时初始化
    logger.info("🚀 GuiXiaoxi RAG API 启动中...")
    
    try:
        # 初始化组件
        logger.info("📚 初始化知识库管理器...")

        # 初始化共享数据
        logger.info("🔧 初始化共享数据...")
        initialize_share_data()

        # 初始化管道状态
        logger.info("🔧 初始化管道状态...")
        await initialize_pipeline_status()

        # 重新初始化知识库管理器（现在Shared-Data已经初始化）
        logger.info("🔄 重新初始化知识库...")
        query_manager.kb_manager._initialize_knowledge_bases()
        insert_manager.kb_manager._initialize_knowledge_bases()

        app_initialized = True
        logger.info("✅ GuiXiaoxi RAG API 启动完成")

        yield
        
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("🔄 GuiXiaoxi RAG API 关闭中...")
        app_initialized = False

        # 清理异步任务（简化版本，避免递归问题）
        try:
            # 获取当前事件循环中的所有任务
            current_task = asyncio.current_task()
            tasks = [task for task in asyncio.all_tasks() if not task.done() and task != current_task]
            if tasks:
                logger.info(f"正在清理 {len(tasks)} 个异步任务...")
                # 取消所有未完成的任务（除了当前任务）
                for task in tasks:
                    if not task.cancelled():
                        task.cancel()

                # 简单等待，避免复杂的gather操作
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.warning(f"清理异步任务失败: {e}")

        logger.info("👋 GuiXiaoxi RAG API 已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="GuiXiaoxi RAG API",
    description="贵州大学计算机科学与技术学院智能问答系统 API",
    version="2.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=config_manager.api.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置其他中间件
setup_middleware(app, config_manager)

# 设置异常处理器
setup_exception_handlers(app)


# 标准化响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


# Pydantic 模型
class QueryRequest(BaseModel):
    """查询请求模型"""
    query: str = Field(..., description="查询内容")
    kb_ids: Optional[List[str]] = Field(None, description="知识库ID列表，为空则查询所有启用的知识库")
    mode: QueryMode = Field(QueryMode.HYBRID, description="查询模式")
    stream: Optional[bool] = Field(None, description="是否启用流式输出")


class BatchQueryRequest(BaseModel):
    """批量查询请求模型"""
    queries: List[str] = Field(..., description="查询问题列表")
    mode: QueryMode = Field(QueryMode.HYBRID, description="查询模式")


class GeneralQARequest(BaseModel):
    """通用问答请求模型"""
    query: str = Field(..., description="问题内容")
    stream: Optional[bool] = Field(None, description="是否启用流式输出")


class InsertTextRequest(BaseModel):
    """插入文本请求模型"""
    text: str = Field(..., description="要插入的文本内容")
    kb_id: str = Field(..., description="目标知识库ID")
    description: Optional[str] = Field("", description="文档描述")


class KnowledgeBaseRequest(BaseModel):
    """知识库操作请求模型"""
    name: str = Field(..., description="知识库名称")
    description: str = Field(..., description="知识库描述")
    working_dir: Optional[str] = Field(None, description="工作目录")


# API 端点
@app.get("/", response_model=ApiResponse)
async def root():
    """根端点"""
    return ApiResponse(
        success=True,
        message="GuiXiaoxi RAG API",
        data={
            "version": "2.0.0",
            "status": "running" if app_initialized else "initializing"
        }
    )


@app.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    return ApiResponse(
        success=True,
        message="服务运行正常" if app_initialized else "服务初始化中",
        data={
            "status": "healthy" if app_initialized else "initializing",
            "timestamp": asyncio.get_event_loop().time(),
            "insert_manager": insert_manager is not None,
            "query_manager": query_manager is not None,
            "config": {
                "llm_model": config_manager.llm.model,
                "embedding_model": config_manager.embedding.model,
                "stream_enabled": config_manager.rag.enable_stream,
                "general_qa_enabled": config_manager.rag.enable_general_qa
            }
        }
    )


@app.get("/config")
async def get_config():
    """获取配置信息"""
    return {
        "llm": {
            "model": config_manager.llm.model,
            "stream": config_manager.llm.stream,
            "temperature": config_manager.llm.temperature
        },
        "embedding": {
            "model": config_manager.embedding.model,
            "embedding_dim": config_manager.embedding.embedding_dim
        },
        "rag": {
            "default_kb": config_manager.rag.default_kb,
            "language": config_manager.rag.language,
            "enable_stream": config_manager.rag.enable_stream,
            "enable_general_qa": config_manager.rag.enable_general_qa
        },
        "knowledge_bases": {
            kb_id: {
                "name": kb_config.name,
                "description": kb_config.description,
                "enabled": kb_config.enabled
            }
            for kb_id, kb_config in config_manager.knowledge_bases.items()
        }
    }


@app.get("/knowledge-bases")
async def list_knowledge_bases():
    """列出所有知识库"""
    try:
        kb_list = insert_manager.list_knowledge_bases()
        return {"knowledge_bases": kb_list}
    except Exception as e:
        logger.error(f"列出知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/knowledge-bases/{kb_id}")
async def get_knowledge_base(kb_id: str):
    """获取知识库信息"""
    try:
        info = insert_manager.get_knowledge_base_info(kb_id)
        if "error" in info:
            raise HTTPException(status_code=404, detail=info["error"])
        return info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/knowledge-bases/{kb_id}")
async def create_knowledge_base(kb_id: str, request: KnowledgeBaseRequest):
    """创建新知识库"""
    try:
        success = config_manager.add_knowledge_base(
            kb_id=kb_id,
            name=request.name,
            description=request.description,
            working_dir=request.working_dir
        )
        
        if not success:
            raise HTTPException(status_code=400, detail=f"知识库 {kb_id} 已存在")
        
        # 重新加载知识库
        insert_manager.reload_knowledge_base(kb_id)
        query_manager.kb_manager.reload_knowledge_base(kb_id)
        
        return {"message": f"知识库 {kb_id} 创建成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/knowledge-bases/{kb_id}")
async def delete_knowledge_base(kb_id: str):
    """删除知识库"""
    try:
        success = config_manager.remove_knowledge_base(kb_id)
        if not success:
            raise HTTPException(status_code=400, detail=f"无法删除知识库 {kb_id}")
        
        return {"message": f"知识库 {kb_id} 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/knowledge-bases/{kb_id}/enable")
async def enable_knowledge_base(kb_id: str):
    """启用知识库"""
    try:
        success = config_manager.enable_knowledge_base(kb_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"知识库 {kb_id} 不存在")
        
        # 重新加载知识库
        insert_manager.reload_knowledge_base(kb_id)
        query_manager.kb_manager.reload_knowledge_base(kb_id)
        
        return {"message": f"知识库 {kb_id} 已启用"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启用知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/knowledge-bases/{kb_id}/disable")
async def disable_knowledge_base(kb_id: str):
    """禁用知识库"""
    try:
        success = config_manager.disable_knowledge_base(kb_id)
        if not success:
            raise HTTPException(status_code=400, detail=f"无法禁用知识库 {kb_id}")
        
        # 重新加载知识库
        insert_manager.reload_knowledge_base(kb_id)
        query_manager.kb_manager.reload_knowledge_base(kb_id)
        
        return {"message": f"知识库 {kb_id} 已禁用"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/insert/text")
async def insert_text(request: InsertTextRequest):
    """插入文本到知识库"""
    try:
        results = []
        async for result in insert_manager.insert_text(
            text=request.text,
            kb_id=request.kb_id,
            description=request.description
        ):
            results.append(result)
        
        # 返回最后的结果
        final_result = results[-1] if results else {"error": "没有返回结果"}
        if "error" in final_result:
            raise HTTPException(status_code=400, detail=final_result["error"])
        
        return final_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"插入文本失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/insert/file/{kb_id}")
async def insert_file(
    kb_id: str,
    file: UploadFile = File(...),
    description: str = Form("")
):
    """上传并插入文件到知识库"""
    try:
        # 保存上传的文件
        file_content = await file.read()
        file_path = insert_manager.file_manager.save_uploaded_file(
            file_content, file.filename
        )

        results = []
        async for result in insert_manager.insert_file(
            file_path=file_path,
            kb_id=kb_id,
            description=description
        ):
            results.append(result)

        # 清理临时文件
        try:
            os.unlink(file_path)
        except:
            pass

        # 返回最后的结果
        final_result = results[-1] if results else {"error": "没有返回结果"}
        if "error" in final_result:
            raise HTTPException(status_code=400, detail=final_result["error"])

        return final_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"插入文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/insert/directory/{kb_id}", response_model=ApiResponse)
async def insert_directory(
    kb_id: str,
    directory_path: str = Form(...),
    recursive: bool = Form(True),
    file_extensions: Optional[str] = Form(None),
    description: str = Form("")
):
    """批量导入目录中的文件到知识库"""
    try:
        from pathlib import Path

        dir_path = Path(directory_path)
        if not dir_path.exists() or not dir_path.is_dir():
            raise HTTPException(status_code=400, detail="目录路径不存在或不是有效目录")

        # 解析文件扩展名
        allowed_extensions = None
        if file_extensions:
            allowed_extensions = [ext.strip() for ext in file_extensions.split(',')]

        # 收集文件
        files_to_process = []
        if recursive:
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    if not allowed_extensions or file_path.suffix.lower() in allowed_extensions:
                        if insert_manager.document_processor.can_process(str(file_path)):
                            files_to_process.append(file_path)
        else:
            for file_path in dir_path.iterdir():
                if file_path.is_file():
                    if not allowed_extensions or file_path.suffix.lower() in allowed_extensions:
                        if insert_manager.document_processor.can_process(str(file_path)):
                            files_to_process.append(file_path)

        if not files_to_process:
            return ApiResponse(
                success=False,
                message="目录中没有找到可处理的文件",
                data={"directory": str(dir_path), "total_files": 0}
            )

        # 批量处理文件
        results = []
        successful = 0
        failed = 0

        for file_path in files_to_process:
            try:
                file_results = []
                async for result in insert_manager.insert_file(
                    file_path=str(file_path),
                    kb_id=kb_id,
                    description=f"{description} - {file_path.name}"
                ):
                    file_results.append(result)

                final_result = file_results[-1] if file_results else {}
                if "error" not in final_result:
                    successful += 1
                    results.append({
                        "file": str(file_path),
                        "status": "success",
                        "message": "文件处理成功"
                    })
                else:
                    failed += 1
                    results.append({
                        "file": str(file_path),
                        "status": "error",
                        "message": final_result.get("error", "未知错误")
                    })

            except Exception as e:
                failed += 1
                results.append({
                    "file": str(file_path),
                    "status": "error",
                    "message": str(e)
                })

        return ApiResponse(
            success=successful > 0,
            message=f"目录导入完成，成功: {successful}, 失败: {failed}",
            data={
                "directory": str(dir_path),
                "total_files": len(files_to_process),
                "successful": successful,
                "failed": failed,
                "results": results
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"目录导入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/insert/supported-formats", response_model=ApiResponse)
async def get_supported_formats():
    """获取支持的文件格式"""
    try:
        supported_formats = {
            ".txt": "纯文本文件",
            ".md": "Markdown文件",
            ".docx": "Word文档",
            ".pdf": "PDF文档",
            ".json": "JSON文件",
            ".xml": "XML文件",
            ".zip": "ZIP压缩包"
        }

        return ApiResponse(
            success=True,
            message="支持的文件格式获取成功",
            data={
                "formats": supported_formats,
                "total_count": len(supported_formats)
            }
        )

    except Exception as e:
        logger.error(f"获取支持格式失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query")
async def query_knowledge_bases(request: QueryRequest):
    """查询知识库 - 支持所有查询模式"""
    try:
        if not request.kb_ids:
            # 查询所有启用的知识库
            enabled_kbs = config_manager.get_enabled_knowledge_bases()
            kb_ids = list(enabled_kbs.keys())
        else:
            kb_ids = request.kb_ids

        if len(kb_ids) == 1:
            # 单知识库查询
            result = await query_manager.query_single_kb(
                query=request.query,
                kb_id=kb_ids[0],
                mode=request.mode,
                stream=request.stream
            )

            if hasattr(result, '__aiter__'):
                # 流式响应
                async def generate():
                    async for chunk in result:
                        yield f"data: {json.dumps({'chunk': chunk}, ensure_ascii=False)}\n\n"

                return StreamingResponse(generate(), media_type="text/plain")
            else:
                # 非流式响应
                return {
                    "success": True,
                    "message": "查询成功",
                    "data": {
                        "response": str(result),
                        "mode": request.mode.value,
                        "kb_id": kb_ids[0],
                        "timestamp": datetime.now().isoformat()
                    }
                }
        else:
            # 多知识库查询
            results = await query_manager.query_multiple_kbs(
                query=request.query,
                kb_ids=kb_ids,
                mode=request.mode,
                stream=request.stream
            )

            if hasattr(results, '__aiter__'):
                # 流式响应
                async def generate():
                    async for chunk in results:
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

                return StreamingResponse(generate(), media_type="text/plain")
            else:
                # 非流式响应
                return {
                    "success": True,
                    "message": "查询成功",
                    "data": {
                        "results": results,
                        "mode": request.mode.value,
                        "kb_ids": kb_ids,
                        "timestamp": datetime.now().isoformat()
                    }
                }

    except Exception as e:
        logger.error(f"查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/advanced", response_model=ApiResponse)
async def advanced_query(request: dict):
    """
    高级查询接口 - 支持GuiXiaoxi的所有查询参数
    """
    try:
        from server.api.routes.advanced_query import AdvancedQueryRequest

        # 解析请求
        advanced_request = AdvancedQueryRequest(**request)

        # 转换查询模式
        mode_mapping = {
            "local": QueryMode.LOCAL,
            "global": QueryMode.GLOBAL,
            "hybrid": QueryMode.HYBRID,
            "naive": QueryMode.NAIVE,
            "mix": QueryMode.MIX,  # 保留mix查询方案
            "bypass": QueryMode.BYPASS
        }

        query_mode = mode_mapping.get(advanced_request.mode, QueryMode.HYBRID)

        # 构建查询参数
        query_params = {
            "mode": query_mode,
            "knowledge_base_ids": advanced_request.knowledge_base_ids,
            "top_k": advanced_request.top_k,
            "enable_stream": False,
            "response_type": advanced_request.response_type,
            "user_prompt": advanced_request.user_prompt,
            "conversation_history": advanced_request.conversation_history,
            "history_turns": advanced_request.history_turns,
            "only_need_context": advanced_request.only_need_context,
            "only_need_prompt": advanced_request.only_need_prompt,
            "max_token_for_text_unit": advanced_request.max_token_for_text_unit,
            "max_token_for_global_context": advanced_request.max_token_for_global_context,
            "max_token_for_local_context": advanced_request.max_token_for_local_context,
            "chunk_top_k": advanced_request.chunk_top_k,
            "chunk_rerank_top_k": advanced_request.chunk_rerank_top_k
        }

        # 执行查询
        if query_mode == QueryMode.BYPASS:
            # 通用问答模式 - 使用简化的LLM调用
            from guixiaoxi.llm.openai import openai_complete_if_cache

            result = await asyncio.wait_for(
                openai_complete_if_cache(
                    model=config_manager.llm.model,
                    prompt=advanced_request.query,
                    api_key=config_manager.llm.api_key,
                    base_url=config_manager.llm.api_base,
                    temperature=config_manager.llm.temperature,
                    max_tokens=config_manager.llm.max_tokens,
                    timeout=60
                ),
                timeout=90
            )
        else:
            # RAG查询模式
            result = await query_manager.query_async(advanced_request.query, **query_params)

        # 处理特殊响应类型
        if advanced_request.only_need_context:
            return ApiResponse(
                success=True,
                message="上下文检索成功",
                data={
                    "context": result,
                    "mode": advanced_request.mode,
                    "timestamp": datetime.now().isoformat()
                }
            )

        if advanced_request.only_need_prompt:
            return ApiResponse(
                success=True,
                message="提示词生成成功",
                data={
                    "prompt": result,
                    "mode": advanced_request.mode,
                    "timestamp": datetime.now().isoformat()
                }
            )

        # 标准查询响应
        return ApiResponse(
            success=True,
            message="查询成功",
            data={
                "response": result,
                "mode": advanced_request.mode,
                "knowledge_bases": advanced_request.knowledge_base_ids or "all_enabled",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"高级查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/stream")
async def stream_query(request: dict):
    """
    流式查询接口 - 实时返回查询结果
    """
    try:
        from server.api.routes.advanced_query import AdvancedQueryRequest

        # 解析请求
        advanced_request = AdvancedQueryRequest(**request)

        # 转换查询模式
        mode_mapping = {
            "local": QueryMode.LOCAL,
            "global": QueryMode.GLOBAL,
            "hybrid": QueryMode.HYBRID,
            "naive": QueryMode.NAIVE,
            "mix": QueryMode.MIX,  # 保留mix查询方案
            "bypass": QueryMode.BYPASS
        }

        query_mode = mode_mapping.get(advanced_request.mode, QueryMode.HYBRID)

        # 构建查询参数
        query_params = {
            "mode": query_mode,
            "knowledge_base_ids": advanced_request.knowledge_base_ids,
            "top_k": advanced_request.top_k,
            "enable_stream": True,
            "response_type": advanced_request.response_type,
            "user_prompt": advanced_request.user_prompt,
            "conversation_history": advanced_request.conversation_history,
            "history_turns": advanced_request.history_turns,
            "max_token_for_text_unit": advanced_request.max_token_for_text_unit,
            "max_token_for_global_context": advanced_request.max_token_for_global_context,
            "max_token_for_local_context": advanced_request.max_token_for_local_context,
            "chunk_top_k": advanced_request.chunk_top_k,
            "chunk_rerank_top_k": advanced_request.chunk_rerank_top_k
        }

        async def stream_generator():
            """流式响应生成器"""
            try:
                # 发送开始标记
                yield json.dumps({
                    "type": "start",
                    "message": "查询开始",
                    "mode": advanced_request.mode,
                    "timestamp": datetime.now().isoformat()
                }) + "\n"

                # 执行流式查询
                async for chunk in query_manager.query_stream_async(advanced_request.query, **query_params):
                    if chunk:
                        yield json.dumps({
                            "type": "chunk",
                            "content": chunk,
                            "timestamp": datetime.now().isoformat()
                        }) + "\n"

                # 发送结束标记
                yield json.dumps({
                    "type": "end",
                    "message": "查询完成",
                    "timestamp": datetime.now().isoformat()
                }) + "\n"

            except Exception as e:
                logger.error(f"流式查询错误: {e}")
                yield json.dumps({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }) + "\n"

        return StreamingResponse(
            stream_generator(),
            media_type="application/x-ndjson",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "application/x-ndjson",
                "X-Accel-Buffering": "no"
            }
        )

    except Exception as e:
        logger.error(f"流式查询初始化失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/query/modes", response_model=ApiResponse)
async def get_query_modes():
    """获取支持的查询模式列表"""
    return ApiResponse(
        success=True,
        message="查询模式列表",
        data={
            "modes": [
                {
                    "name": "local",
                    "description": "本地实体查询，基于实体相似性检索",
                    "use_case": "适用于查询特定实体的详细信息"
                },
                {
                    "name": "global",
                    "description": "全局关系查询，基于关系图谱检索",
                    "use_case": "适用于查询实体间的关系和连接"
                },
                {
                    "name": "hybrid",
                    "description": "混合查询，结合本地和全局信息",
                    "use_case": "适用于大多数复杂查询场景"
                },
                {
                    "name": "naive",
                    "description": "简单向量检索，基于文档相似性",
                    "use_case": "适用于简单的文档检索"
                },
                {
                    "name": "mix",
                    "description": "混合模式查询，兼容性查询方案",
                    "use_case": "适用于需要兼容旧版本的查询场景"
                },
                {
                    "name": "bypass",
                    "description": "直接LLM查询，跳过RAG检索",
                    "use_case": "适用于通用对话和不需要特定知识的查询"
                }
            ]
        }
    )


@app.post("/query/batch", response_model=ApiResponse)
async def batch_query(request: BatchQueryRequest):
    """批量查询"""
    try:
        if not request.queries:
            raise HTTPException(status_code=400, detail="查询列表不能为空")

        # 获取启用的知识库
        enabled_kbs = config_manager.get_enabled_knowledge_bases()
        kb_ids = list(enabled_kbs.keys())

        if not kb_ids:
            raise HTTPException(status_code=400, detail="没有启用的知识库")

        results = []
        start_time = asyncio.get_event_loop().time()

        # 批量处理查询
        for i, query_text in enumerate(request.queries):
            try:
                if len(kb_ids) == 1:
                    # 单知识库查询
                    result = await query_manager.query_single_kb(
                        query=query_text,
                        kb_id=kb_ids[0],
                        mode=request.mode,
                        stream=False  # 批量查询不使用流式输出
                    )
                    results.append({
                        "query": query_text,
                        "result": str(result),
                        "kb_id": kb_ids[0],
                        "status": "success"
                    })
                else:
                    # 多知识库查询
                    kb_results = await query_manager.query_multiple_kbs(
                        query=query_text,
                        kb_ids=kb_ids,
                        mode=request.mode,
                        stream=False
                    )
                    results.append({
                        "query": query_text,
                        "results": kb_results,
                        "status": "success"
                    })

            except Exception as e:
                logger.error(f"批量查询第{i+1}个问题失败: {e}")
                results.append({
                    "query": query_text,
                    "result": f"查询失败: {str(e)}",
                    "status": "error"
                })

        end_time = asyncio.get_event_loop().time()
        processing_time = round(end_time - start_time, 2)

        return ApiResponse(
            success=True,
            message="批量查询完成",
            data={
                "total": len(request.queries),
                "processed": len(results),
                "processing_time": f"{processing_time}s",
                "results": results
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量查询失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/query/templates", response_model=ApiResponse)
async def get_query_templates():
    """获取查询模板"""
    try:
        templates = {
            "学院信息": {
                "template": "请介绍计算机科学与技术学院的基本情况",
                "description": "获取学院的基本信息、历史沿革等",
                "category": "学院概况"
            },
            "专业查询": {
                "template": "请介绍{专业名称}专业的培养目标和课程设置",
                "description": "查询特定专业的详细信息",
                "category": "专业信息",
                "parameters": ["专业名称"]
            },
            "师资查询": {
                "template": "请介绍{教师姓名}的研究方向和学术成果",
                "description": "查询教师的详细信息",
                "category": "师资力量",
                "parameters": ["教师姓名"]
            },
            "课程信息": {
                "template": "请介绍{课程名称}的课程内容和教学安排",
                "description": "查询课程的详细信息",
                "category": "课程设置",
                "parameters": ["课程名称"]
            },
            "招生信息": {
                "template": "请介绍计算机学院的招生政策和录取要求",
                "description": "获取招生相关信息",
                "category": "招生就业"
            },
            "科研成果": {
                "template": "请介绍计算机学院在{研究领域}方面的科研成果",
                "description": "查询特定领域的科研成果",
                "category": "科学研究",
                "parameters": ["研究领域"]
            }
        }

        return ApiResponse(
            success=True,
            message="查询模板获取成功",
            data={
                "templates": templates,
                "total_count": len(templates),
                "categories": list(set(t.get("category", "其他") for t in templates.values()))
            }
        )

    except Exception as e:
        logger.error(f"获取查询模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/general")
async def general_qa(request: GeneralQARequest):
    """通用问答（不使用知识库）"""
    try:
        result = await query_manager.general_qa(
            query=request.query,
            stream=request.stream
        )

        if hasattr(result, '__aiter__'):
            # 流式响应
            async def generate():
                async for chunk in result:
                    yield f"data: {json.dumps({'chunk': chunk}, ensure_ascii=False)}\n\n"

            return StreamingResponse(generate(), media_type="text/plain")
        else:
            # 非流式响应
            return {"result": result}

    except Exception as e:
        logger.error(f"通用问答失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/query/history")
async def get_query_history(limit: int = 50):
    """获取查询历史"""
    try:
        history = query_manager.get_query_history(limit)
        return {"history": history}
    except Exception as e:
        logger.error(f"获取查询历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/query/history")
async def clear_query_history():
    """清空查询历史"""
    try:
        query_manager.clear_query_history()
        return {"message": "查询历史已清空"}
    except Exception as e:
        logger.error(f"清空查询历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/stats")
async def get_stats():
    """获取系统统计信息"""
    try:
        kb_stats = query_manager.get_knowledge_base_stats()
        return {
            "knowledge_bases": kb_stats,
            "query_history_count": len(query_manager.get_query_history(1000))
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graph/stats", response_model=ApiResponse)
async def get_graph_stats():
    """获取知识图谱统计信息"""
    try:
        kb_stats = query_manager.get_knowledge_base_stats()

        # 计算总体统计
        total_nodes = 0
        total_edges = 0
        loaded_kbs = 0

        for kb_id, kb_info in kb_stats.items():
            if kb_info.get("status") == "loaded":
                loaded_kbs += 1
                # 这里可以添加具体的节点和边统计逻辑

        return ApiResponse(
            success=True,
            message="图统计信息获取成功",
            data={
                "knowledge_bases": kb_stats,
                "summary": {
                    "total_knowledge_bases": len(kb_stats),
                    "loaded_knowledge_bases": loaded_kbs,
                    "total_nodes": total_nodes,
                    "total_edges": total_edges
                },
                "last_updated": asyncio.get_event_loop().time()
            }
        )

    except Exception as e:
        logger.error(f"获取图统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graph/export", response_model=ApiResponse)
async def export_graph_data(
    kb_id: Optional[str] = None,
    format: str = "json",
    limit: Optional[int] = None
):
    """导出知识图谱数据"""
    try:
        if kb_id:
            # 导出指定知识库的图数据
            rag_instance = query_manager.kb_manager.get_rag_instance(kb_id)
            if not rag_instance:
                raise HTTPException(status_code=404, detail=f"知识库 {kb_id} 不存在或未加载")

            # 这里需要实现具体的图数据导出逻辑
            # 由于当前的RAG实例可能没有直接的图数据访问方法，我们返回基本信息
            graph_data = {
                "kb_id": kb_id,
                "nodes": [],
                "edges": [],
                "metadata": {
                    "export_time": asyncio.get_event_loop().time(),
                    "format": format,
                    "total_nodes": 0,
                    "total_edges": 0
                }
            }
        else:
            # 导出所有知识库的图数据
            all_graph_data = {}
            for kb_id in config_manager.get_enabled_knowledge_bases().keys():
                rag_instance = query_manager.kb_manager.get_rag_instance(kb_id)
                if rag_instance:
                    all_graph_data[kb_id] = {
                        "nodes": [],
                        "edges": [],
                        "metadata": {
                            "export_time": asyncio.get_event_loop().time(),
                            "format": format
                        }
                    }

            graph_data = {
                "knowledge_bases": all_graph_data,
                "summary": {
                    "total_knowledge_bases": len(all_graph_data),
                    "export_time": asyncio.get_event_loop().time(),
                    "format": format
                }
            }

        return ApiResponse(
            success=True,
            message="图数据导出成功",
            data=graph_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出图数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=config_manager.api.host,
        port=config_manager.api.port,
        log_level=config_manager.logging.level.lower()
    )


@app.post("/insert/file/{kb_id}")
async def insert_file(
    kb_id: str,
    file: UploadFile = File(...),
    description: str = Form("")
):
    """上传并插入文件到知识库"""
    try:
        # 保存上传的文件
        file_content = await file.read()
        file_path = insert_manager.file_manager.save_uploaded_file(
            file_content, file.filename
        )
        
        results = []
        async for result in insert_manager.insert_file(
            file_path=file_path,
            kb_id=kb_id,
            description=description
        ):
            results.append(result)
        
        # 清理临时文件
        try:
            os.unlink(file_path)
        except:
            pass
        
        # 返回最后的结果
        final_result = results[-1] if results else {"error": "没有返回结果"}
        if "error" in final_result:
            raise HTTPException(status_code=400, detail=final_result["error"])
        
        return final_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"插入文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))





@app.get("/query/history")
async def get_query_history(limit: int = 50):
    """获取查询历史"""
    try:
        history = query_manager.get_query_history(limit)
        return {"history": history}
    except Exception as e:
        logger.error(f"获取查询历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/query/history")
async def clear_query_history():
    """清空查询历史"""
    try:
        query_manager.clear_query_history()
        return {"message": "查询历史已清空"}
    except Exception as e:
        logger.error(f"清空查询历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/stats")
async def get_stats():
    """获取系统统计信息"""
    try:
        kb_stats = query_manager.get_knowledge_base_stats()
        return {
            "knowledge_bases": kb_stats,
            "query_history_count": len(query_manager.get_query_history(1000))
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query/test")
async def test_query(request: dict):
    """测试查询端点 - 绕过复杂的RAG逻辑"""
    try:
        query = request.get("query", "")
        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        logger.info(f"测试查询: {query}")

        # 直接调用LLM
        from guixiaoxi.llm.openai import openai_complete_if_cache

        result = await asyncio.wait_for(
            openai_complete_if_cache(
                model=config_manager.llm.model,
                prompt=query,
                api_key=config_manager.llm.api_key,
                base_url=config_manager.llm.api_base,
                timeout=30
            ),
            timeout=60
        )

        return {
            "success": True,
            "data": {
                "response": result,
                "mode": "test",
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"测试查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试查询失败: {str(e)}")


@app.post("/query/stream")
async def streaming_query(request: dict):
    """
    流式查询接口 - 基于OpenAI客户端的实现
    """
    try:
        from openai import AsyncOpenAI

        query = request.get("query", "")
        mode = request.get("mode", "hybrid")
        knowledge_base_ids = request.get("knowledge_base_ids", ["cs_college"])
        max_tokens = request.get("max_tokens", 1000)

        if not query:
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        logger.info(f"开始流式查询: {query[:50]}...")

        async def create_streaming_response():
            try:
                # 初始化OpenAI客户端
                client = AsyncOpenAI(
                    api_key=config_manager.llm.api_key,
                    base_url=config_manager.llm.api_base
                )

                # 构建消息
                if mode == "bypass":
                    # 直接LLM查询
                    messages = [
                        {"role": "user", "content": query}
                    ]
                else:
                    # RAG查询 - 先获取上下文
                    mode_mapping = {
                        "local": QueryMode.LOCAL,
                        "global": QueryMode.GLOBAL,
                        "hybrid": QueryMode.HYBRID,
                        "naive": QueryMode.NAIVE,
                        "mix": QueryMode.MIX,
                        "bypass": QueryMode.BYPASS
                    }

                    query_mode = mode_mapping.get(mode, QueryMode.HYBRID)

                    # 获取RAG上下文（非流式）
                    try:
                        context_result = await query_manager.query(
                            query=query,
                            mode=query_mode,
                            kb_ids=knowledge_base_ids,
                            stream=False,
                            max_tokens=max_tokens
                        )

                        # 构建包含上下文的消息
                        system_prompt = f"""你是一个智能助手，请基于以下上下文信息回答用户的问题。

上下文信息：
{context_result}

请根据上下文信息准确回答问题，如果上下文中没有相关信息，请说明。"""

                        messages = [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": query}
                        ]
                    except Exception as e:
                        logger.error(f"获取RAG上下文失败: {e}")
                        # 降级为直接查询
                        messages = [
                            {"role": "user", "content": query}
                        ]

                # 发送流式请求
                completion = await client.chat.completions.create(
                    model=config_manager.llm.model,
                    messages=messages,
                    temperature=0.6,
                    max_tokens=max_tokens,
                    stream=True
                )

                # 流式返回结果
                async for chunk in completion:
                    if chunk.choices and len(chunk.choices) > 0:
                        delta = chunk.choices[0].delta

                        # 处理推理内容（如果有）
                        if hasattr(delta, "reasoning_content") and delta.reasoning_content:
                            content = delta.reasoning_content
                        elif delta.content:
                            content = delta.content
                        else:
                            continue

                        # 构建SSE格式的响应
                        sse_data = {
                            "type": "delta",
                            "delta": {
                                "content": content
                            },
                            "timestamp": datetime.now().isoformat()
                        }

                        yield f"data: {json.dumps(sse_data, ensure_ascii=False)}\n\n"

                # 发送结束标记
                end_data = {
                    "type": "end",
                    "timestamp": datetime.now().isoformat()
                }
                yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

            except Exception as e:
                logger.error(f"流式查询失败: {e}")
                error_data = {
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

        # 返回流式响应
        return StreamingResponse(
            create_streaming_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )

    except Exception as e:
        logger.error(f"流式查询接口错误: {e}")
        raise HTTPException(status_code=500, detail=f"流式查询失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=config_manager.api.host,
        port=config_manager.api.port,
        log_level=config_manager.logging.level.lower()
    )
