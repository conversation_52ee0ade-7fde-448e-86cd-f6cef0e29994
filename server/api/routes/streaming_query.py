#!/usr/bin/env python3
"""
流式查询路由 - 基于OpenAI客户端的实现
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, AsyncGenerator
import json
import asyncio
import logging
from datetime import datetime

from server.core.query import QueryManager, QueryMode
from server.config.config import config_manager

router = APIRouter()
logger = logging.getLogger(__name__)

class StreamingQueryRequest(BaseModel):
    """流式查询请求模型"""
    query: str
    mode: str = "hybrid"
    knowledge_base_ids: Optional[List[str]] = None
    response_type: str = "Multiple Paragraphs"
    top_k: int = 10
    max_tokens: int = 1000

async def create_openai_streaming_response(
    query: str,
    mode: str,
    knowledge_base_ids: Optional[List[str]] = None,
    **kwargs
) -> AsyncGenerator[str, None]:
    """
    创建基于OpenAI客户端的流式响应
    """
    try:
        from openai import AsyncOpenAI
        
        # 初始化OpenAI客户端
        client = AsyncOpenAI(
            api_key=config_manager.llm.api_key,
            base_url=config_manager.llm.api_base
        )
        
        # 构建消息
        if mode == "bypass":
            # 直接LLM查询
            messages = [
                {"role": "user", "content": query}
            ]
        else:
            # RAG查询 - 先获取上下文
            query_manager = QueryManager()
            
            # 转换查询模式
            mode_mapping = {
                "local": QueryMode.LOCAL,
                "global": QueryMode.GLOBAL,
                "hybrid": QueryMode.HYBRID,
                "naive": QueryMode.NAIVE,
                "mix": QueryMode.MIX,
                "bypass": QueryMode.BYPASS
            }
            
            query_mode = mode_mapping.get(mode, QueryMode.HYBRID)
            kb_ids = knowledge_base_ids or ["cs_college"]
            
            # 获取RAG上下文（非流式）
            try:
                context_result = await query_manager.query(
                    query=query,
                    mode=query_mode,
                    kb_ids=kb_ids,
                    stream=False,
                    **kwargs
                )
                
                # 构建包含上下文的消息
                system_prompt = f"""你是一个智能助手，请基于以下上下文信息回答用户的问题。

上下文信息：
{context_result}

请根据上下文信息准确回答问题，如果上下文中没有相关信息，请说明。"""
                
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query}
                ]
            except Exception as e:
                logger.error(f"获取RAG上下文失败: {e}")
                # 降级为直接查询
                messages = [
                    {"role": "user", "content": query}
                ]
        
        # 发送流式请求
        completion = await client.chat.completions.create(
            model=config_manager.llm.model,
            messages=messages,
            temperature=0.6,
            max_tokens=kwargs.get('max_tokens', 1000),
            stream=True
        )
        
        # 流式返回结果
        async for chunk in completion:
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                
                # 处理推理内容（如果有）
                if hasattr(delta, "reasoning_content") and delta.reasoning_content:
                    content = delta.reasoning_content
                elif delta.content:
                    content = delta.content
                else:
                    continue
                
                # 构建SSE格式的响应
                sse_data = {
                    "type": "delta",
                    "delta": {
                        "content": content
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
                yield f"data: {json.dumps(sse_data, ensure_ascii=False)}\n\n"
        
        # 发送结束标记
        end_data = {
            "type": "end",
            "timestamp": datetime.now().isoformat()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"
        
    except Exception as e:
        logger.error(f"流式查询失败: {e}")
        error_data = {
            "type": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

@router.post("/stream")
async def streaming_query(request: StreamingQueryRequest):
    """
    流式查询接口
    
    支持的查询模式：
    - local: 本地实体查询
    - global: 全局关系查询  
    - hybrid: 混合查询
    - naive: 简单向量检索
    - mix: 混合检索模式
    - bypass: 直接LLM查询
    """
    try:
        logger.info(f"开始流式查询: {request.query[:50]}...")
        
        # 创建流式响应生成器
        response_generator = create_openai_streaming_response(
            query=request.query,
            mode=request.mode,
            knowledge_base_ids=request.knowledge_base_ids,
            response_type=request.response_type,
            top_k=request.top_k,
            max_tokens=request.max_tokens
        )
        
        # 返回流式响应
        return StreamingResponse(
            response_generator,
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )
        
    except Exception as e:
        logger.error(f"流式查询接口错误: {e}")
        raise HTTPException(status_code=500, detail=f"流式查询失败: {str(e)}")

@router.get("/stream/test")
async def test_streaming():
    """测试流式响应"""
    async def test_generator():
        for i in range(10):
            data = {
                "type": "delta",
                "delta": {
                    "content": f"测试消息 {i+1}\n"
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
            await asyncio.sleep(0.5)
        
        end_data = {
            "type": "end",
            "timestamp": datetime.now().isoformat()
        }
        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        test_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive", 
            "Content-Type": "text/event-stream"
        }
    )
