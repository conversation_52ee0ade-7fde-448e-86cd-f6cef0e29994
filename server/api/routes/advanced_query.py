"""
高级查询路由 - 基于GuiXiaoxi的完整查询功能实现
支持流式查询、多模式查询、上下文查询等高级功能
"""

import json
import logging
import asyncio
from typing import Any, Dict, List, Literal, Optional, AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, field_validator

from server.core.query import query_manager, QueryMode
from server.config.config import config_manager
from server.api.exceptions import QueryException

logger = logging.getLogger("advanced_query")
router = APIRouter(prefix="/query", tags=["Advanced Query"])


class AdvancedQueryRequest(BaseModel):
    """高级查询请求模型 - 基于GuiXiaoxi的QueryRequest"""
    
    query: str = Field(
        min_length=1,
        description="查询文本",
        example="什么是人工智能？"
    )
    
    mode: Literal["local", "global", "hybrid", "naive", "mix", "bypass"] = Field(
        default="hybrid",
        description="查询模式：local(本地实体), global(全局关系), hybrid(混合), naive(简单向量), mix(混合模式), bypass(直接LLM)"
    )
    
    knowledge_base_ids: Optional[List[str]] = Field(
        default=None,
        description="指定查询的知识库ID列表，为空则查询所有启用的知识库"
    )
    
    only_need_context: Optional[bool] = Field(
        default=None,
        description="如果为True，只返回检索到的上下文，不生成回答"
    )
    
    only_need_prompt: Optional[bool] = Field(
        default=None,
        description="如果为True，只返回生成的提示词，不产生回答"
    )
    
    response_type: Optional[str] = Field(
        default=None,
        description="响应格式类型，如：'Multiple Paragraphs', 'Single Paragraph', 'Bullet Points'",
        example="Bullet Points"
    )
    
    top_k: Optional[int] = Field(
        ge=1,
        le=100,
        default=None,
        description="检索的top-k项目数量。在local模式下表示实体数量，在global模式下表示关系数量"
    )
    
    chunk_top_k: Optional[int] = Field(
        ge=1,
        le=50,
        default=None,
        description="从向量搜索中初始检索的文本块数量"
    )
    
    chunk_rerank_top_k: Optional[int] = Field(
        ge=1,
        le=20,
        default=None,
        description="重排序后保留的文本块数量"
    )
    
    max_token_for_text_unit: Optional[int] = Field(
        gt=1,
        default=None,
        description="每个检索文本块允许的最大token数量"
    )
    
    max_token_for_global_context: Optional[int] = Field(
        gt=1,
        default=None,
        description="全局检索中关系描述分配的最大token数量"
    )
    
    max_token_for_local_context: Optional[int] = Field(
        gt=1,
        default=None,
        description="本地检索中实体描述分配的最大token数量"
    )
    
    conversation_history: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="对话历史记录，格式：[{'role': 'user/assistant', 'content': 'message'}]"
    )
    
    history_turns: Optional[int] = Field(
        ge=0,
        le=10,
        default=None,
        description="考虑的完整对话轮次数量（用户-助手对）"
    )
    
    user_prompt: Optional[str] = Field(
        default=None,
        description="用户自定义提示词，如果提供，将替代默认提示模板"
    )
    
    enable_stream: Optional[bool] = Field(
        default=None,
        description="是否启用流式输出"
    )
    
    @field_validator("query", mode="after")
    @classmethod
    def query_strip_after(cls, query: str) -> str:
        return query.strip()
    
    @field_validator("conversation_history", mode="after")
    @classmethod
    def conversation_history_role_check(
        cls, conversation_history: List[Dict[str, Any]] | None
    ) -> List[Dict[str, Any]] | None:
        if conversation_history is None:
            return None
        for msg in conversation_history:
            if "role" not in msg or msg["role"] not in {"user", "assistant"}:
                raise ValueError(
                    "每条消息必须包含'role'键，值为'user'或'assistant'"
                )
        return conversation_history


class QueryResponse(BaseModel):
    """查询响应模型"""
    success: bool = Field(description="查询是否成功")
    message: str = Field(description="响应消息")
    data: Dict[str, Any] = Field(description="查询结果数据")


class ContextResponse(BaseModel):
    """上下文响应模型"""
    success: bool = Field(description="查询是否成功")
    message: str = Field(description="响应消息")
    data: Dict[str, Any] = Field(description="上下文数据")


class PromptResponse(BaseModel):
    """提示词响应模型"""
    success: bool = Field(description="查询是否成功")
    message: str = Field(description="响应消息")
    data: Dict[str, Any] = Field(description="提示词数据")


@router.post("/advanced", response_model=QueryResponse)
async def advanced_query(request: AdvancedQueryRequest):
    """
    高级查询接口 - 支持多种查询模式和参数
    
    支持的查询模式：
    - local: 本地实体查询，基于实体相似性
    - global: 全局关系查询，基于关系图谱
    - hybrid: 混合查询，结合本地和全局信息
    - naive: 简单向量检索
    - bypass: 直接LLM查询，跳过RAG
    """
    try:
        start_time = datetime.now()
        
        # 转换查询模式
        mode_mapping = {
            "local": QueryMode.LOCAL,
            "global": QueryMode.GLOBAL,
            "hybrid": QueryMode.HYBRID,
            "naive": QueryMode.NAIVE,
            "mix": QueryMode.MIX,  # 添加mix模式
            "bypass": QueryMode.BYPASS
        }
        
        query_mode = mode_mapping.get(request.mode, QueryMode.HYBRID)
        
        # 构建查询参数
        query_params = {
            "mode": query_mode,
            "knowledge_base_ids": request.knowledge_base_ids,
            "top_k": request.top_k,
            "enable_stream": False,  # 非流式查询
            "response_type": request.response_type,
            "user_prompt": request.user_prompt,
            "conversation_history": request.conversation_history,
            "history_turns": request.history_turns,
            "only_need_context": request.only_need_context,
            "only_need_prompt": request.only_need_prompt,
            "max_token_for_text_unit": request.max_token_for_text_unit,
            "max_token_for_global_context": request.max_token_for_global_context,
            "max_token_for_local_context": request.max_token_for_local_context,
            "chunk_top_k": request.chunk_top_k,
            "chunk_rerank_top_k": request.chunk_rerank_top_k
        }
        
        # 执行查询
        result = await query_manager.query_async(request.query, **query_params)
        
        # 计算查询时间
        query_time = (datetime.now() - start_time).total_seconds()
        
        # 处理特殊响应类型
        if request.only_need_context:
            return QueryResponse(
                success=True,
                message="上下文检索成功",
                data={
                    "context": result,
                    "query_time": query_time,
                    "mode": request.mode,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        if request.only_need_prompt:
            return QueryResponse(
                success=True,
                message="提示词生成成功",
                data={
                    "prompt": result,
                    "query_time": query_time,
                    "mode": request.mode,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 标准查询响应
        return QueryResponse(
            success=True,
            message="查询成功",
            data={
                "response": result,
                "query_time": query_time,
                "mode": request.mode,
                "knowledge_bases": request.knowledge_base_ids or "all_enabled",
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"高级查询失败: {e}")
        raise QueryException(f"查询处理失败: {str(e)}")


@router.post("/stream")
async def stream_query(request: AdvancedQueryRequest):
    """
    流式查询接口 - 实时返回查询结果
    
    返回NDJSON格式的流式数据，每行一个JSON对象
    """
    try:
        # 转换查询模式
        mode_mapping = {
            "local": QueryMode.LOCAL,
            "global": QueryMode.GLOBAL,
            "hybrid": QueryMode.HYBRID,
            "naive": QueryMode.NAIVE,
            "mix": QueryMode.MIX,  # 添加mix模式
            "bypass": QueryMode.BYPASS
        }
        
        query_mode = mode_mapping.get(request.mode, QueryMode.HYBRID)
        
        # 构建查询参数
        query_params = {
            "mode": query_mode,
            "knowledge_base_ids": request.knowledge_base_ids,
            "top_k": request.top_k,
            "enable_stream": True,  # 启用流式输出
            "response_type": request.response_type,
            "user_prompt": request.user_prompt,
            "conversation_history": request.conversation_history,
            "history_turns": request.history_turns,
            "max_token_for_text_unit": request.max_token_for_text_unit,
            "max_token_for_global_context": request.max_token_for_global_context,
            "max_token_for_local_context": request.max_token_for_local_context,
            "chunk_top_k": request.chunk_top_k,
            "chunk_rerank_top_k": request.chunk_rerank_top_k
        }
        
        async def stream_generator():
            """流式响应生成器"""
            try:
                # 发送开始标记
                yield json.dumps({
                    "type": "start",
                    "message": "查询开始",
                    "mode": request.mode,
                    "timestamp": datetime.now().isoformat()
                }) + "\n"
                
                # 执行流式查询
                async for chunk in query_manager.query_stream_async(request.query, **query_params):
                    if chunk:
                        yield json.dumps({
                            "type": "chunk",
                            "content": chunk,
                            "timestamp": datetime.now().isoformat()
                        }) + "\n"
                
                # 发送结束标记
                yield json.dumps({
                    "type": "end",
                    "message": "查询完成",
                    "timestamp": datetime.now().isoformat()
                }) + "\n"
                
            except Exception as e:
                logger.error(f"流式查询错误: {e}")
                yield json.dumps({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }) + "\n"
        
        return StreamingResponse(
            stream_generator(),
            media_type="application/x-ndjson",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "application/x-ndjson",
                "X-Accel-Buffering": "no"
            }
        )
        
    except Exception as e:
        logger.error(f"流式查询初始化失败: {e}")
        raise QueryException(f"流式查询启动失败: {str(e)}")


@router.get("/modes")
async def get_query_modes():
    """获取支持的查询模式列表"""
    return {
        "success": True,
        "message": "查询模式列表",
        "data": {
            "modes": [
                {
                    "name": "local",
                    "description": "本地实体查询，基于实体相似性检索",
                    "use_case": "适用于查询特定实体的详细信息"
                },
                {
                    "name": "global", 
                    "description": "全局关系查询，基于关系图谱检索",
                    "use_case": "适用于查询实体间的关系和连接"
                },
                {
                    "name": "hybrid",
                    "description": "混合查询，结合本地和全局信息",
                    "use_case": "适用于大多数复杂查询场景"
                },
                {
                    "name": "naive",
                    "description": "简单向量检索，基于文档相似性",
                    "use_case": "适用于简单的文档检索"
                },
                {
                    "name": "mix",
                    "description": "混合检索模式，结合知识图谱和向量检索",
                    "use_case": "适用于需要综合多种检索策略的复杂查询"
                },
                {
                    "name": "bypass",
                    "description": "直接LLM查询，跳过RAG检索",
                    "use_case": "适用于通用对话和不需要特定知识的查询"
                }
            ]
        }
    }
