"""
GuiXiaoxi RAG API 中间件
包含请求日志、安全、速率限制等中间件
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
import json

logger = logging.getLogger("guixiaoxi_api")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        start_time = time.time()
        
        # 记录请求信息
        client_ip = request.client.host if request.client else "unknown"
        method = request.method
        url = str(request.url)
        
        logger.info(f"请求开始: {method} {url} from {client_ip}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            logger.info(
                f"请求完成: {method} {url} - "
                f"状态码: {response.status_code} - "
                f"处理时间: {process_time:.3f}s"
            )
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"请求异常: {method} {url} - "
                f"错误: {str(e)} - "
                f"处理时间: {process_time:.3f}s"
            )
            raise


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(self, app, allowed_origins: list = None):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["*"]
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # 检查请求来源
        origin = request.headers.get("origin")
        if origin and self.allowed_origins != ["*"]:
            if origin not in self.allowed_origins:
                logger.warning(f"拒绝来自未授权源的请求: {origin}")
                raise HTTPException(status_code=403, detail="Forbidden: Invalid origin")
        
        # 处理请求
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """速率限制中间件"""
    
    def __init__(self, app, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # {client_ip: [(timestamp, count), ...]}
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                (timestamp, count) for timestamp, count in self.requests[client_ip]
                if current_time - timestamp < self.window_seconds
            ]
        
        # 计算当前窗口内的请求数
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        current_requests = sum(count for _, count in self.requests[client_ip])
        
        # 检查是否超过限制
        if current_requests >= self.max_requests:
            logger.warning(f"客户端 {client_ip} 超过速率限制: {current_requests}/{self.max_requests}")
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded. Max {self.max_requests} requests per {self.window_seconds} seconds"
            )
        
        # 记录当前请求
        self.requests[client_ip].append((current_time, 1))
        
        # 处理请求
        response = await call_next(request)
        
        # 添加速率限制信息到响应头
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(self.max_requests - current_requests - 1)
        response.headers["X-RateLimit-Reset"] = str(int(current_time + self.window_seconds))
        
        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            response = await call_next(request)
            return response
            
        except HTTPException:
            # FastAPI的HTTPException直接抛出
            raise
            
        except Exception as e:
            # 捕获其他异常并转换为标准格式
            logger.error(f"未处理的异常: {str(e)}", exc_info=True)
            
            # 返回标准错误响应
            error_response = {
                "success": False,
                "message": "服务器内部错误",
                "data": {
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            }
            
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content=error_response
            )


def setup_middleware(app, config_manager):
    """设置所有中间件"""
    
    # 错误处理中间件（最外层）
    app.add_middleware(ErrorHandlingMiddleware)
    
    # 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 安全中间件
    app.add_middleware(
        SecurityMiddleware,
        allowed_origins=config_manager.api.cors_origins
    )
    
    # 速率限制中间件
    if hasattr(config_manager.api, 'rate_limit_enabled') and config_manager.api.rate_limit_enabled:
        max_requests = getattr(config_manager.api, 'rate_limit_requests', 100)
        window_seconds = getattr(config_manager.api, 'rate_limit_window', 60)
        
        app.add_middleware(
            RateLimitMiddleware,
            max_requests=max_requests,
            window_seconds=window_seconds
        )
    
    logger.info("中间件设置完成")
