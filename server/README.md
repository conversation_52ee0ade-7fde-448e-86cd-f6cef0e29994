# GuiXiaoxi RAG Server

服务端代码组织结构，包含所有后端服务和核心功能模块。

## 📁 目录结构

```
server/
├── api/                    # API 服务模块
│   ├── app.py             # FastAPI 主应用
│   ├── middleware.py      # 中间件集合
│   └── __init__.py
├── web/                   # Web 界面模块
│   ├── streamlit_app.py   # Streamlit 应用
│   └── __init__.py
├── core/                  # 核心功能模块
│   ├── insert.py          # 文档插入模块
│   ├── query.py           # 查询模块
│   ├── token_optimizer.py # Token 优化器
│   └── __init__.py
├── config/                # 配置模块
│   ├── config.py          # 配置管理
│   ├── config.json        # 配置文件
│   ├── requirements.txt   # 依赖列表
│   └── __init__.py
├── tests/                 # 测试模块
│   ├── comprehensive_test.py  # 综合测试
│   ├── test_api.py           # API 测试
│   ├── verify_deployment.py # 部署验证
│   └── __init__.py
├── scripts/               # 脚本工具
│   ├── deploy.sh          # 部署脚本
│   ├── demo_usage.py      # 使用演示
│   └── __init__.py
├── utils/                 # 工具模块
│   ├── insert_old.py      # 旧版插入模块
│   ├── query_old.py       # 旧版查询模块
│   └── __init__.py
└── README.md              # 本文件
```

## 🚀 模块说明

### API 服务 (api/)
- **app.py**: FastAPI 主应用，提供 REST API 接口
- **middleware.py**: 中间件集合，包含日志、CORS、安全等功能

### Web 界面 (web/)
- **streamlit_app.py**: Streamlit Web 应用，提供用户界面

### 核心功能 (core/)
- **insert.py**: 文档插入和知识图谱构建
- **query.py**: 智能查询和检索功能
- **token_optimizer.py**: Token 使用优化

### 配置管理 (config/)
- **config.py**: 配置管理类和工具
- **config.json**: 系统配置文件
- **requirements.txt**: Python 依赖列表

### 测试模块 (tests/)
- **comprehensive_test.py**: 完整的系统测试
- **test_api.py**: API 接口测试
- **verify_deployment.py**: 部署验证脚本

### 脚本工具 (scripts/)
- **deploy.sh**: 自动化部署脚本
- **demo_usage.py**: 系统使用演示

### 工具模块 (utils/)
- 包含旧版本文件和辅助工具

## 🔧 使用方式

### 启动服务
```bash
# 从项目根目录启动
python start_server.py      # 启动 API 服务
python start_streamlit.py   # 启动完整服务（API + Web）
```

### 导入模块
```python
# 从项目根目录导入
from server.core.query import GuiXiaoxiQueryManager
from server.core.insert import GuiXiaoxiRAGManager
from server.config.config import config_manager
```

## 📝 开发说明

### 添加新功能
1. 在相应的模块目录下创建新文件
2. 更新 `__init__.py` 文件导出新功能
3. 在 API 应用中注册新的端点
4. 添加相应的测试用例

### 修改配置
1. 编辑 `server/config/config.json`
2. 或通过环境变量覆盖配置
3. 重启服务使配置生效

### 运行测试
```bash
# 从项目根目录运行
python server/tests/comprehensive_test.py
python server/tests/test_api.py
```

## 🔗 相关文档

- [API 参考文档](../docs/API_REFERENCE.md)
- [安装部署指南](../docs/INSTALLATION.md)
- [故障排除指南](../docs/TROUBLESHOOTING.md)
