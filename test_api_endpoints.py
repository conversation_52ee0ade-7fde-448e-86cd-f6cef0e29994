#!/usr/bin/env python3
"""
GuiXiaoxi RAG API 端点测试脚本
测试所有主要API端点的可用性和响应格式
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class GuiXiaoxiAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def test_endpoint(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     files: Optional[Dict] = None, expected_status: int = 200) -> Dict[str, Any]:
        """测试单个API端点"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                if files:
                    response = self.session.post(url, data=data, files=files)
                else:
                    response = self.session.post(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                return {"success": False, "error": f"Unsupported method: {method}"}
            
            result = {
                "success": response.status_code == expected_status,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "endpoint": endpoint,
                "method": method.upper()
            }
            
            try:
                result["response"] = response.json()
            except:
                result["response"] = response.text[:200] + "..." if len(response.text) > 200 else response.text
                
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "endpoint": endpoint,
                "method": method.upper()
            }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有API端点测试"""
        print("🚀 开始测试 GuiXiaoxi RAG API 端点...")
        print("=" * 60)
        
        results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "test_results": []
        }
        
        # 测试用例定义
        test_cases = [
            # 系统管理端点
            ("GET", "/", None, 200, "根端点"),
            ("GET", "/health", None, 200, "健康检查"),
            ("GET", "/config", None, 200, "获取配置"),
            ("GET", "/stats", None, 200, "系统统计"),
            
            # 知识库管理端点
            ("GET", "/knowledge-bases", None, 200, "列出知识库"),
            
            # 查询相关端点
            ("GET", "/query/modes", None, 200, "查询模式"),
            ("GET", "/query/templates", None, 200, "查询模板"),
            ("GET", "/query/history", None, 200, "查询历史"),
            
            # 图数据端点
            ("GET", "/graph/stats", None, 200, "图统计信息"),
            ("GET", "/graph/export", None, 200, "导出图数据"),
            
            # 文档管理端点
            ("GET", "/insert/supported-formats", None, 200, "支持格式"),
            
            # 查询测试（需要数据的端点）
            ("POST", "/query", {
                "query": "测试查询",
                "mode": "hybrid"
            }, 200, "基础查询"),
            
            ("POST", "/query/general", {
                "query": "什么是人工智能？",
                "stream": False
            }, 200, "通用问答"),
            
            ("POST", "/query/batch", {
                "queries": ["测试问题1", "测试问题2"],
                "mode": "hybrid"
            }, 200, "批量查询"),
        ]
        
        # 执行测试
        for method, endpoint, data, expected_status, description in test_cases:
            print(f"测试: {method} {endpoint} - {description}")
            
            result = self.test_endpoint(method, endpoint, data, expected_status=expected_status)
            result["description"] = description
            
            results["test_results"].append(result)
            results["total_tests"] += 1
            
            if result["success"]:
                results["passed"] += 1
                print(f"  ✅ 通过 ({result['response_time']:.3f}s)")
            else:
                results["failed"] += 1
                print(f"  ❌ 失败 - {result.get('error', f'状态码: {result.get('status_code', 'N/A')}')}") 
            
            time.sleep(0.1)  # 避免请求过快
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print(f"  总测试数: {results['total_tests']}")
        print(f"  通过: {results['passed']} ✅")
        print(f"  失败: {results['failed']} ❌")
        print(f"  成功率: {(results['passed']/results['total_tests']*100):.1f}%")
        
        # 显示失败的测试
        failed_tests = [r for r in results["test_results"] if not r["success"]]
        if failed_tests:
            print("\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  - {test['method']} {test['endpoint']}: {test.get('error', '状态码错误')}")
        
        return results
    
    def test_specific_endpoints(self, endpoints: list) -> None:
        """测试指定的端点"""
        print(f"🎯 测试指定端点: {len(endpoints)} 个")
        print("=" * 40)
        
        for endpoint in endpoints:
            result = self.test_endpoint("GET", endpoint)
            status = "✅" if result["success"] else "❌"
            print(f"{status} GET {endpoint}")
            if not result["success"]:
                print(f"    错误: {result.get('error', '未知错误')}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="GuiXiaoxi RAG API 端点测试")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务地址")
    parser.add_argument("--endpoints", nargs="+", help="指定要测试的端点")
    
    args = parser.parse_args()
    
    tester = GuiXiaoxiAPITester(args.url)
    
    if args.endpoints:
        tester.test_specific_endpoints(args.endpoints)
    else:
        results = tester.run_all_tests()
        
        # 保存测试结果
        with open("api_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 测试结果已保存到: api_test_results.json")

if __name__ == "__main__":
    main()
