import{s as a,c as s,a as e,C as t}from"./chunk-A2AXSNBT-ZI6SIAYy.js";import{_ as i}from"./mermaid-vendor-DGPC_TDM.js";import"./chunk-RZ5BOZE2-BhCqM4LN.js";import"./feature-graph-Chuw_ipx.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";var f={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{f as diagram};
