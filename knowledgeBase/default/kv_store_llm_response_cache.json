{"hybrid:keywords:08ff0804ac2b097a0b9f1b7d00b76b6e": {"return": "{\"high_level_keywords\": [\"\\u6d4b\\u8bd5\"], \"low_level_keywords\": [\"\\u6d4b\\u8bd5\\u67e5\\u8be2\"]}", "cache_type": "keywords", "chunk_id": null, "embedding": null, "embedding_shape": null, "embedding_min": null, "embedding_max": null, "original_prompt": "测试查询", "create_time": 1753936318, "update_time": 1753936318, "_id": "hybrid:keywords:08ff0804ac2b097a0b9f1b7d00b76b6e"}, "hybrid:keywords:743e13beb01086807a301f20c3ccd4e5": {"return": "{\"high_level_keywords\": [\"Computer Science College\", \"Academic programs\"], \"low_level_keywords\": [\"Computer Science College\", \"majors\"]}", "cache_type": "keywords", "chunk_id": null, "embedding": null, "embedding_shape": null, "embedding_min": null, "embedding_max": null, "original_prompt": "计算机学院有哪些专业？", "create_time": 1753936505, "update_time": 1753936505, "_id": "hybrid:keywords:743e13beb01086807a301f20c3ccd4e5"}, "hybrid:query:743e13beb01086807a301f20c3ccd4e5": {"return": "<think>\n好的，用户问的是贵州大学计算机科学与技术学院有哪些专业。首先，我需要仔细查看提供的知识图谱和文档碎片信息，找出所有相关专业。\n\n首先看实体部分，发现有多个实体涉及专业方向。比如“软件工程技术与人工智能”是软件工程专业下的研究方向，还有“计算机网络与信息安全”作为研究生专业方向。另外，计算机科学与技术学院有本科、硕士和博士专业，比如计算机科学与技术本科专业，以及计算机科学与技术硕士点、博士点。\n\n然后看关系部分，学院与各个专业之间的关联。例如，计算机科学与技术学院包含软件工程技术与人工智能专业，管理该专业的学业数据。还有计算机网络与信息安全专业属于该学院的教学范畴。另外，学院还开设了计算机软件与理论、信息安全等专业。\n\n还需要注意专业代码，比如计算机网络与信息安全的代码是081203，而软件工程专业代码是083500。同时，学院还涉及电子信息专业硕士点（085400）和计算机技术专业学位硕士点。\n\n另外，学院还提供计算机科学与技术一级学科下的多个研究方向，如计算机软件与理论、计算机网络与信息安全、人工智能等。还有云计算与大数据技术、物联网与嵌入式软件开发等作为硕士研究生的核心研究方向。\n\n需要确认是否有遗漏的专业，比如电子信息专业硕士点和软件工程专业学位硕士点也是学院的一部分。同时，计算机科学与技术学院还负责软件工程博士研究生的培养工作。\n\n总结下来，学院的专业包括本科、硕士、博士层次，涵盖计算机科学与技术、软件工程、信息安全、人工智能、计算机网络与信息安全等多个方向，并且有多个专业代码对应不同的研究方向。\n</think>\n\n### 贵州大学计算机科学与技术学院专业介绍\n\n贵州大学计算机科学与技术学院提供多层次、多方向的专业设置，涵盖本科、硕士及博士研究生教育。以下是学院主要开设的专业方向及相关信息：\n\n---\n\n#### **1. 本科专业**\n- **计算机科学与技术本科专业**  \n  学院的国家一流本科专业和区域一流建设专业，授予工学学士学位，核心课程包括数据结构、操作系统、计算机网络、数据库原理等。  \n  [KG] 文件路径: unknown_source\n\n---\n\n#### **2. 硕士研究生专业**\n- **计算机科学与技术（081200）**  \n  一级学科硕士点，包含多个研究方向，如：  \n  - **计算机软件与理论**（081201）  \n  - **计算机网络与信息安全**（081203）  \n  - **人工智能与模式识别**  \n  - **大数据技术与互联网应用**  \n  - **云计算与大数据技术**  \n  [KG] 文件路径: unknown_source\n\n- **软件工程（083500）**  \n  专业学位硕士点，研究方向包括软件工程技术与人工智能、软件工程理论与计算理论等。  \n  [KG] 文件路径: unknown_source\n\n- **电子信息（085400）**  \n  专业学位硕士点，涵盖计算机技术、人工智能、网络与信息安全等领域。  \n  [KG] 文件路径: unknown_source\n\n---\n\n#### **3. 博士研究生专业**\n- **软件工程博士点**  \n  学院负责软件工程一级学科博士点及学位点的建设与管理，研究方向包括软件工程技术与人工智能、计算机网络与信息安全等。  \n  [KG] 文件路径: unknown_source\n\n- **计算机科学与技术博士点**  \n  涵盖理论计算机科学、密码学与信息安全、人工智能与模式识别等方向。  \n  [KG] 文件路径: unknown_source\n\n---\n\n#### **4. 其他研究方向**\n- **信息安全**  \n  学院设有信息安全专业，推免名额按毕业生人数比例分配，研究内容涉及网络安全、数据保护等。  \n  [KG] 文件路径: unknown_source\n\n- **人工智能**  \n  作为计算机科学与技术学院的重点研究方向之一，结合软件工程与人工智能技术，研究领域包括复杂系统开发、智能算法等。  \n  [KG] 文件路径: unknown_source\n\n- **物联网与嵌入式软件开发**  \n  学院研究方向之一，聚焦物联网技术及嵌入式系统开发。  \n  [KG] 文件路径: unknown_source\n\n---\n\n#### **5. 专业课程与培养特色**\n- **核心课程**  \n  包括数据结构、操作系统、计算机网络、数据库原理、算法设计与分析、高级语言程序设计等。  \n  [KG] 文件路径: unknown_source\n\n- **实践与科研**  \n  学院注重理论与实践结合，提供算力集群支持科研实验，并参与乡村振兴技术支援、大数据国际会议承办等项目。  \n  [KG] 文件路径: unknown_source\n\n---\n\n### References\n1. [KG] unknown_source  \n2. [KG] unknown_source  \n3. [KG] unknown_source  \n4. [KG] unknown_source  \n5. [KG] unknown_source", "cache_type": "query", "chunk_id": null, "embedding": null, "embedding_shape": null, "embedding_min": null, "embedding_max": null, "original_prompt": "计算机学院有哪些专业？", "create_time": 1753936527, "update_time": 1753936527, "_id": "hybrid:query:743e13beb01086807a301f20c3ccd4e5"}, "hybrid:keywords:9127ac0fddde693c2e27ec0f8914011d": {"return": "{\"high_level_keywords\": [\"Software engineering major\", \"Main courses\", \"Computer science fundamentals\", \"Engineering principles\", \"Curriculum structure\"], \"low_level_keywords\": [\"Data structures\", \"Algorithms\", \"Software design\", \"Programming languages\", \"Software testing\", \"Operating systems\", \"Database systems\", \"Project management\"]}", "cache_type": "keywords", "chunk_id": null, "embedding": null, "embedding_shape": null, "embedding_min": null, "embedding_max": null, "original_prompt": "软件工程专业的主要课程有哪些？", "create_time": 1753936531, "update_time": 1753936531, "_id": "hybrid:keywords:9127ac0fddde693c2e27ec0f8914011d"}}