#!/usr/bin/env python3
"""
GuiXiaoxi RAG 故障排除脚本
自动检测和修复常见问题
"""

import os
import json
import requests
import subprocess
import sys
from pathlib import Path

class GuiXiaoxiTroubleshooter:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.llm_url = "http://localhost:8100/v1"
        self.embedding_url = "http://localhost:8200/v1"
        self.config_file = "config.json"
        
    def print_header(self, title):
        print(f"\n{'='*60}")
        print(f"🔧 {title}")
        print(f"{'='*60}")
    
    def print_step(self, step, description):
        print(f"\n{step}. {description}")
        print("-" * 40)
    
    def check_config_file(self):
        """检查配置文件"""
        self.print_step("1", "检查配置文件")
        
        if not Path(self.config_file).exists():
            print(f"❌ 配置文件 {self.config_file} 不存在")
            return False
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ 配置文件存在")
            print(f"   LLM API: {config.get('llm', {}).get('api_base', 'N/A')}")
            print(f"   Embedding API: {config.get('embedding', {}).get('api_base', 'N/A')}")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
            return False
    
    def check_environment_variables(self):
        """检查环境变量"""
        self.print_step("2", "检查环境变量")
        
        required_vars = [
            "OPENAI_API_KEY",
            "OPENAI_API_BASE"
        ]
        
        missing_vars = []
        for var in required_vars:
            value = os.getenv(var)
            if value:
                print(f"✅ {var}: {value}")
            else:
                print(f"❌ {var}: 未设置")
                missing_vars.append(var)
        
        if missing_vars:
            print(f"\n💡 解决方案:")
            print(f"   export OPENAI_API_KEY=\"sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q\"")
            print(f"   export OPENAI_API_BASE=\"http://localhost:8100/v1\"")
            print(f"   或运行: source setup_env.sh")
            return False
        
        return True
    
    def check_llm_service(self):
        """检查LLM服务"""
        self.print_step("3", "检查LLM服务")
        
        try:
            response = requests.get(f"{self.llm_url}/models", timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ LLM服务正常 ({self.llm_url})")
                if 'data' in models and models['data']:
                    print(f"   可用模型: {[m['id'] for m in models['data']]}")
                return True
            else:
                print(f"❌ LLM服务响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ LLM服务连接失败: {e}")
            print(f"💡 请确保LLM服务在 {self.llm_url} 运行")
            return False
    
    def check_embedding_service(self):
        """检查嵌入服务"""
        self.print_step("4", "检查嵌入服务")
        
        try:
            response = requests.get(f"{self.embedding_url}/models", timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ 嵌入服务正常 ({self.embedding_url})")
                if 'data' in models and models['data']:
                    print(f"   可用模型: {[m['id'] for m in models['data']]}")
                return True
            else:
                print(f"❌ 嵌入服务响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 嵌入服务连接失败: {e}")
            print(f"💡 请确保嵌入服务在 {self.embedding_url} 运行")
            return False
    
    def check_api_service(self):
        """检查API服务"""
        self.print_step("5", "检查API服务")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                health = response.json()
                print(f"✅ API服务正常 ({self.base_url})")
                print(f"   状态: {health.get('data', {}).get('status', 'N/A')}")
                return True
            else:
                print(f"❌ API服务响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API服务连接失败: {e}")
            print(f"💡 请运行: python start_server.py")
            return False
    
    def test_query(self):
        """测试查询功能"""
        self.print_step("6", "测试查询功能")
        
        try:
            payload = {
                "query": "测试查询",
                "mode": "hybrid"
            }
            
            response = requests.post(
                f"{self.base_url}/query",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ 查询功能正常")
                # 只显示前100个字符
                result = response.text[:100] + "..." if len(response.text) > 100 else response.text
                print(f"   响应: {result}")
                return True
            else:
                print(f"❌ 查询失败: {response.status_code}")
                print(f"   错误: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"❌ 查询测试失败: {e}")
            return False
    
    def fix_environment(self):
        """修复环境变量"""
        self.print_step("修复", "设置环境变量")
        
        try:
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 设置环境变量
            llm_api_key = config.get('llm', {}).get('api_key', 'sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q')
            llm_api_base = config.get('llm', {}).get('api_base', 'http://localhost:8100/v1')
            
            os.environ['OPENAI_API_KEY'] = llm_api_key
            os.environ['OPENAI_API_BASE'] = llm_api_base
            
            print(f"✅ 环境变量已设置")
            print(f"   OPENAI_API_KEY: {llm_api_key}")
            print(f"   OPENAI_API_BASE: {llm_api_base}")
            
            return True
            
        except Exception as e:
            print(f"❌ 环境变量设置失败: {e}")
            return False
    
    def run_diagnosis(self):
        """运行完整诊断"""
        self.print_header("GuiXiaoxi RAG 故障诊断")
        
        checks = [
            ("配置文件", self.check_config_file),
            ("环境变量", self.check_environment_variables),
            ("LLM服务", self.check_llm_service),
            ("嵌入服务", self.check_embedding_service),
            ("API服务", self.check_api_service),
            ("查询功能", self.test_query),
        ]
        
        results = {}
        for name, check_func in checks:
            results[name] = check_func()
        
        # 总结
        self.print_header("诊断总结")
        passed = sum(results.values())
        total = len(results)
        
        print(f"📊 检查结果: {passed}/{total} 项通过")
        
        for name, result in results.items():
            status = "✅" if result else "❌"
            print(f"   {status} {name}")
        
        # 提供修复建议
        if not results.get("环境变量", True):
            print(f"\n🔧 自动修复建议:")
            print(f"   1. 运行: source setup_env.sh")
            print(f"   2. 或者运行: ./restart_server_with_env.sh")
            
        if not results.get("API服务", True):
            print(f"\n🔧 服务启动建议:")
            print(f"   python start_server.py")
            
        return passed == total

def main():
    troubleshooter = GuiXiaoxiTroubleshooter()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--fix":
        troubleshooter.fix_environment()
    else:
        success = troubleshooter.run_diagnosis()
        
        if not success:
            print(f"\n💡 运行 'python troubleshoot.py --fix' 尝试自动修复")

if __name__ == "__main__":
    main()
