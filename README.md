# GuiXiaoxi RAG 智能问答系统 v2.0

基于贵州大学计算机科学与技术学院的知识图谱构建的智能问答系统，采用 RAG（Retrieval-Augmented Generation）技术，支持多知识库管理、流式输出和通用问答功能。

## 🎯 项目概述

GuiXiaoxi RAG 是一个专门为贵州大学计算机科学与技术学院设计的智能知识问答系统。系统通过构建学院相关的知识图谱，结合大语言模型的生成能力，为用户提供准确、及时的信息查询服务。

### ✨ v2.0 新特性

- 📚 **多知识库管理**：支持创建和管理多个独立知识库
- 🔄 **联合查询**：可同时查询多个知识库并汇总结果
- 💬 **通用问答**：支持不依赖知识库的通用问题回答
- 🌊 **流式输出**：实时流式响应，提升用户体验
- ⚙️ **配置集中**：统一的配置管理，支持动态配置
- 🎯 **知识库分类**：按主题或用途创建专门的知识库

### 核心特性

- 🧠 **智能问答**：基于知识图谱的精准问答
- 🔍 **多模式查询**：支持局部、全局、混合等多种查询模式
- 📊 **知识图谱**：可视化的知识结构展示
- 🚀 **高性能**：异步处理，支持并发查询
- 🌐 **Web 界面**：直观的 Streamlit Web 应用
- 📱 **REST API**：完整的 API 接口支持

## 🏗️ 系统架构

ps aux | grep -E "(start_server|uvicorn|fastapi)" | grep -v grep

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 界面      │    │   REST API      │    │   RAG 引擎      │
│  (Streamlit)    │◄──►│   (FastAPI)     │◄──►│  (GuiXiaoxi)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户交互      │    │   业务逻辑      │    │   知识处理      │
│   - 问答界面    │    │   - 查询管理    │    │   - 图谱构建    │
│   - 文档管理    │    │   - 文档处理    │    │   - 向量检索    │
│   - 数据可视化  │    │   - 结果聚合    │    │   - 语义理解    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.10-3.12
- Conda 环境管理器
- 16GB+ 内存推荐
- 50GB+ 存储空间

### 安装部署

```bash
# 1. 创建环境
conda create -n lightrag312 python=3.12 -y
conda activate lightrag312

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置系统
# 编辑 config.json 设置 API 地址和密钥

# 4. 启动服务
python start_streamlit.py  # 启动完整服务（API + Web）
# 或者分别启动
python start_server.py     # 只启动 API 服务
```

### 访问系统

- **Web 界面**: http://localhost:8501
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 🆕 v2.0 功能详解

### 📚 多知识库管理

系统支持创建和管理多个独立的知识库，每个知识库可以专注于特定的主题或用途：

```json
{
  "knowledge_bases": {
    "cs_college": {
      "name": "计算机学院",
      "description": "贵州大学计算机科学与技术学院知识库",
      "working_dir": "./knowledgeBase/cs_college",
      "enabled": true
    },
    "general": {
      "name": "通用知识",
      "description": "通用知识库",
      "working_dir": "./knowledgeBase/general",
      "enabled": true
    }
  }
}
```

### 🔄 联合查询

支持同时查询多个知识库，系统会并行处理查询请求并汇总结果：

```python
# API 调用示例
{
  "query": "计算机学院有哪些专业？",
  "kb_ids": ["cs_college", "general"],
  "mode": "hybrid",
  "stream": true
}
```

### 💬 通用问答

支持不依赖知识库的通用问题回答，直接使用大语言模型的知识：

```python
# 通用问答 API
POST /query/general
{
  "query": "什么是人工智能？",
  "stream": true
}
```

### 🌊 流式输出

支持实时流式响应，提升用户体验：

- **Web 界面**：实时显示回答内容
- **API 接口**：Server-Sent Events (SSE) 流式响应
- **可配置**：可选择启用或禁用流式输出

## 📚 API 端点详解

### 🔍 查询相关端点

#### `POST /query` - 基础查询
智能查询接口，支持多知识库查询。

**请求参数:**
```json
{
  "query": "计算机科学与技术学院的院长是谁？",
  "kb_ids": ["cs_college", "general"],  // 可选，为空则查询所有启用的知识库
  "mode": "hybrid",  // local, global, hybrid, naive, mix, bypass
  "stream": false  // 是否启用流式输出
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "response": "计算机科学与技术学院的院长是秦永彬...",
    "mode": "hybrid",
    "knowledge_bases": ["cs_college", "general"],
    "timestamp": "2025-07-31T12:34:56"
  }
}
```

#### `POST /query/advanced` - 高级查询
支持GuiXiaoxi的所有高级查询参数。

**请求参数:**
```json
{
  "query": "人工智能的发展历程",
  "mode": "hybrid",
  "knowledge_base_ids": ["cs_college"],
  "top_k": 10,
  "chunk_top_k": 20,
  "response_type": "Bullet Points",
  "conversation_history": [
    {"role": "user", "content": "什么是人工智能？"},
    {"role": "assistant", "content": "人工智能是..."}
  ],
  "only_need_context": false,
  "only_need_prompt": false
}
```

#### `POST /query/stream` - 流式查询
实时流式返回查询结果，支持Server-Sent Events。

**请求参数:**
```json
{
  "query": "介绍计算机学院的专业设置",
  "mode": "hybrid",
  "enable_stream": true
}
```

**响应格式 (NDJSON):**
```json
{"type": "start", "message": "查询开始", "timestamp": "2025-07-31T12:34:56"}
{"type": "chunk", "content": "计算机科学与技术学院", "timestamp": "2025-07-31T12:34:57"}
{"type": "chunk", "content": "设有多个专业...", "timestamp": "2025-07-31T12:34:58"}
{"type": "end", "message": "查询完成", "timestamp": "2025-07-31T12:35:00"}
```

#### `POST /query/batch` - 批量查询
支持一次性处理多个查询请求。

**请求参数:**
```json
{
  "queries": [
    "计算机学院有哪些专业？",
    "软件工程专业的主要课程有哪些？"
  ],
  "mode": "hybrid"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "批量查询成功",
  "data": {
    "total": 2,
    "processed": 2,
    "results": [
      "计算机科学与技术学院设有以下专业...",
      "软件工程专业的主要课程包括..."
    ]
  }
}
```

#### `POST /query/general` - 通用问答
不依赖知识库的通用问题回答。

**请求参数:**
```json
{
  "query": "什么是人工智能？",
  "stream": false
}
```

#### `GET /query/modes` - 查询模式
获取支持的查询模式列表和说明。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "modes": [
      {
        "name": "local",
        "description": "本地实体查询，基于实体相似性检索",
        "use_case": "适用于查询特定实体的详细信息"
      },
      {
        "name": "global",
        "description": "全局关系查询，基于关系图谱检索",
        "use_case": "适用于查询实体间的关系和连接"
      },
      {
        "name": "hybrid",
        "description": "混合查询，结合本地和全局信息",
        "use_case": "适用于大多数复杂查询场景"
      }
    ]
  }
}
```

#### `GET /query/templates` - 查询模板
获取预定义的查询模板，帮助用户构建查询。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "templates": {
      "学院信息": {
        "template": "请介绍计算机科学与技术学院的基本情况",
        "description": "获取学院的基本信息、历史沿革等",
        "category": "学院概况"
      },
      "专业查询": {
        "template": "请介绍{专业名称}专业的培养目标和课程设置",
        "description": "查询特定专业的详细信息",
        "category": "专业信息",
        "parameters": ["专业名称"]
      }
    }
  }
}
```

#### `GET /query/history` - 查询历史
获取查询历史记录。

**查询参数:**
- `limit`: 返回记录数量限制（默认50）

#### `DELETE /query/history` - 清空历史
清空所有查询历史记录。

### 📚 知识库管理端点

#### `GET /knowledge-bases` - 列出知识库
获取所有知识库的列表和状态。

**响应示例:**
```json
{
  "knowledge_bases": {
    "cs_college": {
      "name": "计算机学院",
      "description": "贵州大学计算机科学与技术学院知识库",
      "enabled": true,
      "working_dir": "./knowledgeBase/cs_college",
      "status": "loaded"
    },
    "general": {
      "name": "通用知识",
      "description": "通用知识库",
      "enabled": true,
      "working_dir": "./knowledgeBase/general",
      "status": "loaded"
    }
  }
}
```

#### `GET /knowledge-bases/{kb_id}` - 获取知识库信息
获取指定知识库的详细信息。

#### `POST /knowledge-bases/{kb_id}` - 创建知识库
创建新的知识库。

**请求参数:**
```json
{
  "name": "新知识库",
  "description": "知识库描述",
  "working_dir": "./knowledgeBase/new_kb"
}
```

#### `DELETE /knowledge-bases/{kb_id}` - 删除知识库
删除指定的知识库。

#### `POST /knowledge-bases/{kb_id}/enable` - 启用知识库
启用指定的知识库。

#### `POST /knowledge-bases/{kb_id}/disable` - 禁用知识库
禁用指定的知识库。

### 📄 文档管理端点

#### `POST /insert/text` - 插入文本
直接插入文本内容到知识库。

**请求参数:**
```json
{
  "text": "要插入的文本内容",
  "kb_id": "cs_college",
  "description": "文档描述"
}
```

#### `POST /insert/file/{kb_id}` - 文件上传
上传文件到指定知识库。

**请求参数 (multipart/form-data):**
- `file`: 上传的文件
- `description`: 文件描述（可选）

**支持的文件格式:**
- 文档：`.txt`, `.md`, `.pdf`, `.docx`, `.rtf`, `.odt`
- 数据：`.json`, `.xml`, `.csv`, `.yaml`, `.yml`
- 代码：`.py`, `.js`, `.html`, `.css`, `.sql`
- 其他：`.log`, `.conf`, `.ini`, `.properties`

#### `POST /insert/directory/{kb_id}` - 目录导入
从指定目录批量导入文档。

**请求参数 (form-data):**
```
directory_path: /path/to/documents
recursive: true
file_extensions: .txt,.md,.pdf
description: 批量导入的文档
```

#### `GET /insert/supported-formats` - 支持格式
获取系统支持的文件格式列表。

### 📊 图数据端点

#### `GET /graph/stats` - 图统计信息
获取知识图谱的统计信息。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total_nodes": 1250,
    "total_edges": 3420,
    "loaded_knowledge_bases": 2,
    "knowledge_bases": {
      "cs_college": {
        "nodes": 800,
        "edges": 2100,
        "status": "loaded"
      },
      "general": {
        "nodes": 450,
        "edges": 1320,
        "status": "loaded"
      }
    }
  }
}
```

#### `GET /graph/export` - 导出图数据
导出完整的知识图谱数据。

**查询参数:**
- `kb_id`: 指定知识库ID（可选，为空则导出所有）
- `format`: 导出格式，支持 `json`, `graphml`（默认json）
- `limit`: 限制导出的节点数量（可选）

**响应示例:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "entity_1",
        "type": "Person",
        "properties": {
          "name": "秦永彬",
          "title": "院长",
          "department": "计算机科学与技术学院"
        }
      }
    ],
    "edges": [
      {
        "source": "entity_1",
        "target": "entity_2",
        "type": "WORKS_AT",
        "properties": {
          "since": "2020"
        }
      }
    ]
  }
}
```

### ⚙️ 系统管理端点

#### `GET /health` - 健康检查
检查服务运行状态。

**响应示例:**
```json
{
  "success": true,
  "message": "服务运行正常",
  "data": {
    "status": "healthy",
    "timestamp": **********.123,
    "insert_manager": true,
    "query_manager": true
  }
}
```

#### `GET /config` - 获取配置
获取当前系统配置信息。

**响应示例:**
```json
{
  "llm": {
    "model": "qwen14b",
    "stream": true,
    "temperature": 0.7
  },
  "embedding": {
    "model": "embedding_qwen",
    "dimension": 1536
  },
  "rag": {
    "working_dir": "./output",
    "max_token_size": 2048
  }
}
```

#### `GET /stats` - 系统统计
获取系统整体统计信息。

**响应示例:**
```json
{
  "knowledge_bases": {
    "cs_college": {
      "nodes": 800,
      "edges": 2100,
      "status": "loaded"
    }
  },
  "query_history_count": 156
}
```

## 📚 文档导航

详细文档请查看 `docs/` 目录：

- 📖 [安装部署指南](docs/INSTALLATION.md) - 详细的安装和配置说明
- 🎨 [Web 界面使用指南](docs/STREAMLIT_GUIDE.md) - Streamlit 应用使用教程
- 🔧 [API 参考文档](docs/API_REFERENCE.md) - 完整的 API 接口说明
- 🚨 [故障排除指南](docs/TROUBLESHOOTING.md) - 常见问题解决方案
- 📊 [部署状态报告](docs/DEPLOYMENT_STATUS.md) - 系统部署和测试结果
- 📋 [项目总结](docs/PROJECT_SUMMARY.md) - 项目概况和技术细节

## 🎯 使用场景

### 学院信息查询
- 学院基本信息、组织架构
- 专业设置、课程安排
- 师资队伍、研究方向

### 学术资源检索
- 科研项目、学术成果
- 实验室设备、研究平台
- 合作交流、学术活动

### 教学服务支持
- 课程信息、教学计划
- 学位要求、毕业条件
- 实习实践、就业指导

## 🛠️ 技术栈

### 后端技术
- **Web 框架**: FastAPI 0.104+
- **异步运行**: Uvicorn
- **RAG 引擎**: GuiXiaoxiRAG (基于 LightRAG)
- **语言模型**: Qwen14B
- **嵌入模型**: embedding_qwen

### 前端技术
- **Web 框架**: Streamlit 1.47+
- **数据处理**: Pandas 2.3+
- **可视化**: Plotly 6.2+
- **文件处理**: OpenPyXL 3.1+

### 数据存储
- **图数据**: JSON + GraphML
- **向量数据**: 自定义向量数据库
- **文档存储**: 文件系统
- **缓存**: 内存 + 文件缓存

## 📞 联系我们

- 📧 **邮箱**: <EMAIL>
- 🌐 **官网**: https://cs.gzu.edu.cn
- 💬 **讨论**: GitHub Discussions
- 🐛 **问题**: GitHub Issues

## 🙏 致谢

感谢以下项目和团队的支持：

- [LightRAG](https://github.com/HKUDS/LightRAG) - 原始 RAG 框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代 Web 框架
- [Streamlit](https://streamlit.io/) - 数据应用框架
- [Qwen](https://github.com/QwenLM/Qwen) - 大语言模型
- 贵州大学计算机科学与技术学院 - 数据和支持

## 📊 API 端点总结

### 查询接口 (7个)
- `POST /query` - 基础查询
- `POST /query/advanced` - 高级查询
- `POST /query/stream` - 流式查询
- `POST /query/batch` - 批量查询
- `POST /query/general` - 通用问答
- `GET /query/modes` - 查询模式
- `GET /query/templates` - 查询模板
- `GET /query/history` - 查询历史
- `DELETE /query/history` - 清空历史

### 知识库管理 (6个)
- `GET /knowledge-bases` - 列出知识库
- `GET /knowledge-bases/{kb_id}` - 获取知识库信息
- `POST /knowledge-bases/{kb_id}` - 创建知识库
- `DELETE /knowledge-bases/{kb_id}` - 删除知识库
- `POST /knowledge-bases/{kb_id}/enable` - 启用知识库
- `POST /knowledge-bases/{kb_id}/disable` - 禁用知识库

### 文档管理 (4个)
- `POST /insert/text` - 插入文本
- `POST /insert/file/{kb_id}` - 文件上传
- `POST /insert/directory/{kb_id}` - 目录导入
- `GET /insert/supported-formats` - 支持格式

### 图数据管理 (2个)
- `GET /graph/stats` - 图统计信息
- `GET /graph/export` - 导出图数据

### 系统管理 (3个)
- `GET /health` - 健康检查
- `GET /config` - 获取配置
- `GET /stats` - 系统统计

**总计：22个API端点**

---

**GuiXiaoxi RAG** - 让知识触手可及 🚀



## 💡 使用示例

### 1. Python 客户端示例

```python
import requests
import json
import asyncio
import aiohttp

class GuiXiaoxiClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def health_check(self):
        """健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        return response.json()

    def query(self, question, kb_ids=None, mode="hybrid", stream=False):
        """基础查询"""
        payload = {
            "query": question,
            "mode": mode,
            "stream": stream
        }
        if kb_ids:
            payload["kb_ids"] = kb_ids

        response = self.session.post(f"{self.base_url}/query", json=payload)
        return response.json()

    def advanced_query(self, question, **kwargs):
        """高级查询"""
        payload = {"query": question, **kwargs}
        response = self.session.post(f"{self.base_url}/query/advanced", json=payload)
        return response.json()

    def stream_query(self, question, **kwargs):
        """流式查询"""
        payload = {"query": question, **kwargs}
        response = self.session.post(f"{self.base_url}/query/stream", json=payload, stream=True)

        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    yield data
                except json.JSONDecodeError:
                    continue

    def insert_text(self, text, kb_id, description=""):
        """插入文本"""
        payload = {
            "text": text,
            "kb_id": kb_id,
            "description": description
        }
        response = self.session.post(f"{self.base_url}/insert/text", json=payload)
        return response.json()

    def upload_file(self, file_path, kb_id, description=""):
        """上传文件"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'description': description}
            response = self.session.post(
                f"{self.base_url}/insert/file/{kb_id}",
                files=files,
                data=data
            )
        return response.json()

    def get_knowledge_bases(self):
        """获取知识库列表"""
        response = self.session.get(f"{self.base_url}/knowledge-bases")
        return response.json()

    def get_graph_stats(self):
        """获取图统计信息"""
        response = self.session.get(f"{self.base_url}/graph/stats")
        return response.json()

# 使用示例
if __name__ == "__main__":
    client = GuiXiaoxiClient()

    # 健康检查
    health = client.health_check()
    print(f"服务状态: {health['message']}")

    # 基础查询
    result = client.query("计算机科学与技术学院的院长是谁？")
    if result['success']:
        print(f"查询结果: {result['data']['response']}")

    # 高级查询
    advanced_result = client.advanced_query(
        "人工智能的发展历程",
        mode="hybrid",
        response_type="Bullet Points",
        top_k=10
    )

    # 流式查询
    print("流式查询结果:")
    for chunk in client.stream_query("介绍计算机学院的专业设置"):
        if chunk.get('type') == 'chunk':
            print(chunk['content'], end='', flush=True)

    # 获取统计信息
    stats = client.get_graph_stats()
    if stats['success']:
        data = stats['data']
        print(f"\n知识图谱统计: 节点 {data['total_nodes']}, 边 {data['total_edges']}")
```

### 2. cURL 示例

```bash
# 健康检查
curl -X GET "http://localhost:8000/health"

# 基础查询
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "计算机学院院长是谁",
    "mode": "hybrid",
    "kb_ids": ["cs_college"]
  }'

# 高级查询
curl -X POST "http://localhost:8000/query/advanced" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "陈艳平是谁",
    "mode": "mix",
    "response_type": "Bullet Points",
    "top_k": 10,
    "knowledge_base_ids": ["cs_college"]
  }'

# 流式查询
curl -X POST "http://localhost:8000/query/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "介绍计算机学院的专业设置",
    "mode": "hybrid"
  }'

# 批量查询
curl -X POST "http://localhost:8000/query/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "queries": [
      "计算机学院有哪些专业？",
      "软件工程专业的主要课程有哪些？"
    ],
    "mode": "hybrid"
  }'

# 通用问答
curl -X POST "http://localhost:8000/query/general" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能？",
    "stream": false
  }'

# 插入文本
curl -X POST "http://localhost:8000/insert/text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是要插入的文本内容",
    "kb_id": "cs_college",
    "description": "测试文档"
  }'

# 上传文件
curl -X POST "http://localhost:8000/insert/file/cs_college" \
  -F "file=@document.pdf" \
  -F "description=重要文档"

# 获取知识库列表
curl -X GET "http://localhost:8000/knowledge-bases"

# 获取图统计
curl -X GET "http://localhost:8000/graph/stats"

# 导出图数据
curl -X GET "http://localhost:8000/graph/export?kb_id=cs_college&format=json"
```

### 3. JavaScript/Node.js 示例

```javascript
const axios = require('axios');

class GuiXiaoxiClient {
  constructor(baseURL = 'http://localhost:8000') {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      console.error('健康检查失败:', error.message);
      return null;
    }
  }

  async query(question, options = {}) {
    try {
      const payload = {
        query: question,
        mode: options.mode || 'hybrid',
        kb_ids: options.kb_ids,
        stream: options.stream || false
      };

      const response = await this.client.post('/query', payload);
      return response.data;
    } catch (error) {
      console.error('查询失败:', error.message);
      return null;
    }
  }

  async advancedQuery(question, options = {}) {
    try {
      const payload = { query: question, ...options };
      const response = await this.client.post('/query/advanced', payload);
      return response.data;
    } catch (error) {
      console.error('高级查询失败:', error.message);
      return null;
    }
  }

  async batchQuery(questions, mode = 'hybrid') {
    try {
      const payload = { queries: questions, mode };
      const response = await this.client.post('/query/batch', payload);
      return response.data;
    } catch (error) {
      console.error('批量查询失败:', error.message);
      return null;
    }
  }

  async generalQA(question, stream = false) {
    try {
      const payload = { query: question, stream };
      const response = await this.client.post('/query/general', payload);
      return response.data;
    } catch (error) {
      console.error('通用问答失败:', error.message);
      return null;
    }
  }

  async insertText(text, kbId, description = '') {
    try {
      const payload = { text, kb_id: kbId, description };
      const response = await this.client.post('/insert/text', payload);
      return response.data;
    } catch (error) {
      console.error('插入文本失败:', error.message);
      return null;
    }
  }

  async getKnowledgeBases() {
    try {
      const response = await this.client.get('/knowledge-bases');
      return response.data;
    } catch (error) {
      console.error('获取知识库列表失败:', error.message);
      return null;
    }
  }

  async getGraphStats() {
    try {
      const response = await this.client.get('/graph/stats');
      return response.data;
    } catch (error) {
      console.error('获取图统计失败:', error.message);
      return null;
    }
  }
}

// 使用示例
(async () => {
  const client = new GuiXiaoxiClient();

  // 健康检查
  const health = await client.healthCheck();
  console.log('服务状态:', health?.message);

  // 基础查询
  const result = await client.query('计算机科学与技术学院的院长是谁？', {
    mode: 'hybrid',
    kb_ids: ['cs_college']
  });

  if (result?.success) {
    console.log('查询结果:', result.data.response);
  }

  // 高级查询
  const advancedResult = await client.advancedQuery('人工智能的发展历程', {
    mode: 'hybrid',
    response_type: 'Bullet Points',
    top_k: 10,
    knowledge_base_ids: ['cs_college']
  });

  // 批量查询
  const batchResult = await client.batchQuery([
    '计算机学院有哪些专业？',
    '软件工程专业的主要课程有哪些？'
  ]);

  // 通用问答
  const generalResult = await client.generalQA('什么是人工智能？');

  // 获取统计信息
  const stats = await client.getGraphStats();
  if (stats?.success) {
    const data = stats.data;
    console.log(`知识图谱统计: 节点 ${data.total_nodes}, 边 ${data.total_edges}`);
  }
})();
```

## 📋 配置说明

### 环境变量

可以通过环境变量覆盖配置文件中的设置：

```bash
# LLM 服务配置
export OPENAI_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
export OPENAI_API_BASE="http://localhost:8100/v1"
export LLM_MODEL="qwen14b"

# 嵌入服务配置
export EMBEDDING_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
export EMBEDDING_API_BASE="http://localhost:8200/v1"
export EMBEDDING_MODEL="embedding_qwen"

# GuiXiaoxi 特定配置
export GUIXIAOXI_LLM_API_BASE="http://localhost:8100/v1"
export GUIXIAOXI_EMBEDDING_API_BASE="http://localhost:8200/v1"
export GUIXIAOXI_WORKING_DIR="./custom_output"
export GUIXIAOXI_API_PORT=8080
```

**重要提示**：由于系统使用OpenAI兼容的API调用方式，必须设置 `OPENAI_API_KEY` 环境变量，即使使用的是本地LLM服务。

### 配置文件结构 (config.json)

```json
{
  "llm": {
    "model": "qwen14b",
    "api_base": "http://localhost:8100/v1",
    "api_key": "your-api-key",
    "temperature": 0.7,
    "max_tokens": 2048,
    "stream": true
  },
  "embedding": {
    "model": "embedding_qwen",
    "api_base": "http://localhost:8200/v1",
    "api_key": "your-embedding-key",
    "dimension": 1536,
    "batch_size": 32
  },
  "rag": {
    "working_dir": "./output",
    "max_token_size": 2048,
    "chunk_size": 512,
    "chunk_overlap": 50,
    "top_k": 10,
    "enable_cache": true
  },
  "api": {
    "host": "0.0.0.0",
    "port": 8000,
    "workers": 1,
    "cors_origins": ["*"],
    "max_upload_size": 52428800
  },
  "knowledge_bases": {
    "cs_college": {
      "name": "计算机学院",
      "description": "贵州大学计算机科学与技术学院知识库",
      "working_dir": "./knowledgeBase/cs_college",
      "enabled": true
    },
    "general": {
      "name": "通用知识",
      "description": "通用知识库",
      "working_dir": "./knowledgeBase/general",
      "enabled": true
    }
  },
  "logging": {
    "level": "INFO",
    "file_path": "logs/guixiaoxi.log",
    "max_file_size": "10MB",
    "backup_count": 5
  }
}
```

### 配置项说明

#### LLM 配置
- `model`: 使用的大语言模型名称
- `api_base`: LLM API 服务地址
- `api_key`: API 密钥
- `temperature`: 生成温度（0-1）
- `max_tokens`: 最大生成token数
- `stream`: 是否启用流式输出

#### 嵌入模型配置
- `model`: 嵌入模型名称
- `api_base`: 嵌入API服务地址
- `dimension`: 向量维度
- `batch_size`: 批处理大小

#### RAG 系统配置
- `working_dir`: 工作目录
- `max_token_size`: 最大token大小
- `chunk_size`: 文本块大小
- `chunk_overlap`: 文本块重叠
- `top_k`: 检索的top-k数量
- `enable_cache`: 是否启用缓存

#### API 服务配置
- `host`: 服务监听地址
- `port`: 服务端口
- `workers`: 工作进程数
- `cors_origins`: CORS允许的源
- `max_upload_size`: 最大上传文件大小

#### 知识库配置
- `name`: 知识库显示名称
- `description`: 知识库描述
- `working_dir`: 知识库工作目录
- `enabled`: 是否启用

## 🚀 部署指南

### 开发环境部署

```bash
# 1. 激活 conda 环境
conda activate lightrag312

# 2. 启动服务
python start_server.py --host 0.0.0.0 --port 8000

# 3. 验证服务
python comprehensive_test.py
```

### 生产环境部署

#### 方式一：使用 Gunicorn

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动服务（4个工作进程）
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile - \
  --log-level info
```

#### 方式二：使用 Systemd 服务

创建服务文件 `/etc/systemd/system/guixiaoxi-rag.service`:

```ini
[Unit]
Description=GuiXiaoxi RAG API Service
After=network.target

[Service]
Type=exec
User=your-user
Group=your-group
WorkingDirectory=/path/to/gui_xiaoxi
Environment=PATH=/root/miniconda3/envs/lightrag312/bin
ExecStart=/root/miniconda3/envs/lightrag312/bin/python start_server.py --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable guixiaoxi-rag
sudo systemctl start guixiaoxi-rag
sudo systemctl status guixiaoxi-rag
```

#### 方式三：使用 Docker

创建 `Dockerfile`:

```dockerfile
FROM python:3.12-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000"]
```

构建和运行：
```bash
# 构建镜像
docker build -t guixiaoxi-rag:latest .

# 运行容器
docker run -d \
  --name guixiaoxi-rag \
  -p 8000:8000 \
  -v $(pwd)/output:/app/output \
  -v $(pwd)/config.json:/app/config.json \
  guixiaoxi-rag:latest
```

### 反向代理配置

#### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 文件上传大小限制
        client_max_body_size 100M;
    }

    # 静态文件缓存
    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_cache_valid 200 1h;
    }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败

**问题**: `ModuleNotFoundError: No module named 'guixiaoxi'`

**解决方案**:
```bash
# 确保在正确的 conda 环境中
conda activate lightrag312

# 检查 Python 路径
python -c "import sys; print(sys.path)"

# 确保当前目录在 Python 路径中
export PYTHONPATH=$PYTHONPATH:$(pwd)
```

#### 1.1 OPENAI_API_KEY 错误

**问题**: `查询失败: 'OPENAI_API_KEY'`

**原因**: GuiXiaoxi使用OpenAI兼容的API调用方式，需要设置环境变量

**解决方案**:
```bash
# 方法1：设置环境变量
export OPENAI_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
export OPENAI_API_BASE="http://localhost:8100/v1"

# 方法2：使用提供的脚本
source setup_env.sh

# 方法3：重启服务器
./restart_server_with_env.sh
```

#### 1.2 嵌入模型错误

**问题**: `This model does not appear to be an embedding model`

**原因**: 嵌入服务配置问题或API调用错误

**解决方案**:
```bash
# 检查嵌入服务状态
curl http://localhost:8200/v1/models

# 确保嵌入服务正确启动
# 嵌入服务应该使用 --is-embedding 参数启动

# 检查配置文件中的嵌入API地址
grep -A 5 "embedding" config.json
```

#### 2. 查询超时

**问题**: 查询请求超时或响应缓慢

**解决方案**:
- 检查 LLM 服务是否正常运行
- 增加请求超时时间
- 检查网络连接
- 监控系统资源使用情况

#### 3. 内存不足

**问题**: 处理大文档时内存不足

**解决方案**:
```bash
# 增加系统内存限制
ulimit -v unlimited

# 调整 Python 内存管理
export PYTHONMALLOC=malloc

# 分批处理大文档
# 在 config.json 中调整 max_token_size
```

#### 4. 图数据导出失败

**问题**: 图数据文件不存在或损坏

**解决方案**:
```bash
# 检查 output 目录
ls -la output/

# 重新构建知识图谱
curl -X DELETE "http://localhost:8000/insert/clear"
curl -X POST "http://localhost:8000/insert/directory" -d "directory_name=cs_college_data"
```

### 性能优化

#### 1. 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

#### 2. 应用级优化

在 `config.json` 中调整参数:

```json
{
  "api": {
    "workers": 4,
    "max_upload_size": 52428800
  },
  "rag": {
    "max_token_size": 2048,
    "embedding_dim": 1536
  }
}
```

#### 3. 数据库优化

```bash
# 定期清理临时文件
find temp_uploads/ -type f -mtime +1 -delete

# 压缩图数据文件
gzip output/graph_data.json
```

### 监控和日志

#### 1. 启用详细日志

```json
{
  "logging": {
    "level": "DEBUG",
    "file_path": "logs/guixiaoxi.log"
  }
}
```

#### 2. 监控脚本

创建 `monitor.py`:

```python
import requests
import time
import logging

def monitor_service():
    while True:
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                logging.info("Service is healthy")
            else:
                logging.warning(f"Service returned {response.status_code}")
        except Exception as e:
            logging.error(f"Service check failed: {e}")

        time.sleep(30)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    monitor_service()
```

## 🏗️ 开发说明

### 项目结构

```
gui_xiaoxi/
├── app.py                    # FastAPI 应用主文件
├── config.py                 # 配置管理模块
├── middleware.py             # 中间件集合
├── insert.py                 # 文档插入模块
├── query.py                  # 查询处理模块
├── start_server.py           # 服务启动脚本
├── comprehensive_test.py     # 综合测试脚本
├── test_api.py              # API 测试脚本
├── config.json              # 配置文件
├── requirements.txt         # Python 依赖
├── README.md               # 项目文档
├── output/                 # 知识图谱数据目录
├── temp_uploads/           # 临时上传目录
├── logs/                   # 日志目录
└── guixiaoxi/             # 核心 RAG 库
    ├── __init__.py
    ├── guixiaoxi_rag.py   # 主要 RAG 类
    ├── constants.py       # 常量定义
    ├── utils.py          # 工具函数
    ├── llm/              # LLM 接口
    └── kg/               # 知识图谱模块
```

### 代码重构说明

1. **重命名**: 将 `lightrag` 重命名为 `guixiaoxi`，`LightRAG` 类重命名为 `GuiXiaoxiRAG`
2. **模块化**: 将功能拆分为独立的模块，提高代码可维护性
3. **配置管理**: 实现统一的配置管理系统，支持文件和环境变量
4. **错误处理**: 完善的异常处理和日志记录系统
5. **API 设计**: RESTful API 设计，支持异步操作和批量处理
6. **中间件**: 添加安全、日志、性能监控等中间件
7. **测试**: 提供完整的测试脚本和示例

### 开发环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd gui_xiaoxi

# 2. 创建 conda 环境
conda create -n lightrag312 python=3.12
conda activate lightrag312

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
export GUIXIAOXI_LLM_API_BASE="http://localhost:8100/v1"
export GUIXIAOXI_EMBEDDING_API_BASE="http://localhost:8200/v1"

# 5. 运行测试
python comprehensive_test.py
```

## 📄 许可证

本项目基于原 LightRAG 项目进行重构和优化，遵循相同的开源许可证。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

### 提交规范

- 使用清晰的提交信息
- 遵循现有的代码风格
- 添加必要的测试
- 更新相关文档

## 📞 技术支持

如有问题，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者
- 查看项目 Wiki 获取更多信息
