# GuiXiaoxi RAG 故障排除指南

## 🚨 常见问题快速解决

### 1. 服务启动问题

#### 问题：API 服务无法启动
**症状**：
- 运行 `python app.py` 后没有输出
- 访问 http://localhost:8000 连接被拒绝

**解决方案**：
```bash
# 1. 检查端口占用
netstat -tlnp | grep :8000
# 如果端口被占用，杀死占用进程
kill -9 <pid>

# 2. 检查 Python 环境
conda activate lightrag312
python --version  # 应该是 3.10-3.12

# 3. 检查依赖安装
python -c "import fastapi, uvicorn; print('Dependencies OK')"

# 4. 查看详细错误
python app.py 2>&1 | tee app.log
```

#### 问题：Streamlit 应用启动失败
**症状**：
- `streamlit run` 命令报错
- 浏览器无法访问 http://localhost:8501

**解决方案**：
```bash
# 1. 检查 Streamlit 安装
streamlit --version

# 2. 重新安装 Streamlit
pip uninstall streamlit
pip install streamlit>=1.28.0

# 3. 清除缓存
streamlit cache clear

# 4. 使用详细模式启动
streamlit run streamlit_app.py --logger.level debug
```

### 2. 查询相关问题

#### 问题：Token 超出限制错误
**症状**：
```
Error code: 400 - The input (42974 tokens) is longer than the model's context length (40960 tokens)
```

**解决方案**：
```bash
# 1. 检查配置文件中的 token 设置
cat config.json | grep -A 5 "llm"

# 2. 使用更保守的查询模式
# 避免使用 "mix" 模式，改用 "local" 或 "global"

# 3. 分解复杂查询
# 将一个复杂问题分解为多个简单问题
```

**配置优化**：
```json
{
  "llm": {
    "max_token_size": 2048,
    "max_tokens": 4096
  }
}
```

#### 问题：查询响应时间过长
**症状**：
- 查询超过 30 秒没有响应
- 浏览器显示超时错误

**解决方案**：
```bash
# 1. 检查系统资源
htop  # 查看 CPU 和内存使用
df -h  # 查看磁盘空间

# 2. 优化查询参数
# 在 API 请求中使用更具体的查询模式

# 3. 检查网络连接
curl -I http://localhost:8100/v1/models  # 检查 LLM 服务
curl -I http://localhost:8200/v1/models  # 检查嵌入服务
```

### 3. 文档管理问题

#### 问题：文件上传失败
**症状**：
- 上传文件时出现 413 错误
- 文件处理中断

**解决方案**：
```bash
# 1. 检查文件大小限制
grep "max_upload_size" config.json

# 2. 增加上传限制
# 在 config.json 中修改：
"max_upload_size": 209715200  # 200MB

# 3. 检查磁盘空间
df -h ./temp_uploads
df -h ./output

# 4. 清理临时文件
rm -rf temp_uploads/*
```

#### 问题：目录导入失败
**症状**：
- 提示目录不存在
- 文件处理出错

**解决方案**：
```bash
# 1. 检查目录路径
ls -la files/
ls -la files/cs_college_data/

# 2. 检查文件权限
chmod -R 755 files/

# 3. 检查文件格式
file files/cs_college_data/*

# 4. 手动测试文件处理
python -c "
import docx2txt
print(docx2txt.process('files/cs_college_data/test.docx'))
"
```

### 4. 知识图谱问题

#### 问题：图数据为空或不完整
**症状**：
- 节点数为 0
- 图统计显示异常

**解决方案**：
```bash
# 1. 检查输出目录
ls -la output/
cat output/graph_data.json | head -20

# 2. 重新构建知识图谱
rm -rf output/*
# 重新导入数据

# 3. 检查日志
tail -f logs/guixiaoxi.log | grep -i graph

# 4. 验证数据文件
python -c "
import json
with open('output/graph_data.json', 'r') as f:
    data = json.load(f)
    print(f'Nodes: {len(data.get(\"nodes\", []))}')
    print(f'Edges: {len(data.get(\"edges\", []))}')
"
```

### 5. 性能问题

#### 问题：系统响应缓慢
**症状**：
- 页面加载时间过长
- API 响应延迟

**诊断步骤**：
```bash
# 1. 系统资源监控
top -p $(pgrep -f "python.*app.py")
iostat -x 1 5

# 2. 网络连接检查
netstat -an | grep :8000
ss -tuln | grep :8501

# 3. 日志分析
tail -f logs/guixiaoxi.log | grep -E "(ERROR|WARNING|SLOW)"

# 4. 数据库性能
ls -lh output/*.json
du -sh output/
```

**优化方案**：
```bash
# 1. 增加系统资源
# 添加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 2. 优化配置
# 在 config.json 中调整：
{
  "api": {
    "workers": 2,
    "max_connections": 100
  }
}

# 3. 清理缓存
rm -rf __pycache__/
rm -rf .streamlit/
```

### 6. 依赖问题

#### 问题：模块导入错误
**症状**：
```
ModuleNotFoundError: No module named 'xxx'
```

**解决方案**：
```bash
# 1. 检查环境激活
conda info --envs
conda activate lightrag312

# 2. 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 3. 检查 Python 路径
python -c "import sys; print('\n'.join(sys.path))"

# 4. 手动安装缺失模块
pip install <missing-module>
```

#### 问题：版本冲突
**症状**：
- 依赖版本不兼容警告
- 功能异常

**解决方案**：
```bash
# 1. 检查版本冲突
pip check

# 2. 创建新环境
conda create -n lightrag312_new python=3.12 -y
conda activate lightrag312_new
pip install -r requirements.txt

# 3. 固定版本
pip freeze > requirements_fixed.txt
```

## 🔧 调试工具

### 1. 日志分析工具

```bash
# 实时监控日志
tail -f logs/guixiaoxi.log

# 过滤错误日志
grep -i error logs/guixiaoxi.log

# 统计错误类型
grep -i error logs/guixiaoxi.log | cut -d'-' -f4 | sort | uniq -c

# 查看最近的查询
grep "执行查询" logs/guixiaoxi.log | tail -10
```

### 2. 系统监控脚本

```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

echo "=== GuiXiaoxi RAG 系统状态 ==="
echo "时间: $(date)"
echo

# 检查进程
echo "=== 进程状态 ==="
ps aux | grep -E "(app.py|streamlit)" | grep -v grep

# 检查端口
echo "=== 端口状态 ==="
netstat -tlnp | grep -E ":8000|:8501"

# 检查资源使用
echo "=== 资源使用 ==="
free -h
df -h | grep -E "/$|/tmp"

# 检查服务健康
echo "=== 服务健康检查 ==="
curl -s http://localhost:8000/health | jq .
```

### 3. 性能测试脚本

```python
#!/usr/bin/env python3
# performance_test.py - 性能测试脚本

import requests
import time
import statistics

def test_api_performance():
    """测试 API 性能"""
    url = "http://localhost:8000/query"
    test_queries = [
        "计算机学院的院长是谁？",
        "人工智能专业有哪些课程？",
        "软件工程专业的培养目标是什么？"
    ]
    
    response_times = []
    
    for query in test_queries:
        start_time = time.time()
        try:
            response = requests.post(url, json={
                "query": query,
                "mode": "local"
            }, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = end_time - start_time
                response_times.append(response_time)
                print(f"✅ 查询成功: {query[:20]}... ({response_time:.2f}s)")
            else:
                print(f"❌ 查询失败: {query[:20]}... (HTTP {response.status_code})")
        except Exception as e:
            print(f"❌ 查询异常: {query[:20]}... ({e})")
    
    if response_times:
        print(f"\n📊 性能统计:")
        print(f"平均响应时间: {statistics.mean(response_times):.2f}s")
        print(f"最快响应时间: {min(response_times):.2f}s")
        print(f"最慢响应时间: {max(response_times):.2f}s")

if __name__ == "__main__":
    test_api_performance()
```

## 📞 获取帮助

### 1. 自助诊断清单

在寻求帮助前，请完成以下检查：

- [ ] 确认 Python 环境正确激活
- [ ] 检查所有依赖是否正确安装
- [ ] 验证配置文件格式正确
- [ ] 确认端口未被其他程序占用
- [ ] 检查系统资源是否充足
- [ ] 查看日志文件中的错误信息

### 2. 收集诊断信息

```bash
#!/bin/bash
# collect_info.sh - 收集诊断信息

echo "=== 系统信息 ===" > diagnostic_info.txt
uname -a >> diagnostic_info.txt
python --version >> diagnostic_info.txt
conda --version >> diagnostic_info.txt

echo -e "\n=== 环境信息 ===" >> diagnostic_info.txt
conda info --envs >> diagnostic_info.txt
pip list >> diagnostic_info.txt

echo -e "\n=== 配置信息 ===" >> diagnostic_info.txt
cat config.json >> diagnostic_info.txt

echo -e "\n=== 最近日志 ===" >> diagnostic_info.txt
tail -50 logs/guixiaoxi.log >> diagnostic_info.txt

echo "诊断信息已保存到 diagnostic_info.txt"
```

### 3. 联系支持

如果问题仍未解决，请提供以下信息：

1. **问题描述**：详细描述遇到的问题
2. **复现步骤**：如何重现该问题
3. **错误信息**：完整的错误日志
4. **系统环境**：操作系统、Python 版本等
5. **配置文件**：config.json 内容（隐藏敏感信息）

**联系方式**：
- 📧 邮箱：<EMAIL>
- 🐛 GitHub Issues：https://github.com/example/issues
- 💬 讨论区：https://github.com/example/discussions
