# GuiXiaoxi RAG API 参考文档

## 🚀 API 概览

GuiXiaoxi RAG 提供了完整的 REST API 接口，支持所有核心功能的程序化访问。

**基础信息**：
- **Base URL**: `http://localhost:8000`
- **API 版本**: v1
- **认证方式**: 无需认证（本地部署）
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📋 API 端点列表

### 1. 系统管理

#### 1.1 健康检查
```http
GET /health
```

**描述**: 检查服务运行状态

**响应示例**:
```json
{
  "success": true,
  "message": "服务运行正常",
  "data": {
    "status": "healthy",
    "insert_manager": true,
    "query_manager": true
  }
}
```

#### 1.2 获取配置信息
```http
GET /config
```

**描述**: 获取系统配置信息

**响应示例**:
```json
{
  "success": true,
  "message": "配置获取成功",
  "data": {
    "llm_model": "qwen14b",
    "embedding_model": "embedding_qwen",
    "working_dir": "./output"
  }
}
```

### 2. 查询功能

#### 2.1 单个查询
```http
POST /query
```

**描述**: 执行单个问题查询

**请求体**:
```json
{
  "query": "计算机科学与技术学院的院长是谁？",
  "mode": "mix"
}
```

**参数说明**:
- `query` (string, 必需): 查询问题
- `mode` (string, 可选): 查询模式，可选值：
  - `local`: 局部模式
  - `global`: 全局模式
  - `hybrid`: 混合模式
  - `mix`: 综合模式（默认）

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": null,
  "result": "根据知识图谱信息，计算机科学与技术学院的现任院长是秦永彬教授..."
}
```

#### 2.2 批量查询
```http
POST /query/batch
```

**描述**: 批量处理多个查询

**请求体**:
```json
{
  "queries": [
    "计算机学院有哪些专业？",
    "人工智能专业的培养目标是什么？"
  ],
  "mode": "local"
}
```

**参数说明**:
- `queries` (array, 必需): 查询问题列表
- `mode` (string, 可选): 查询模式，默认为 "mix"

**响应示例**:
```json
{
  "success": true,
  "message": "批量查询成功",
  "data": {
    "total": 2,
    "processed": 2
  },
  "results": [
    "计算机科学与技术学院设有以下专业...",
    "人工智能专业的培养目标是..."
  ]
}
```

#### 2.3 获取查询模板
```http
GET /query/templates
```

**描述**: 获取预定义的查询模板

**响应示例**:
```json
{
  "success": true,
  "message": "模板获取成功",
  "data": {
    "学院信息": "请介绍计算机科学与技术学院的基本情况",
    "专业查询": "请介绍{专业名称}专业的培养目标和课程设置",
    "师资查询": "请介绍{教师姓名}的研究方向和学术成果"
  }
}
```

### 3. 文档管理

#### 3.1 插入文本
```http
POST /insert/text
```

**描述**: 直接插入文本到知识库

**请求体**:
```json
{
  "texts": [
    "这是第一段要插入的文本内容...",
    "这是第二段要插入的文本内容..."
  ]
}
```

**参数说明**:
- `texts` (array, 必需): 要插入的文本列表

**响应示例**:
```json
{
  "success": true,
  "message": "文本插入成功",
  "data": {
    "inserted_count": 2,
    "processing_time": "3.2s"
  }
}
```

#### 3.2 从目录导入
```http
POST /insert/directory
```

**描述**: 从指定目录批量导入文档

**请求体** (form-data):
```
directory_name: cs_college_data
```

**参数说明**:
- `directory_name` (string, 必需): 目录名称

**响应示例**:
```json
{
  "success": true,
  "message": "目录导入成功",
  "data": {
    "directory": "cs_college_data",
    "files_processed": 15,
    "processing_time": "45.6s"
  }
}
```

#### 3.3 文件上传
```http
POST /insert/upload
```

**描述**: 上传文件到知识库

**请求体** (multipart/form-data):
```
files: [file1.txt, file2.pdf, ...]
```

**参数说明**:
- `files` (files, 必需): 要上传的文件列表

**响应示例**:
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "uploaded_files": ["file1.txt", "file2.pdf"],
    "processing_time": "12.3s"
  }
}
```

#### 3.4 清空知识库
```http
DELETE /insert/clear
```

**描述**: 清空整个知识库（危险操作）

**响应示例**:
```json
{
  "success": true,
  "message": "知识库已清空",
  "data": {
    "cleared_at": "2025-07-30T15:30:00Z"
  }
}
```

### 4. 知识图谱

#### 4.1 获取图统计
```http
GET /graph/stats
```

**描述**: 获取知识图谱统计信息

**响应示例**:
```json
{
  "success": true,
  "message": "统计信息获取成功",
  "data": {
    "nodes_count": 10087,
    "edges_count": 15192,
    "json_file_exists": true,
    "xml_file_exists": true,
    "last_updated": "2025-07-30T12:00:00Z"
  }
}
```

#### 4.2 导出图数据
```http
GET /graph/export
```

**描述**: 导出完整的知识图谱数据

**响应示例**:
```json
{
  "success": true,
  "message": "图数据导出成功",
  "data": {
    "export_time": "2025-07-30T15:30:00Z"
  },
  "graph_data": {
    "nodes": [...],
    "edges": [...]
  }
}
```

#### 4.3 获取节点数据
```http
GET /graph/nodes?limit=10&offset=0
```

**描述**: 分页获取图节点数据

**查询参数**:
- `limit` (int, 可选): 每页数量，默认 10
- `offset` (int, 可选): 偏移量，默认 0

**响应示例**:
```json
{
  "success": true,
  "message": "节点数据获取成功",
  "data": {
    "nodes": [
      {
        "id": "node_1",
        "entity_type": "Person",
        "description": "教授，研究方向为人工智能",
        "source_id": "doc_123"
      }
    ],
    "total": 10087,
    "limit": 10,
    "offset": 0
  }
}
```

## 🔧 错误处理

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "data": null,
  "error_code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `QUERY_FAILED` | 500 | 查询执行失败 |
| `INSERT_FAILED` | 500 | 文档插入失败 |
| `FILE_NOT_FOUND` | 404 | 文件未找到 |
| `INVALID_PARAMETER` | 400 | 参数无效 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |
| `TOKEN_LIMIT_EXCEEDED` | 400 | Token 超出限制 |

## 📝 使用示例

### Python 示例

```python
import requests
import json

# 基础配置
BASE_URL = "http://localhost:8000"

# 1. 健康检查
response = requests.get(f"{BASE_URL}/health")
print(response.json())

# 2. 单个查询
query_data = {
    "query": "计算机学院的院长是谁？",
    "mode": "mix"
}
response = requests.post(f"{BASE_URL}/query", json=query_data)
result = response.json()
print(result["result"])

# 3. 批量查询
batch_data = {
    "queries": [
        "学院有哪些专业？",
        "人工智能专业的课程有哪些？"
    ],
    "mode": "local"
}
response = requests.post(f"{BASE_URL}/query/batch", json=batch_data)
results = response.json()["results"]
for i, result in enumerate(results):
    print(f"问题 {i+1}: {result}")

# 4. 插入文本
text_data = {
    "texts": ["这是要插入的新文本内容"]
}
response = requests.post(f"{BASE_URL}/insert/text", json=text_data)
print(response.json())

# 5. 获取图统计
response = requests.get(f"{BASE_URL}/graph/stats")
stats = response.json()["data"]
print(f"节点数: {stats['nodes_count']}, 边数: {stats['edges_count']}")
```

### JavaScript 示例

```javascript
const BASE_URL = "http://localhost:8000";

// 单个查询
async function query(question, mode = "mix") {
    const response = await fetch(`${BASE_URL}/query`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: question,
            mode: mode
        })
    });
    
    const result = await response.json();
    return result.success ? result.result : result.message;
}

// 使用示例
query("计算机学院的院长是谁？")
    .then(answer => console.log(answer))
    .catch(error => console.error(error));
```

### cURL 示例

```bash
# 健康检查
curl -X GET "http://localhost:8000/health"

# 单个查询
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "计算机学院的院长是谁？", "mode": "mix"}'

# 批量查询
curl -X POST "http://localhost:8000/query/batch" \
  -H "Content-Type: application/json" \
  -d '{"queries": ["学院有哪些专业？", "AI专业的课程？"], "mode": "local"}'

# 插入文本
curl -X POST "http://localhost:8000/insert/text" \
  -H "Content-Type: application/json" \
  -d '{"texts": ["这是新的文本内容"]}'

# 获取图统计
curl -X GET "http://localhost:8000/graph/stats"
```

## 🚀 性能优化建议

### 1. 查询优化
- 使用合适的查询模式
- 避免过于复杂的查询
- 合理使用批量查询

### 2. 并发控制
- 避免同时进行大量查询
- 使用适当的超时设置
- 监控系统资源使用

### 3. 数据管理
- 定期清理无用数据
- 合理分批插入大量文档
- 监控知识库大小

## 📞 技术支持

如有问题，请：
1. 检查 API 文档和示例
2. 查看服务日志
3. 访问 http://localhost:8000/docs 查看交互式文档
4. 联系技术支持团队
