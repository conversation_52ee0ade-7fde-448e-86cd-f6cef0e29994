# GuiXiaoxi RAG Streamlit Web 界面使用指南

## 🎯 概述

GuiXiaoxi RAG Streamlit 应用提供了一个直观的 Web 界面，让用户可以通过浏览器轻松使用所有 RAG 功能，无需编写代码或使用命令行工具。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

```bash
# 激活环境
conda activate lightrag312

# 运行启动脚本（会自动启动 API 和 Web 界面）
python start_streamlit.py
```

### 方法二：分别启动服务

```bash
# 激活环境
conda activate lightrag312

# 终端1：启动 API 服务
python start_server.py --host 0.0.0.0 --port 8000

# 终端2：启动 Streamlit 应用
streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **Streamlit Web 界面**: http://localhost:8501
- **API 服务**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs

## 📱 功能页面详解

### 🏠 首页概览

**功能**：
- 系统状态监控
- 快速查询入口
- 知识图谱统计
- 最近查询历史

**使用方法**：
1. 查看系统运行状态和统计信息
2. 在快速查询框输入问题
3. 选择查询模式（mix/local/global/hybrid）
4. 点击"立即查询"获取答案

### 💬 智能问答

**功能**：
- 类似 ChatGPT 的对话界面
- 支持多轮对话
- 查询模式选择
- 查询模板快速使用

**使用方法**：
1. 选择合适的查询模式
2. 在输入框输入问题
3. 查看 AI 回答
4. 可以继续追问相关问题
5. 使用查询模板快速构建问题

**查询模式说明**：
- **mix**: 混合模式，综合多种检索策略，适合大多数问题
- **local**: 局部模式，基于局部信息检索，适合具体细节问题
- **global**: 全局模式，基于全局信息检索，适合概括性问题
- **hybrid**: 混合模式，结合局部和全局信息，适合复杂问题

### 📚 批量查询

**功能**：
- 同时处理多个问题
- 支持手动输入和文件上传
- 结果导出（CSV/JSON）
- 查询统计分析

**使用方法**：
1. 选择输入方式：
   - **手动输入**：每行一个问题
   - **文件上传**：支持 TXT 和 CSV 文件
2. 选择查询模式
3. 预览问题列表
4. 点击"开始批量查询"
5. 查看结果并可导出

### 📄 文档管理

**功能**：
- 文本直接插入
- 文件上传处理
- 目录批量导入
- 知识库管理

**使用方法**：

#### 文本插入
1. 选择输入方式（单个/多个/文件上传）
2. 输入或上传文本内容
3. 预览内容
4. 点击"插入到知识库"

#### 目录导入
1. 输入服务器目录名称（如：cs_college_data）
2. 点击"从目录导入"
3. 等待处理完成

#### 知识库管理
1. 查看知识库统计信息
2. 清空知识库（谨慎操作）

### 📊 知识图谱

**功能**：
- 图谱统计分析
- 节点数据浏览
- 图数据导出
- 可视化展示

**使用方法**：

#### 图谱统计
- 查看节点数、边数、图密度等统计信息
- 查看数据文件状态
- 简单的可视化图表

#### 节点浏览
1. 设置每页显示数量
2. 选择页码
3. 点击"刷新节点数据"
4. 浏览节点详细信息

#### 数据导出
1. 点击"导出完整图数据"
2. 选择下载格式（JSON/Excel）
3. 查看数据预览

### ⚙️ 系统管理

**功能**：
- 服务状态监控
- 系统性能分析
- 缓存管理
- 维护操作

**使用方法**：

#### 服务状态
- 点击"刷新服务状态"检查系统健康状况
- 查看各组件运行状态

#### 系统监控
- 查看查询历史统计
- 分析查询模式分布
- 监控知识图谱状态

#### 维护操作
- 清空查询历史
- 清空聊天记录
- 查看系统信息

## 💡 使用技巧

### 1. 查询优化
- **具体问题**：使用 local 模式，如"某个专业的课程设置"
- **概括问题**：使用 global 模式，如"学院整体情况"
- **复杂问题**：使用 hybrid 或 mix 模式
- **不确定时**：使用 mix 模式（默认推荐）

### 2. 批量查询
- 问题数量建议不超过 20 个，避免超时
- 使用 CSV 文件时，问题放在第一列
- 可以先用少量问题测试，再进行大批量处理

### 3. 文档管理
- 插入新文档后，等待几分钟让系统处理完成
- 大文档建议分段插入，提高处理效率
- 定期检查知识库状态，确保数据完整性

### 4. 性能优化
- 避免同时进行多个大型操作
- 定期清理查询历史和聊天记录
- 监控系统资源使用情况

## 🔧 故障排除

### 常见问题

#### 1. 页面无法访问
- 检查 Streamlit 服务是否启动：http://localhost:8501
- 检查防火墙设置
- 确认端口 8501 未被占用

#### 2. 查询失败
- 检查 API 服务状态：http://localhost:8000/health
- 确认网络连接正常
- 查看错误信息并重试

#### 3. 文档上传失败
- 检查文件格式是否支持
- 确认文件大小不超过限制
- 检查文件编码（建议使用 UTF-8）

#### 4. 图数据显示异常
- 刷新页面重新加载数据
- 检查知识库是否有数据
- 重新导出图数据

### 解决方案

1. **重启服务**：
   ```bash
   # 停止所有服务（Ctrl+C）
   # 重新启动
   python start_streamlit.py
   ```

2. **清理缓存**：
   - 在系统管理页面清空缓存
   - 刷新浏览器页面

3. **检查日志**：
   - 查看终端输出的错误信息
   - 检查 API 服务日志

## 📈 最佳实践

### 1. 日常使用流程
1. 启动服务并检查状态
2. 根据需要选择合适的功能页面
3. 进行查询或管理操作
4. 定期维护和清理

### 2. 数据管理
- 定期备份重要的查询结果
- 及时导出知识图谱数据
- 保持知识库内容更新

### 3. 性能监控
- 关注系统资源使用情况
- 监控查询响应时间
- 定期检查服务健康状态

## 🎉 总结

GuiXiaoxi RAG Streamlit 应用提供了完整的 Web 界面来使用所有 RAG 功能，让用户可以：

- 🔍 **智能查询**：通过直观的界面进行单个或批量查询
- 📄 **文档管理**：轻松上传和管理知识库文档
- 📊 **数据分析**：可视化知识图谱和系统统计
- ⚙️ **系统维护**：监控和管理系统运行状态

通过这个 Web 界面，用户无需任何编程知识就能充分利用 GuiXiaoxi RAG 的强大功能！
