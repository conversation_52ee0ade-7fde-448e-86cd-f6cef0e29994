# GuiXiaoxi RAG 安装部署指南

## 🎯 系统要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 16GB以上，推荐32GB
- **存储**: 50GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 7+)
- **Python**: 3.10-3.12
- **Conda**: Miniconda 或 Anaconda
- **Git**: 版本控制工具

## 🚀 快速安装

### 1. 环境准备

```bash
# 创建 Conda 环境
conda create -n lightrag312 python=3.12 -y
conda activate lightrag312

# 克隆项目（如果需要）
git clone <repository-url>
cd gui_xiaoxi
```

### 2. 安装依赖

```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 或者使用清华源加速
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 3. 配置设置

```bash
# 复制配置文件
cp config.json.example config.json

# 编辑配置文件
nano config.json
```

### 4. 启动服务

```bash
# 方法1：使用启动脚本
python start_streamlit.py

# 方法2：分别启动
# 终端1：启动 API 服务
python app.py

# 终端2：启动 Web 界面
streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0
```

## 📋 详细安装步骤

### 步骤1：系统环境准备

#### Ubuntu/Debian 系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y git curl wget build-essential

# 安装 Python 开发工具
sudo apt install -y python3-dev python3-pip
```

#### CentOS/RHEL 系统
```bash
# 更新系统包
sudo yum update -y

# 安装必要工具
sudo yum install -y git curl wget gcc gcc-c++ make

# 安装 Python 开发工具
sudo yum install -y python3-devel python3-pip
```

### 步骤2：安装 Conda

```bash
# 下载 Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

# 安装 Miniconda
bash Miniconda3-latest-Linux-x86_64.sh -b -p $HOME/miniconda3

# 初始化 Conda
$HOME/miniconda3/bin/conda init bash
source ~/.bashrc
```

### 步骤3：创建项目环境

```bash
# 创建专用环境
conda create -n lightrag312 python=3.12 -y

# 激活环境
conda activate lightrag312

# 验证 Python 版本
python --version
```

### 步骤4：安装项目依赖

```bash
# 进入项目目录
cd /path/to/gui_xiaoxi

# 安装核心依赖
pip install fastapi uvicorn streamlit pandas plotly

# 安装完整依赖
pip install -r requirements.txt

# 验证安装
python -c "import fastapi, streamlit, pandas; print('Dependencies installed successfully')"
```

### 步骤5：配置系统

#### 5.1 配置文件设置
```bash
# 检查配置文件
cat config.json

# 主要配置项说明：
# - llm.api_base: LLM 服务地址
# - llm.model: 使用的语言模型
# - embedding.api_base: 嵌入服务地址
# - rag.working_dir: 工作目录
```

#### 5.2 创建必要目录
```bash
# 创建工作目录
mkdir -p output logs temp_uploads

# 设置权限
chmod 755 output logs temp_uploads
```

#### 5.3 环境变量设置
```bash
# 添加到 ~/.bashrc
echo 'export GUIXIAOXI_HOME=/path/to/gui_xiaoxi' >> ~/.bashrc
echo 'export PYTHONPATH=$GUIXIAOXI_HOME:$PYTHONPATH' >> ~/.bashrc
source ~/.bashrc
```

### 步骤6：验证安装

```bash
# 测试 API 服务
python -c "from app import app; print('API app loaded successfully')"

# 测试 Streamlit 应用
python -c "import streamlit_app; print('Streamlit app loaded successfully')"

# 运行综合测试
python comprehensive_test.py
```

## 🔧 配置详解

### config.json 配置文件

```json
{
  "llm": {
    "api_base": "http://localhost:8100/v1",
    "api_key": "your-api-key",
    "model": "qwen14b",
    "max_token_size": 2048,
    "max_tokens": 4096
  },
  "embedding": {
    "api_base": "http://localhost:8200/v1",
    "api_key": "your-api-key",
    "model": "embedding_qwen",
    "embedding_dim": 2560,
    "max_token_size": 2048
  },
  "rag": {
    "working_dir": "./output",
    "language": "中文",
    "max_gleaning": 1,
    "force_llm_summary_on_merge": true
  },
  "api": {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": false,
    "cors_origins": ["*"],
    "max_upload_size": 104857600,
    "temp_dir": "temp_uploads"
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_path": "logs/guixiaoxi.log",
    "max_file_size": 10485760,
    "backup_count": 5
  }
}
```

### 环境变量配置

```bash
# 创建 .env 文件
cat > .env << EOF
# LLM 配置
LLM_API_BASE=http://localhost:8100/v1
LLM_API_KEY=your-api-key
LLM_MODEL=qwen14b

# 嵌入模型配置
EMBEDDING_API_BASE=http://localhost:8200/v1
EMBEDDING_API_KEY=your-api-key
EMBEDDING_MODEL=embedding_qwen

# 系统配置
WORKING_DIR=./output
LOG_LEVEL=INFO
MAX_UPLOAD_SIZE=104857600
EOF
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
FROM python:3.12-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 创建必要目录
RUN mkdir -p output logs temp_uploads

# 暴露端口
EXPOSE 8000 8501

# 启动脚本
CMD ["python", "start_streamlit.py"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  guixiaoxi-rag:
    build: .
    ports:
      - "8000:8000"
      - "8501:8501"
    volumes:
      - ./output:/app/output
      - ./logs:/app/logs
      - ./temp_uploads:/app/temp_uploads
    environment:
      - LLM_API_BASE=http://llm-service:8100/v1
      - EMBEDDING_API_BASE=http://embedding-service:8200/v1
    depends_on:
      - llm-service
      - embedding-service

  llm-service:
    image: your-llm-service:latest
    ports:
      - "8100:8100"

  embedding-service:
    image: your-embedding-service:latest
    ports:
      - "8200:8200"
```

### Docker 部署命令
```bash
# 构建镜像
docker build -t guixiaoxi-rag .

# 运行容器
docker run -d \
  --name guixiaoxi-rag \
  -p 8000:8000 \
  -p 8501:8501 \
  -v $(pwd)/output:/app/output \
  -v $(pwd)/logs:/app/logs \
  guixiaoxi-rag

# 使用 docker-compose
docker-compose up -d
```

## 🔍 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 问题：pip 安装超时
# 解决：使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 问题：编译错误
# 解决：安装编译工具
sudo apt install build-essential python3-dev
```

#### 2. 服务启动失败
```bash
# 问题：端口被占用
# 解决：检查端口使用情况
netstat -tlnp | grep :8000
kill -9 <pid>

# 问题：权限不足
# 解决：检查文件权限
chmod +x start_server.py
chmod 755 output logs temp_uploads
```

#### 3. 模型连接失败
```bash
# 问题：无法连接 LLM 服务
# 解决：检查服务状态和配置
curl http://localhost:8100/v1/models

# 问题：API Key 无效
# 解决：检查配置文件中的 API Key
```

#### 4. 内存不足
```bash
# 问题：系统内存不足
# 解决：增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 日志分析

```bash
# 查看应用日志
tail -f logs/guixiaoxi.log

# 查看系统资源使用
htop
df -h
free -h

# 查看网络连接
netstat -tlnp | grep python
```

## 📊 性能优化

### 系统优化
```bash
# 调整文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化
```python
# 在 config.json 中调整参数
{
  "api": {
    "workers": 4,  # 增加工作进程数
    "max_connections": 1000,
    "keepalive_timeout": 30
  },
  "rag": {
    "max_concurrent_queries": 10,
    "cache_size": 1000
  }
}
```

## 🔄 更新升级

### 代码更新
```bash
# 备份当前版本
cp -r gui_xiaoxi gui_xiaoxi_backup

# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重启服务
python start_streamlit.py
```

### 数据迁移
```bash
# 备份数据
tar -czf output_backup.tar.gz output/

# 迁移配置
cp config.json config.json.bak
# 手动合并新配置项
```

## 📞 技术支持

### 获取帮助
- 📖 查看文档：`docs/` 目录
- 🐛 报告问题：GitHub Issues
- 💬 技术交流：项目讨论区
- 📧 联系支持：<EMAIL>

### 社区资源
- 官方文档：https://docs.example.com
- 示例代码：https://github.com/example/samples
- 视频教程：https://youtube.com/example
- 技术博客：https://blog.example.com
