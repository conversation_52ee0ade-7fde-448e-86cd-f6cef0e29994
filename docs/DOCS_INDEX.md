# GuiXiaoxi RAG 文档中心

欢迎来到 GuiXiaoxi RAG 智能问答系统的文档中心！这里包含了系统的完整文档，帮助您快速上手和深入了解系统功能。

## 📚 文档导航

### 🚀 快速开始
- **[安装部署指南](INSTALLATION.md)** - 从零开始部署系统
  - 系统要求和环境准备
  - 详细的安装步骤
  - 配置文件说明
  - Docker 部署方案
  - 故障排除和优化建议

### 🎨 用户指南
- **[Web 界面使用指南](STREAMLIT_GUIDE.md)** - Streamlit 应用完整教程
  - 功能页面详解
  - 操作步骤说明
  - 使用技巧和最佳实践
  - 常见问题解答

### 🔧 开发者文档
- **[API 参考文档](API_REFERENCE.md)** - 完整的 API 接口说明
  - 所有端点的详细说明
  - 请求和响应格式
  - 错误处理机制
  - 使用示例和代码片段

### 🚨 运维支持
- **[故障排除指南](TROUBLESHOOTING.md)** - 常见问题解决方案
  - 服务启动问题
  - 查询相关问题
  - 性能优化建议
  - 调试工具和方法

### 📊 项目信息
- **[部署状态报告](DEPLOYMENT_STATUS.md)** - 系统部署和测试结果
  - 功能验证结果
  - 性能指标统计
  - 技术架构说明
  - 项目文件结构

- **[项目总结](PROJECT_SUMMARY.md)** - 项目概况和技术细节
  - 项目背景和目标
  - 技术选型和架构
  - 开发过程和里程碑
  - 未来发展规划

## 🎯 文档使用建议

### 新用户推荐阅读顺序
1. **[安装部署指南](INSTALLATION.md)** - 了解如何部署系统
2. **[Web 界面使用指南](STREAMLIT_GUIDE.md)** - 学习如何使用 Web 界面
3. **[故障排除指南](TROUBLESHOOTING.md)** - 遇到问题时的解决方案

### 开发者推荐阅读顺序
1. **[项目总结](PROJECT_SUMMARY.md)** - 了解项目整体架构
2. **[API 参考文档](API_REFERENCE.md)** - 学习 API 接口使用
3. **[部署状态报告](DEPLOYMENT_STATUS.md)** - 了解系统当前状态

### 运维人员推荐阅读顺序
1. **[安装部署指南](INSTALLATION.md)** - 掌握部署和配置
2. **[故障排除指南](TROUBLESHOOTING.md)** - 学习问题诊断和解决
3. **[部署状态报告](DEPLOYMENT_STATUS.md)** - 了解系统性能指标

## 📖 文档特色

### 🎯 实用性强
- 每个文档都包含具体的操作步骤
- 提供大量的代码示例和配置样例
- 涵盖从入门到高级的各种使用场景

### 🔄 持续更新
- 文档与系统功能保持同步更新
- 根据用户反馈不断完善内容
- 定期添加新的使用案例和最佳实践

### 🎨 结构清晰
- 采用统一的文档格式和风格
- 使用丰富的图标和表格提升可读性
- 提供清晰的导航和交叉引用

## 🤝 文档贡献

我们欢迎社区贡献文档内容！如果您发现：

- 📝 文档内容有误或过时
- 💡 有更好的使用方法或技巧
- 🆕 希望添加新的使用场景
- 🐛 发现了新的问题和解决方案

请通过以下方式参与贡献：

1. **GitHub Issues** - 报告文档问题
2. **Pull Request** - 直接提交文档改进
3. **讨论区** - 分享使用经验和建议

## 📞 获取帮助

如果您在使用文档过程中遇到问题：

### 📚 自助资源
- 仔细阅读相关文档章节
- 查看 [故障排除指南](TROUBLESHOOTING.md)
- 搜索 GitHub Issues 中的相关讨论

### 💬 社区支持
- **GitHub Discussions** - 技术讨论和经验分享
- **GitHub Issues** - 问题报告和功能请求
- **项目 Wiki** - 社区维护的补充文档

### 📧 直接联系
- **技术支持邮箱**: <EMAIL>
- **项目维护者**: 通过 GitHub 联系

## 🔄 文档版本

| 版本 | 发布日期 | 主要更新 |
|------|----------|----------|
| v1.0 | 2025-07-30 | 初始版本，包含完整的系统文档 |

## 📋 文档清单

- ✅ [安装部署指南](INSTALLATION.md) - 完整详细
- ✅ [Web 界面使用指南](STREAMLIT_GUIDE.md) - 功能全面
- ✅ [API 参考文档](API_REFERENCE.md) - 接口完整
- ✅ [故障排除指南](TROUBLESHOOTING.md) - 问题覆盖全面
- ✅ [部署状态报告](DEPLOYMENT_STATUS.md) - 信息准确
- ✅ [项目总结](PROJECT_SUMMARY.md) - 内容丰富

---

**GuiXiaoxi RAG 文档中心** - 您的智能问答系统使用指南 📚
