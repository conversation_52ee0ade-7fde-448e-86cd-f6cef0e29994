# GuiXiaoxi RAG v2.0 项目结构

## 📁 项目目录结构

```
gui_xiaoxi/
├── 🚀 启动文件
│   ├── start_server.py           # API 服务启动脚本
│   ├── start_streamlit.py        # 完整服务启动脚本
│   └── README.md                 # 项目主文档
│
├── 🔧 核心模块
│   ├── app.py                    # FastAPI 主应用
│   ├── config.py                 # 配置管理模块
│   ├── query.py                  # 增强查询模块
│   ├── insert.py                 # 增强插入模块
│   ├── streamlit_app.py          # Streamlit Web 应用
│   └── test_system.py            # 系统测试脚本
│
├── ⚙️ 配置文件
│   ├── config.json               # 系统配置文件
│   ├── config.json.example       # 配置示例文件
│   └── requirements.txt          # Python 依赖列表
│
├── 📚 知识库目录 (knowledgeBase/)
│   ├── default/                  # 默认知识库
│   ├── cs_college/              # 计算机学院知识库
│   ├── general/                 # 通用知识库
│   └── custom/                  # 自定义知识库
│
├── 🖥️ 服务端代码 (server/)
│   ├── api/                      # API 服务模块（已迁移到根目录）
│   ├── web/                     # Web 界面模块（已迁移到根目录）
│   ├── core/                    # 核心功能模块（已迁移到根目录）
│   ├── config/                  # 配置模块（已迁移到根目录）
│   ├── tests/                   # 测试模块
│   ├── scripts/                 # 脚本工具
│   ├── utils/                   # 工具模块
│   ├── README.md                # 服务端说明
│   └── __init__.py
│
├── 📚 文档目录 (docs/)
│   ├── README.md                # 文档中心索引
│   ├── INSTALLATION.md          # 安装部署指南
│   ├── STREAMLIT_GUIDE.md       # Web 界面使用指南
│   ├── API_REFERENCE.md         # API 参考文档
│   ├── TROUBLESHOOTING.md       # 故障排除指南
│   ├── DEPLOYMENT_STATUS.md     # 部署状态报告
│   └── PROJECT_SUMMARY.md       # 项目总结
│
├── 🤖 RAG 引擎 (guixiaoxi/)
│   ├── guixiaoxi_rag.py         # 主 RAG 类
│   ├── operate.py               # 核心操作
│   ├── base.py                  # 基础类
│   ├── llm/                     # 语言模型接口
│   ├── kg/                      # 知识图谱存储
│   ├── api/                     # 原始 API 实现
│   └── tools/                   # 工具集合
│
├── 📊 数据目录
│   ├── output/                  # 知识图谱数据
│   ├── files/                   # 输入文档
│   ├── temp_uploads/            # 临时上传
│   └── jim_test/                # 测试数据
│
└── 📋 其他文件
    ├── lightrag.log             # 日志文件
    └── PROJECT_STRUCTURE.md     # 本文件
```

## 🎯 设计原则

### 1. 清晰的分层架构
- **启动层**: 项目根目录只保留启动文件和主文档
- **服务层**: server/ 目录包含所有服务端代码
- **文档层**: docs/ 目录包含所有项目文档
- **引擎层**: guixiaoxi/ 目录包含 RAG 核心引擎

### 2. 模块化组织
- **按功能分类**: API、Web、Core、Config 等
- **职责单一**: 每个模块专注特定功能
- **松耦合**: 模块间通过接口交互

### 3. 便于维护
- **统一入口**: 通过启动脚本统一管理
- **配置集中**: 配置文件集中在 config/ 目录
- **测试完整**: 测试文件组织在 tests/ 目录

## 🚀 使用方式

### 启动服务
```bash
# 启动完整服务（推荐）
python start_streamlit.py

# 只启动 API 服务
python start_server.py

# 只启动 Web 界面
python start_streamlit.py --web-only
```

### 开发调试
```bash
# 启动开发模式
python start_server.py --debug --reload

# 运行测试
python server/tests/comprehensive_test.py

# 查看配置
cat server/config/config.json
```

### 模块导入
```python
# 从项目根目录导入
from server.core.query import GuiXiaoxiQueryManager
from server.core.insert import GuiXiaoxiRAGManager
from server.config.config import config_manager
```

## 📝 文件说明

### 启动文件
- **start_server.py**: API 服务启动脚本，支持多种启动参数
- **start_streamlit.py**: 完整服务启动脚本，自动管理 API 和 Web 服务

### 核心模块
- **server/api/app.py**: FastAPI 应用，提供 REST API 接口
- **server/web/streamlit_app.py**: Streamlit 应用，提供 Web 界面
- **server/core/**: 核心业务逻辑，包含查询、插入、优化等功能

### 配置管理
- **server/config/config.py**: 配置管理类
- **server/config/config.json**: 系统配置文件
- **server/config/requirements.txt**: Python 依赖列表

### 测试和工具
- **server/tests/**: 各种测试脚本
- **server/scripts/**: 部署和演示脚本
- **server/utils/**: 工具和旧版本文件

## 🔧 开发指南

### 添加新功能
1. 在相应的 server/ 子目录下创建新文件
2. 更新相关的 `__init__.py` 文件
3. 在 API 应用中注册新端点（如需要）
4. 添加相应的测试用例

### 修改配置
1. 编辑 `server/config/config.json`
2. 或通过环境变量覆盖配置
3. 重启服务使配置生效

### 部署更新
1. 使用 `server/scripts/deploy.sh` 自动部署
2. 或手动更新代码后重启服务
3. 运行 `server/tests/verify_deployment.py` 验证部署

## 📊 目录统计

| 目录 | 文件数 | 主要内容 |
|------|--------|----------|
| 根目录 | 3 | 启动文件和主文档 |
| server/ | 20+ | 所有服务端代码 |
| docs/ | 7 | 完整项目文档 |
| guixiaoxi/ | 50+ | RAG 核心引擎 |
| 数据目录 | 变动 | 运行时数据 |

## 🎉 优势特点

### 1. 结构清晰
- 根目录简洁，只保留必要文件
- 功能模块分类明确
- 文档组织完整

### 2. 易于使用
- 统一的启动入口
- 清晰的使用说明
- 完整的文档支持

### 3. 便于维护
- 模块化设计
- 配置集中管理
- 测试覆盖完整

### 4. 扩展性强
- 松耦合架构
- 标准化接口
- 插件化设计

这种项目结构既保持了代码的组织性，又提供了良好的用户体验，是现代 Python 项目的最佳实践！
