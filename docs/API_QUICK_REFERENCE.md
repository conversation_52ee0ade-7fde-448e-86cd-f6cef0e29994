# GuiXiaoxi RAG API 快速参考

## 🚀 快速开始

### 基础URL
```
http://localhost:8000
```

### 健康检查
```bash
curl http://localhost:8000/health
```

## 📋 核心API端点

### 1. 查询接口

#### 基础查询
```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "计算机学院院长是谁？",
    "mode": "hybrid",
    "kb_ids": ["cs_college"]
  }'
```

#### 流式查询
```bash
curl -X POST "http://localhost:8000/query/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "介绍人工智能专业",
    "mode": "hybrid"
  }'
```

#### 通用问答
```bash
curl -X POST "http://localhost:8000/query/general" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能？",
    "stream": false
  }'
```

### 2. 知识库管理

#### 列出知识库
```bash
curl http://localhost:8000/knowledge-bases
```

#### 创建知识库
```bash
curl -X POST "http://localhost:8000/knowledge-bases/new_kb" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新知识库",
    "description": "测试知识库",
    "working_dir": "./knowledgeBase/new_kb"
  }'
```

### 3. 文档管理

#### 插入文本
```bash
curl -X POST "http://localhost:8000/insert/text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是要插入的文本内容",
    "kb_id": "cs_college",
    "description": "测试文档"
  }'
```

#### 上传文件
```bash
curl -X POST "http://localhost:8000/insert/file/cs_college" \
  -F "file=@document.pdf" \
  -F "description=重要文档"
```

### 4. 图数据

#### 获取图统计
```bash
curl http://localhost:8000/graph/stats
```

#### 导出图数据
```bash
curl "http://localhost:8000/graph/export?kb_id=cs_college&format=json"
```

## 🐍 Python 客户端示例

```python
import requests

class GuiXiaoxiClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def query(self, question, mode="hybrid", kb_ids=None):
        """基础查询"""
        payload = {
            "query": question,
            "mode": mode
        }
        if kb_ids:
            payload["kb_ids"] = kb_ids
            
        response = self.session.post(f"{self.base_url}/query", json=payload)
        return response.json()
    
    def general_qa(self, question):
        """通用问答"""
        payload = {"query": question, "stream": False}
        response = self.session.post(f"{self.base_url}/query/general", json=payload)
        return response.json()
    
    def get_knowledge_bases(self):
        """获取知识库列表"""
        response = self.session.get(f"{self.base_url}/knowledge-bases")
        return response.json()

# 使用示例
client = GuiXiaoxiClient()

# 查询
result = client.query("计算机学院院长是谁？")
print(result)

# 通用问答
answer = client.general_qa("什么是人工智能？")
print(answer)

# 获取知识库
kbs = client.get_knowledge_bases()
print(kbs)
```

## 🌐 JavaScript 客户端示例

```javascript
class GuiXiaoxiClient {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  async query(question, options = {}) {
    const payload = {
      query: question,
      mode: options.mode || 'hybrid',
      kb_ids: options.kb_ids
    };

    const response = await fetch(`${this.baseURL}/query`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    return response.json();
  }

  async generalQA(question) {
    const payload = { query: question, stream: false };
    
    const response = await fetch(`${this.baseURL}/query/general`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    return response.json();
  }
}

// 使用示例
const client = new GuiXiaoxiClient();

client.query('计算机学院院长是谁？')
  .then(result => console.log(result));
```

## 📊 查询模式说明

| 模式 | 描述 | 适用场景 |
|------|------|----------|
| `local` | 本地实体查询 | 查询特定实体详细信息 |
| `global` | 全局关系查询 | 查询实体间关系和连接 |
| `hybrid` | 混合查询 | 大多数复杂查询场景 |
| `naive` | 简单向量检索 | 简单文档检索 |
| `bypass` | 直接LLM查询 | 通用对话，不需要特定知识 |

## 🔧 常用配置

### 环境变量
```bash
export GUIXIAOXI_LLM_API_BASE="http://localhost:8100/v1"
export GUIXIAOXI_EMBEDDING_API_BASE="http://localhost:8200/v1"
export GUIXIAOXI_API_PORT=8000
```

### 启动服务
```bash
# 完整服务（API + Web）
python start_streamlit.py

# 仅API服务
python start_server.py
```

## 📝 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "response": "查询结果内容...",
    "mode": "hybrid",
    "timestamp": "2025-07-31T12:34:56"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 🚨 常见问题

### 1. 连接失败
- 检查服务是否启动：`curl http://localhost:8000/health`
- 检查端口是否被占用：`netstat -tlnp | grep 8000`

### 2. 查询超时
- 增加请求超时时间
- 检查LLM服务是否正常运行

### 3. 知识库为空
- 确保已导入文档数据
- 检查知识库配置是否正确

## 📞 获取帮助

- 查看完整文档：`README.md`
- API文档：`http://localhost:8000/docs`
- 健康检查：`http://localhost:8000/health`
