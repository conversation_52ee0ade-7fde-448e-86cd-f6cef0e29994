2025-07-31 03:22:44,488 - guixiaoxi_api - INFO - 启动 GuiXiaoxi RAG 服务...
2025-07-31 03:22:44,488 - guixiaoxi_api - INFO - 配置文件: config.json
2025-07-31 03:22:44,488 - guixiaoxi_api - INFO - 服务地址: 0.0.0.0:8010
2025-07-31 03:22:44,488 - guixiaoxi_api - INFO - 工作目录: ./knowledgeBase
2025-07-31 03:22:44,488 - guixiaoxi_api - INFO - 调试模式: False
2025-07-31 03:22:45,395 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:22:45,395 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:22:45,395 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 default 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:22:47,181 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:22:47,251 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:22:49,095 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:22:49,199 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:22:49,319 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:22:49,322 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
ERROR: 初始化知识库 cs_college 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:22:49,323 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:22:49,323 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:22:49,323 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 general 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:22:49,324 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_entities.json'} 0 data
2025-07-31 03:22:49,324 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_relationships.json'} 0 data
2025-07-31 03:22:49,324 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_chunks.json'} 0 data
ERROR: 初始化通用问答RAG失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:22:49,326 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:22:49,326 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:22:49,326 - guixiaoxi_api - INFO - 异常处理器设置完成
2025-07-31 03:22:49,326 - guixiaoxi_api - INFO - 异常处理器设置完成
INFO:     Started server process [977344]
INFO:     Waiting for application startup.
2025-07-31 03:22:49,361 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:22:49,361 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:22:49,362 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:22:49,362 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 创建成功，正在异步初始化...
2025-07-31 03:22:51,200 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:22:51,200 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:22:51,266 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:22:51,266 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:22:53,100 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:22:53,100 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:22:53,204 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:22:53,204 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:22:53,328 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:22:53,328 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:22:53,331 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:22:53,331 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 创建成功，正在异步初始化...
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:22:53,332 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 创建成功，正在异步初始化...
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:22:53,333 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 初始化成功
2025-07-31 03:22:55,126 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:22:55,126 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:22:55,193 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:22:55,193 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:22:57,019 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:22:57,019 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:22:57,129 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:22:57,129 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:22:57,252 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:22:57,252 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:22:57,255 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:22:57,255 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 初始化成功
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:22:57,256 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 初始化成功
2025-07-31 03:22:57,256 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
2025-07-31 03:22:57,256 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
INFO:     Application startup complete.
INFO: 知识库 default 异步初始化完成
INFO: 知识库 cs_college 异步初始化完成
INFO: 知识库 general 异步初始化完成
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
2025-07-31 03:22:57,455 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:22:57,455 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:22:57,456 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:22:57,456 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:38984 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:22:58,462 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:22:58,462 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:22:58,463 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:22:58,463 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:38986 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:22:58,464 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 03:22:58,464 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 03:22:58,465 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:22:58,465 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:38992 - "GET /config HTTP/1.1" 200 OK
2025-07-31 03:22:58,466 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 03:22:58,466 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 03:22:58,467 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:22:58,467 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:39002 - "GET /knowledge-bases HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 03:22:58,663 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 03:22:58,663 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 03:22:58,663 - guixiaoxi_api - INFO - 👋 GuiXiaoxi RAG API 已关闭
2025-07-31 03:22:58,663 - guixiaoxi_api - INFO - 👋 GuiXiaoxi RAG API 已关闭
INFO:     Application shutdown complete.
INFO:     Finished server process [977344]
2025-07-31 03:52:08,763 - guixiaoxi_api - INFO - 启动 GuiXiaoxi RAG 服务...
2025-07-31 03:52:08,763 - guixiaoxi_api - INFO - 配置文件: config.json
2025-07-31 03:52:08,763 - guixiaoxi_api - INFO - 服务地址: 0.0.0.0:8010
2025-07-31 03:52:08,763 - guixiaoxi_api - INFO - 工作目录: ./knowledgeBase
2025-07-31 03:52:08,763 - guixiaoxi_api - INFO - 调试模式: False
2025-07-31 03:52:09,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:52:09,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:52:09,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 default 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:52:11,609 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:52:11,679 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:52:13,609 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:52:13,722 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:52:13,861 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:52:13,864 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
ERROR: 初始化知识库 cs_college 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:52:13,867 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:52:13,867 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:52:13,867 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 general 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:52:13,868 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_entities.json'} 0 data
2025-07-31 03:52:13,868 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_relationships.json'} 0 data
2025-07-31 03:52:13,868 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_chunks.json'} 0 data
ERROR: 初始化通用问答RAG失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:52:13,871 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:52:13,871 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:52:13,872 - guixiaoxi_api - INFO - 异常处理器设置完成
2025-07-31 03:52:13,872 - guixiaoxi_api - INFO - 异常处理器设置完成
INFO:     Started server process [980510]
INFO:     Waiting for application startup.
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:52:13,928 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:52:13,929 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 创建成功，正在异步初始化...
2025-07-31 03:52:15,980 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:52:15,980 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:52:16,049 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:52:16,049 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:52:17,921 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:52:17,921 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:52:18,032 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:52:18,032 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:52:18,159 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:52:18,159 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:52:18,163 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:52:18,163 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 创建成功，正在异步初始化...
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:52:18,164 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 创建成功，正在异步初始化...
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:52:18,165 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 初始化成功
2025-07-31 03:52:20,018 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:52:20,018 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:52:20,087 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:52:20,087 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:52:21,940 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:52:21,940 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:52:22,048 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:52:22,048 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:52:22,175 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:52:22,175 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:52:22,178 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:52:22,178 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 初始化成功
2025-07-31 03:52:22,179 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:52:22,179 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:52:22,180 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:52:22,180 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:52:22,180 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:52:22,180 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 初始化成功
2025-07-31 03:52:22,180 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
2025-07-31 03:52:22,180 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
INFO:     Application startup complete.
INFO: 知识库 default 异步初始化完成
INFO: 知识库 cs_college 异步初始化完成
INFO: 知识库 general 异步初始化完成
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
2025-07-31 03:52:22,730 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:52:22,730 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:52:22,731 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:52:22,731 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:54006 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:52:23,737 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:52:23,737 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 03:52:23,737 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:52:23,737 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:54016 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:52:23,739 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 03:52:23,739 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 03:52:23,739 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:52:23,739 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:54018 - "GET /config HTTP/1.1" 200 OK
2025-07-31 03:52:23,740 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 03:52:23,740 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 03:52:23,741 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:52:23,741 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:54022 - "GET /knowledge-bases HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 03:52:23,887 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 03:52:23,887 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 03:52:23,887 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 03:52:23,887 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 03:52:23,888 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 03:52:23,888 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 03:52:23,908 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
2025-07-31 03:52:23,908 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 90, in _serve
    await self.shutdown(sockets=sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 293, in shutdown
    await self.lifespan.shutdown()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py", line 70, in shutdown
    await self.shutdown_event.wait()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/start_server.py", line 59, in main
    uvicorn.run(
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 194, in run
    with Runner(debug=debug, loop_factory=loop_factory) as runner:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 62, in __exit__
    self.close()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 70, in close
    _cancel_all_tasks(loop)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 204, in _cancel_all_tasks
    task.cancel()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 989 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 03:52:23,928 - guixiaoxi_api - INFO - 🧹 清理资源...
2025-07-31 03:52:23,928 - guixiaoxi_api - INFO - 🧹 清理资源...
❌ 服务启动失败: maximum recursion depth exceeded
🧹 清理资源...
2025-07-31 03:52:23,969 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
2025-07-31 03:52:23,969 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
ERROR:    Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
GeneratorExit

2025-07-31 03:55:17,718 - guixiaoxi_api - INFO - 启动 GuiXiaoxi RAG 服务...
2025-07-31 03:55:17,720 - guixiaoxi_api - INFO - 配置文件: config.json
2025-07-31 03:55:17,720 - guixiaoxi_api - INFO - 服务地址: 0.0.0.0:8014
2025-07-31 03:55:17,720 - guixiaoxi_api - INFO - 工作目录: ./knowledgeBase
2025-07-31 03:55:17,720 - guixiaoxi_api - INFO - 调试模式: False
2025-07-31 03:55:18,654 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:55:18,654 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:55:18,654 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 default 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:55:20,439 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:55:20,506 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:55:22,352 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:55:22,461 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:55:22,585 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:55:22,588 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
ERROR: 初始化知识库 cs_college 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:55:22,589 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:55:22,589 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:55:22,589 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 general 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:55:22,590 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_entities.json'} 0 data
2025-07-31 03:55:22,590 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_relationships.json'} 0 data
2025-07-31 03:55:22,590 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_chunks.json'} 0 data
ERROR: 初始化通用问答RAG失败: Try to create namespace before Shared-Data is initialized
2025-07-31 03:55:22,592 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:55:22,592 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 03:55:22,592 - guixiaoxi_api - INFO - 异常处理器设置完成
2025-07-31 03:55:22,592 - guixiaoxi_api - INFO - 异常处理器设置完成
INFO:     Started server process [981009]
INFO:     Waiting for application startup.
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:55:22,628 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 03:55:22,629 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:55:22,629 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:55:22,629 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 创建成功，正在异步初始化...
2025-07-31 03:55:24,480 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:55:24,480 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:55:24,548 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:55:24,548 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:55:26,363 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:55:26,363 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:55:26,466 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:55:26,466 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:55:26,591 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:55:26,591 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:55:26,594 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:55:26,594 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 创建成功，正在异步初始化...
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:55:26,595 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 创建成功，正在异步初始化...
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 03:55:26,596 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 初始化成功
2025-07-31 03:55:28,423 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:55:28,423 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 03:55:28,490 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:55:28,490 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 03:55:30,303 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:55:30,303 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 03:55:30,407 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:55:30,407 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 03:55:30,527 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:55:30,527 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 03:55:30,530 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 03:55:30,530 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 初始化成功
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 03:55:30,531 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 初始化成功
2025-07-31 03:55:30,531 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
2025-07-31 03:55:30,531 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
INFO:     Application startup complete.
INFO: 知识库 default 异步初始化完成
INFO: 知识库 cs_college 异步初始化完成
INFO: 知识库 general 异步初始化完成
INFO:     Uvicorn running on http://0.0.0.0:8014 (Press CTRL+C to quit)
2025-07-31 03:55:30,685 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:55:30,685 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:55:30,686 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:55:30,686 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:51638 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:55:31,691 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:55:31,691 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:55:31,692 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:55:31,692 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:36306 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:55:31,693 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/config from 127.0.0.1
2025-07-31 03:55:31,693 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/config from 127.0.0.1
2025-07-31 03:55:31,694 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/config - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:55:31,694 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/config - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:36318 - "GET /config HTTP/1.1" 200 OK
2025-07-31 03:55:31,695 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/knowledge-bases from 127.0.0.1
2025-07-31 03:55:31,695 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/knowledge-bases from 127.0.0.1
2025-07-31 03:55:31,696 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/knowledge-bases - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:55:31,696 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/knowledge-bases - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:36328 - "GET /knowledge-bases HTTP/1.1" 200 OK
2025-07-31 03:58:07,720 - guixiaoxi_api - INFO - 请求开始: POST http://localhost:8014/query from 127.0.0.1
2025-07-31 03:58:07,720 - guixiaoxi_api - INFO - 请求开始: POST http://localhost:8014/query from 127.0.0.1
2025-07-31 03:58:07,725 - guixiaoxi_api - INFO - 请求完成: POST http://localhost:8014/query - 状态码: 200 - 处理时间: 0.005s
2025-07-31 03:58:07,725 - guixiaoxi_api - INFO - 请求完成: POST http://localhost:8014/query - 状态码: 200 - 处理时间: 0.005s
INFO:     127.0.0.1:46312 - "POST /query HTTP/1.1" 200 OK
limit_async: Error in decorated function: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
ERROR: 流式查询失败: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
limit_async: Error in decorated function: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
ERROR: 流式查询失败: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
limit_async: Error in decorated function: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
ERROR: 流式查询失败: openai_complete_if_cache() missing 1 required positional argument: 'prompt'
2025-07-31 03:59:02,271 - guixiaoxi_api - INFO - 请求开始: GET http://localhost:8014/health from 127.0.0.1
2025-07-31 03:59:02,271 - guixiaoxi_api - INFO - 请求开始: GET http://localhost:8014/health from 127.0.0.1
2025-07-31 03:59:02,272 - guixiaoxi_api - INFO - 请求完成: GET http://localhost:8014/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:59:02,272 - guixiaoxi_api - INFO - 请求完成: GET http://localhost:8014/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:47400 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:59:23,464 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:59:23,464 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/health from 127.0.0.1
2025-07-31 03:59:23,465 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 03:59:23,465 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:49488 - "GET /health HTTP/1.1" 200 OK
2025-07-31 03:59:23,466 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/config from 127.0.0.1
2025-07-31 03:59:23,466 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/config from 127.0.0.1
2025-07-31 03:59:23,467 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/config - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:59:23,467 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/config - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:49490 - "GET /config HTTP/1.1" 200 OK
2025-07-31 03:59:23,468 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/knowledge-bases from 127.0.0.1
2025-07-31 03:59:23,468 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8014/knowledge-bases from 127.0.0.1
2025-07-31 03:59:23,468 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
2025-07-31 03:59:23,468 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8014/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:49494 - "GET /knowledge-bases HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 04:00:32,842 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 04:00:32,842 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 04:00:32,844 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 04:00:32,844 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 04:00:32,845 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:00:32,845 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:00:32,865 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
2025-07-31 04:00:32,865 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 90, in _serve
    await self.shutdown(sockets=sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 293, in shutdown
    await self.lifespan.shutdown()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py", line 70, in shutdown
    await self.shutdown_event.wait()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/start_server.py", line 59, in main
    uvicorn.run(
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 194, in run
    with Runner(debug=debug, loop_factory=loop_factory) as runner:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 62, in __exit__
    self.close()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 70, in close
    _cancel_all_tasks(loop)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 204, in _cancel_all_tasks
    task.cancel()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 989 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:00:32,885 - guixiaoxi_api - INFO - 🧹 清理资源...
2025-07-31 04:00:32,885 - guixiaoxi_api - INFO - 🧹 清理资源...
❌ 服务启动失败: maximum recursion depth exceeded
🧹 清理资源...
2025-07-31 04:00:32,926 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
2025-07-31 04:00:32,926 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
ERROR:    Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
GeneratorExit

2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-161' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-161' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-162' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-162' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-163' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-163' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-164' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-164' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-165' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-31 04:00:32,951 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-165' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-167' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-167' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-168' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-168' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-169' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-169' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-170' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-170' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-171' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-171' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-173' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-173' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-174' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-174' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-175' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-175' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-176' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-176' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.worker() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:382> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[set.discard()]>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-177' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-31 04:00:32,952 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-177' coro=<priority_limit_async_func_call.<locals>.final_decro.<locals>.health_check() running at /mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py:422> wait_for=<Future pending cb=[Task.task_wakeup()]>>
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed389be00>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed389b940>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed389b0f0>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed389afc0>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed389a770>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4040>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4170>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c42a0>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4500>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4630>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4760>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
limit_async: Critical error in worker: Event loop is closed
Exception ignored in: <coroutine object priority_limit_async_func_call.<locals>.final_decro.<locals>.worker at 0x7f8ed35c4890>
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/guixiaoxi/utils.py", line 413, in worker
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 659, in sleep
RuntimeError: no running event loop
2025-07-31 04:04:13,591 - guixiaoxi_api - INFO - 启动 GuiXiaoxi RAG 服务...
2025-07-31 04:04:13,591 - guixiaoxi_api - INFO - 配置文件: config.json
2025-07-31 04:04:13,591 - guixiaoxi_api - INFO - 服务地址: 0.0.0.0:8010
2025-07-31 04:04:13,591 - guixiaoxi_api - INFO - 工作目录: ./knowledgeBase
2025-07-31 04:04:13,591 - guixiaoxi_api - INFO - 调试模式: False
2025-07-31 04:04:14,501 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 04:04:14,501 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 04:04:14,501 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 default 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 04:04:16,284 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 04:04:16,352 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 04:04:18,282 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 04:04:18,400 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 04:04:18,530 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 04:04:18,533 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
ERROR: 初始化知识库 cs_college 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
ERROR: 初始化知识库 general 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_entities.json'} 0 data
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_relationships.json'} 0 data
2025-07-31 04:04:18,535 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_chunks.json'} 0 data
ERROR: 初始化通用问答RAG失败: Try to create namespace before Shared-Data is initialized
2025-07-31 04:04:18,538 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 04:04:18,538 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 04:04:18,538 - guixiaoxi_api - INFO - 异常处理器设置完成
2025-07-31 04:04:18,538 - guixiaoxi_api - INFO - 异常处理器设置完成
INFO:     Started server process [982073]
INFO:     Waiting for application startup.
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 04:04:18,579 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 04:04:18,579 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 04:04:18,579 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 04:04:18,580 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 04:04:18,580 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 04:04:18,580 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 04:04:18,580 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 创建成功，正在异步初始化...
2025-07-31 04:04:20,510 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 04:04:20,510 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 04:04:20,583 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 04:04:20,583 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 04:04:22,436 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 04:04:22,436 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 04:04:22,547 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 04:04:22,547 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 04:04:22,673 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 04:04:22,673 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 04:04:22,676 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 04:04:22,676 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 创建成功，正在异步初始化...
2025-07-31 04:04:22,677 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 04:04:22,677 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 创建成功，正在异步初始化...
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_entities.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_relationships.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
2025-07-31 04:04:22,678 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/default/vdb_chunks.json'} 0 data
INFO: 知识库 default 初始化成功
2025-07-31 04:04:24,525 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 04:04:24,525 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 04:04:24,594 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 04:04:24,594 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 04:04:26,459 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 04:04:26,459 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 04:04:26,570 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 04:04:26,570 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 04:04:26,698 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 04:04:26,698 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 04:04:26,701 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 04:04:26,701 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 初始化成功
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_entities.json'} 0 data
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_relationships.json'} 0 data
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
2025-07-31 04:04:26,702 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general/vdb_chunks.json'} 0 data
INFO: 知识库 general 初始化成功
2025-07-31 04:04:26,703 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
2025-07-31 04:04:26,703 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
INFO:     Application startup complete.
INFO: 知识库 default 异步初始化完成
INFO: 知识库 cs_college 异步初始化完成
INFO: 知识库 general 异步初始化完成
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
2025-07-31 04:04:27,559 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 04:04:27,559 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 04:04:27,560 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 04:04:27,560 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:40730 - "GET /health HTTP/1.1" 200 OK
2025-07-31 04:04:28,565 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 04:04:28,565 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/health from 127.0.0.1
2025-07-31 04:04:28,566 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
2025-07-31 04:04:28,566 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/health - 状态码: 200 - 处理时间: 0.001s
INFO:     127.0.0.1:40736 - "GET /health HTTP/1.1" 200 OK
2025-07-31 04:04:28,567 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 04:04:28,567 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/config from 127.0.0.1
2025-07-31 04:04:28,568 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
2025-07-31 04:04:28,568 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/config - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:40740 - "GET /config HTTP/1.1" 200 OK
2025-07-31 04:04:28,569 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 04:04:28,569 - guixiaoxi_api - INFO - 请求开始: GET http://0.0.0.0:8010/knowledge-bases from 127.0.0.1
2025-07-31 04:04:28,569 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
2025-07-31 04:04:28,569 - guixiaoxi_api - INFO - 请求完成: GET http://0.0.0.0:8010/knowledge-bases - 状态码: 200 - 处理时间: 0.000s
INFO:     127.0.0.1:40748 - "GET /knowledge-bases HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 04:05:58,733 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 04:05:58,733 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 04:05:58,736 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 04:05:58,736 - guixiaoxi_api - INFO - 正在清理 2 个异步任务...
2025-07-31 04:05:58,737 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:05:58,737 - asyncio - ERROR - Exception in callback Task.task_wakeup(<Future finished result=None>)
handle: <Handle Task.task_wakeup(<Future finished result=None>)>
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 987 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:05:58,756 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
2025-07-31 04:05:58,756 - guixiaoxi_api - ERROR - ❌ 服务启动失败: maximum recursion depth exceeded
Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 90, in _serve
    await self.shutdown(sockets=sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 293, in shutdown
    await self.lifespan.shutdown()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py", line 70, in shutdown
    await self.shutdown_event.wait()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/start_server.py", line 59, in main
    uvicorn.run(
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 194, in run
    with Runner(debug=debug, loop_factory=loop_factory) as runner:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 62, in __exit__
    self.close()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 70, in close
    _cancel_all_tasks(loop)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 204, in _cancel_all_tasks
    task.cancel()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 721, in cancel
    if child.cancel(msg=msg):
       ^^^^^^^^^^^^^^^^^^^^^
  [Previous line repeated 989 more times]
RecursionError: maximum recursion depth exceeded
2025-07-31 04:05:58,775 - guixiaoxi_api - INFO - 🧹 清理资源...
2025-07-31 04:05:58,775 - guixiaoxi_api - INFO - 🧹 清理资源...
❌ 服务启动失败: maximum recursion depth exceeded
🧹 清理资源...
2025-07-31 04:05:58,821 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
2025-07-31 04:05:58,821 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task cancelling name='Task-42' coro=<LifespanOn.main() running at /root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py:86> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[gather.<locals>._done_callback() at /root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py:767]>
ERROR:    Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
GeneratorExit

