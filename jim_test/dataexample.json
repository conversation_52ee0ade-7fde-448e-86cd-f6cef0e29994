{"亲属": {"夫妻": {"定义": "通过合法婚姻关系建立的配偶关系。", "特点": ["最基本的亲属关系。", "享有法律上的权利与义务（如继承权、扶养义务等）。"], "示例": ["丈夫与妻子", "离婚后复婚的夫妻", "再婚夫妻"]}, "一代直系血亲": {"定义": "直接血缘关系中相差一代的关系。", "包括": ["父母与子女", "祖父母与孙子女", "外祖父母与外孙子女"], "特点": ["血缘最亲近的亲属。", "法律上享有继承权、监护权等。"], "示例": ["父亲与儿子", "母亲与女儿", "爷爷与孙子"]}, "一代以上直系血亲": {"定义": "超过一代的直系血缘关系。", "包括": ["曾祖父母与曾孙子女", "高祖父母与玄孙子女"], "特点": ["血缘关系较远，但仍属于直系亲属。", "在某些法律或传统习俗中可能仍享有特定权利或义务。"], "示例": ["曾祖父与曾孙", "高祖父与玄孙"]}, "一代旁系血亲": {"定义": "与自己同一代、但非直系血亲的亲属。", "包括": ["兄弟姐妹", "堂兄弟姐妹（父亲兄弟的子女）", "表兄弟姐妹（母亲姐妹或兄弟的子女）"], "特点": ["血缘较近，但非直系。", "在继承法中通常属于第二顺位继承人。"], "示例": ["亲兄弟", "堂姐与堂弟", "表妹与表哥"]}, "一代以上旁系血亲": {"定义": "旁系血亲中相差一代或多代的关系。", "包括": ["叔伯、舅舅、姑姑、姨妈（父母的兄弟姐妹）", "侄子侄女、外甥外甥女（兄弟姐妹的子女）", "侄孙、侄孙女等"], "特点": ["血缘关系更远，但在某些文化或法律中仍可能享有特定权利。"], "示例": ["叔叔与侄子", "姑姑与侄女", "舅舅与外甥"]}}, "姻亲": {"定义": "通过婚姻关系而产生的亲属关系，并非血缘关系。", "包括": ["岳父母、公婆（配偶的父母）", "妯娌、连襟（配偶的兄弟姐妹的配偶）", "姑子、舅子（配偶的兄弟姐妹）"], "特点": ["没有血缘关系，但有法律上的部分权利义务（如赡养、继承权等）。", "婚姻关系解除后，姻亲关系通常也随之终止。"], "示例": ["妻子的母亲（岳母）", "丈夫的父亲（公公）", "配偶的兄弟的妻子（嫂子/弟媳）", "配偶的姐妹的丈夫（姐夫/妹夫）"]}, "朋友": {"定义": "通过情感、兴趣、共同经历等自愿建立的社会关系。", "特点": ["非血缘、非法律强制建立的关系。", "没有法律上的抚养、赡养或继承义务。", "可能具有高度信任与情感依赖。"], "示例": ["童年玩伴", "兴趣社团的成员", "职场外的社交圈朋友"]}, "同事": {"定义": "在同一单位或组织中工作的人员。", "特点": ["基于职业关系建立。", "通常具有合作、竞争或上下级关系。", "工作环境中互动频繁，但关系多限于工作范围。"], "示例": ["同一部门的团队成员", "跨部门协作的同事", "实习导师与实习生"]}, "同学": {"定义": "在相同学校、班级或课程中学习的人。", "特点": ["基于教育经历建立。", "年龄相仿，常有共同话题。", "可能发展为朋友或长期联系。"], "示例": ["小学同桌", "大学室友", "课外班的学员"]}, "邻居": {"定义": "居住在附近的人，通常为同一社区或楼宇的居民。", "特点": ["地理位置接近，日常生活中可能频繁接触。", "可能有互助关系（如帮忙收快递、看家等）。", "关系可亲可疏，视具体交往情况而定。"], "示例": ["楼上楼下住户", "小区内遛狗的熟人", "租客与房东"]}, "陌生人": {"定义": "彼此之间没有认识或交往关系的人。", "特点": ["没有任何社会关系基础。", "互动通常基于临时或偶然的场合（如问路、交易等）。", "法律上通常无特殊义务，除非发生特定行为（如救助义务）。"], "示例": ["便利店收银员", "出租车司机", "超市购物时偶遇的顾客"]}, "合同交易双方": {"定义": "基于合同约定而建立的权利义务关系的双方。", "特点": ["通过书面或口头合同建立。", "合同内容可涉及买卖、租赁、服务、雇佣等多种形式。", "法律上具有明确的权利义务关系。", "关系通常以利益为导向，而非情感基础。"], "示例": ["房东与租客", "电商平台卖家与买家", "装修公司与业主", "软件开发者与客户"]}}