#!/usr/bin/env python3
"""
简化的Streamlit Web服务启动器
专门用于解决启动问题
"""

import os
import sys
import subprocess
import time
import signal
import requests
from pathlib import Path

def check_api_service():
    """检查API服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_streamlit():
    """启动Streamlit应用"""
    print("🎨 启动 Streamlit Web 界面...")
    
    # 确定应用文件路径
    current_dir = Path(__file__).parent
    app_files = [
        current_dir / "server" / "web" / "minimal_app.py",
        current_dir / "server" / "web" / "simple_streamlit_app.py", 
        current_dir / "server" / "web" / "streamlit_app.py"
    ]
    
    app_path = None
    for path in app_files:
        if path.exists():
            app_path = path
            print(f"使用应用文件: {path.name}")
            break
    
    if not app_path:
        print("❌ 未找到Streamlit应用文件")
        return False
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(current_dir)
    env['STREAMLIT_SERVER_HEADLESS'] = 'true'
    env['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
    
    # 构建启动命令
    cmd = [
        sys.executable,
        "-m", "streamlit",
        "run",
        str(app_path),
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false",
        "--logger.level", "error"
    ]
    
    print(f"启动命令: {' '.join(cmd)}")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=current_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"Streamlit进程已启动，PID: {process.pid}")
        
        # 等待启动
        print("⏳ 等待服务启动...")
        for i in range(30):
            if process.poll() is not None:
                print(f"❌ 进程异常退出，返回码: {process.returncode}")
                # 读取输出
                output = process.stdout.read()
                if output:
                    print(f"进程输出: {output}")
                return False
            
            try:
                response = requests.get("http://localhost:8501", timeout=2)
                if response.status_code == 200:
                    print("✅ Streamlit服务启动成功")
                    print("🌐 Web界面地址: http://localhost:8501")
                    return process
            except:
                pass
            
            time.sleep(1)
            if i % 5 == 0 and i > 0:
                print(f"   等待中... ({i+1}/30)")
        
        print("⚠️ 启动检查超时，但进程仍在运行")
        return process
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 GuiXiaoxi RAG Web服务启动器")
    print("=" * 50)
    
    # 检查API服务
    if check_api_service():
        print("✅ API服务正常运行")
    else:
        print("⚠️ API服务未运行，Web界面功能可能受限")
    
    # 启动Streamlit
    process = start_streamlit()
    
    if not process:
        print("❌ Web服务启动失败")
        return 1
    
    # 等待用户中断
    try:
        print("\n按 Ctrl+C 停止服务...")
        while True:
            if process.poll() is not None:
                print(f"❌ 服务进程异常退出，返回码: {process.returncode}")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        print("✅ 服务已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
