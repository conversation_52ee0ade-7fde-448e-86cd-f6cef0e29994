2025-07-31 15:32:35,920 - guixiaoxi_api - INFO - 启动 GuiXiaoxi RAG 服务...
2025-07-31 15:32:35,920 - guixiaoxi_api - INFO - 配置文件: config.json
2025-07-31 15:32:35,920 - guixiaoxi_api - INFO - 服务地址: 0.0.0.0:8000
2025-07-31 15:32:35,920 - guixiaoxi_api - INFO - 工作目录: ./knowledgeBase
2025-07-31 15:32:35,920 - guixiaoxi_api - INFO - 调试模式: False
2025-07-31 15:32:38,877 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 15:32:38,951 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 15:32:40,876 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 15:32:40,984 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 15:32:41,122 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 15:32:41,125 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
ERROR: 初始化知识库 cs_college 失败: Try to create namespace before Shared-Data is initialized
2025-07-31 15:32:41,129 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_entities.json'} 0 data
2025-07-31 15:32:41,129 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_relationships.json'} 0 data
2025-07-31 15:32:41,130 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/general_qa/vdb_chunks.json'} 0 data
ERROR: 初始化通用问答RAG失败: Try to create namespace before Shared-Data is initialized
2025-07-31 15:32:41,134 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 15:32:41,134 - guixiaoxi_api - INFO - 中间件设置完成
2025-07-31 15:32:41,134 - guixiaoxi_api - INFO - 异常处理器设置完成
2025-07-31 15:32:41,134 - guixiaoxi_api - INFO - 异常处理器设置完成
INFO:     Started server process [1032157]
INFO:     Waiting for application startup.
2025-07-31 15:32:41,169 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 15:32:41,169 - guixiaoxi_api - INFO - 🚀 GuiXiaoxi RAG API 启动中...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 📚 初始化知识库管理器...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔧 初始化共享数据...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔧 初始化管道状态...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 15:32:41,170 - guixiaoxi_api - INFO - 🔄 重新初始化知识库...
2025-07-31 15:32:43,065 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 15:32:43,065 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 15:32:43,135 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 15:32:43,135 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 15:32:45,084 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 15:32:45,084 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 15:32:45,193 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 15:32:45,193 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 15:32:45,318 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 15:32:45,318 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 15:32:45,322 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 15:32:45,322 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 创建成功，正在异步初始化...
2025-07-31 15:32:47,162 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 15:32:47,162 - nano-vectordb - INFO - Load (9805, 2560) data
2025-07-31 15:32:47,235 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 15:32:47,235 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_entities.json'} 9805 data
2025-07-31 15:32:49,106 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 15:32:49,106 - nano-vectordb - INFO - Load (15192, 2560) data
2025-07-31 15:32:49,214 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 15:32:49,214 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_relationships.json'} 15192 data
2025-07-31 15:32:49,336 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 15:32:49,336 - nano-vectordb - INFO - Load (975, 2560) data
2025-07-31 15:32:49,339 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
2025-07-31 15:32:49,339 - nano-vectordb - INFO - Init {'embedding_dim': 2560, 'metric': 'cosine', 'storage_file': './knowledgeBase/cs_college/vdb_chunks.json'} 975 data
INFO: 知识库 cs_college 初始化成功
2025-07-31 15:32:49,340 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
2025-07-31 15:32:49,340 - guixiaoxi_api - INFO - ✅ GuiXiaoxi RAG API 启动完成
INFO:     Application startup complete.
INFO: 知识库 cs_college 异步初始化完成
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 15:35:41,239 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 15:35:41,239 - guixiaoxi_api - INFO - 🔄 GuiXiaoxi RAG API 关闭中...
2025-07-31 15:35:41,241 - guixiaoxi_api - INFO - 正在清理 1 个异步任务...
2025-07-31 15:35:41,241 - guixiaoxi_api - INFO - 正在清理 1 个异步任务...
ERROR:    Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 90, in _serve
    await self.shutdown(sockets=sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 293, in shutdown
    await self.lifespan.shutdown()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py", line 70, in shutdown
    await self.shutdown_event.wait()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/mnt/Jim/project/gui_xiaoxi/server/api/app.py", line 85, in lifespan
    await asyncio.sleep(0.1)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/tasks.py", line 665, in sleep
    return await future
           ^^^^^^^^^^^^
asyncio.exceptions.CancelledError

2025-07-31 15:35:41,243 - guixiaoxi_api - INFO - 🧹 清理资源...
2025-07-31 15:35:41,243 - guixiaoxi_api - INFO - 🧹 清理资源...
✅ 环境变量设置完成
   OPENAI_API_KEY: sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q
   OPENAI_API_BASE: http://localhost:8100/v1
🧹 清理资源...
Traceback (most recent call last):
  File "/mnt/Jim/project/gui_xiaoxi/start_server.py", line 125, in <module>
    main()
  File "/mnt/Jim/project/gui_xiaoxi/start_server.py", line 100, in main
    uvicorn.run(
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/main.py", line 580, in run
    server.run()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/base_events.py", line 691, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 90, in _serve
    await self.shutdown(sockets=sockets)
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/server.py", line 293, in shutdown
    await self.lifespan.shutdown()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/site-packages/uvicorn/lifespan/on.py", line 70, in shutdown
    await self.shutdown_event.wait()
  File "/root/miniconda3/envs/lightrag312/lib/python3.12/asyncio/locks.py", line 212, in wait
    await fut
asyncio.exceptions.CancelledError
