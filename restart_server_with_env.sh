#!/bin/bash
# 重启 GuiXiaoxi RAG 服务器并设置正确的环境变量

echo "🔄 重启 GuiXiaoxi RAG 服务器..."

# 设置环境变量
echo "🔧 设置环境变量..."
export OPENAI_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
export OPENAI_API_BASE="http://localhost:8100/v1"
export LLM_MODEL="qwen14b"
export EMBEDDING_API_KEY="sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q"
export EMBEDDING_API_BASE="http://localhost:8200/v1"
export EMBEDDING_MODEL="embedding_qwen"

# 停止现有服务器
echo "🛑 停止现有服务器..."
pkill -f "python start_server.py" || echo "没有找到运行中的服务器"

# 等待进程完全停止
sleep 2

# 启动新服务器
echo "🚀 启动服务器..."
nohup python start_server.py > server.log 2>&1 &

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 5

# 检查服务器状态
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务器启动成功！"
    echo "🌐 API 地址: http://localhost:8000"
    echo "📚 API 文档: http://localhost:8000/docs"
    
    # 测试查询
    echo ""
    echo "🧪 测试查询..."
    curl -X POST "http://localhost:8000/query" \
      -H "Content-Type: application/json" \
      -d '{
        "query": "测试查询",
        "mode": "hybrid"
      }' 2>/dev/null | head -c 200
    echo ""
    echo ""
    echo "✅ 服务器重启完成！"
else
    echo "❌ 服务器启动失败，请检查日志："
    echo "tail -f server.log"
fi
