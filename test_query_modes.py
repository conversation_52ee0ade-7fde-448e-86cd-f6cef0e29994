#!/usr/bin/env python3
"""
GuiXiaoxi RAG 查询模式测试脚本
测试所有支持的查询模式：LOCAL, GLOBAL, HYBRID, NAIVE, MIX, BYPASS
"""

import asyncio
import json
import time
from typing import Dict, Any
import requests

class QueryModesTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
    def print_header(self, title: str):
        print(f"\n{'='*80}")
        print(f"🔧 {title}")
        print(f"{'='*80}")
    
    def print_mode_test(self, mode: str, description: str):
        print(f"\n🧪 测试模式: {mode.upper()}")
        print(f"📝 描述: {description}")
        print("-" * 60)
    
    def test_basic_query(self, query: str, mode: str) -> Dict[str, Any]:
        """测试基础查询"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "kb_ids": ["cs_college"]
            }
            
            start_time = time.time()
            response = self.session.post(f"{self.base_url}/query", json=payload, timeout=30)
            query_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "mode": mode,
                    "query_time": query_time,
                    "response": result,
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "mode": mode,
                    "query_time": query_time,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "mode": mode,
                "error": str(e),
                "status_code": None
            }
    
    def test_advanced_query(self, query: str, mode: str, **kwargs) -> Dict[str, Any]:
        """测试高级查询"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "knowledge_base_ids": ["cs_college"],
                "top_k": kwargs.get("top_k", 10),
                "response_type": kwargs.get("response_type", "Multiple Paragraphs"),
                **kwargs
            }
            
            start_time = time.time()
            response = self.session.post(f"{self.base_url}/query/advanced", json=payload, timeout=30)
            query_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "mode": mode,
                    "query_time": query_time,
                    "response": result,
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "mode": mode,
                    "query_time": query_time,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "mode": mode,
                "error": str(e),
                "status_code": None
            }
    
    def test_stream_query(self, query: str, mode: str) -> Dict[str, Any]:
        """测试流式查询"""
        try:
            payload = {
                "query": query,
                "mode": mode,
                "knowledge_base_ids": ["cs_college"]
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/query/stream", 
                json=payload, 
                timeout=30,
                stream=True
            )
            
            if response.status_code == 200:
                chunks = []
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            chunks.append(chunk_data)
                        except json.JSONDecodeError:
                            continue
                
                query_time = time.time() - start_time
                return {
                    "success": True,
                    "mode": mode,
                    "query_time": query_time,
                    "chunks_count": len(chunks),
                    "chunks": chunks[:3],  # 只显示前3个块
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "mode": mode,
                    "error": response.text,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "success": False,
                "mode": mode,
                "error": str(e),
                "status_code": None
            }
    
    def run_comprehensive_test(self):
        """运行全面的查询模式测试"""
        self.print_header("GuiXiaoxi RAG 查询模式全面测试")
        
        # 测试查询
        test_query = "计算机学院院长是谁？"
        
        # 查询模式配置
        modes_config = {
            "local": {
                "description": "本地实体查询，基于实体相似性检索",
                "test_params": {"top_k": 10}
            },
            "global": {
                "description": "全局关系查询，基于关系图谱检索", 
                "test_params": {"top_k": 15}
            },
            "hybrid": {
                "description": "混合查询，结合本地和全局信息",
                "test_params": {"top_k": 20}
            },
            "naive": {
                "description": "简单向量检索，基于文档相似性",
                "test_params": {"chunk_top_k": 5}
            },
            "mix": {
                "description": "混合检索模式，结合知识图谱和向量检索",
                "test_params": {"top_k": 15, "chunk_top_k": 5}
            },
            "bypass": {
                "description": "直接LLM查询，跳过RAG检索",
                "test_params": {}
            }
        }
        
        results = {}
        
        for mode, config in modes_config.items():
            self.print_mode_test(mode, config["description"])
            
            # 测试基础查询
            print("📋 基础查询测试...")
            basic_result = self.test_basic_query(test_query, mode)
            
            # 测试高级查询
            print("🚀 高级查询测试...")
            advanced_result = self.test_advanced_query(
                test_query, 
                mode, 
                **config["test_params"]
            )
            
            # 测试流式查询
            print("🌊 流式查询测试...")
            stream_result = self.test_stream_query(test_query, mode)
            
            results[mode] = {
                "basic": basic_result,
                "advanced": advanced_result,
                "stream": stream_result
            }
            
            # 显示结果摘要
            basic_status = "✅" if basic_result["success"] else "❌"
            advanced_status = "✅" if advanced_result["success"] else "❌"
            stream_status = "✅" if stream_result["success"] else "❌"
            
            print(f"结果: 基础查询 {basic_status} | 高级查询 {advanced_status} | 流式查询 {stream_status}")
            
            if basic_result["success"]:
                print(f"⏱️  查询时间: {basic_result['query_time']:.2f}s")
        
        # 生成测试报告
        self.generate_test_report(results)
        
        return results
    
    def generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        self.print_header("测试报告")
        
        total_tests = len(results) * 3  # 每个模式3种测试
        passed_tests = 0
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        
        for mode, mode_results in results.items():
            mode_passed = sum(1 for test_result in mode_results.values() if test_result["success"])
            passed_tests += mode_passed
            
            status = "✅" if mode_passed == 3 else "⚠️" if mode_passed > 0 else "❌"
            print(f"   {status} {mode.upper()}: {mode_passed}/3 通过")
            
            # 显示失败的测试
            for test_type, test_result in mode_results.items():
                if not test_result["success"]:
                    print(f"      ❌ {test_type}: {test_result.get('error', '未知错误')}")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n🎯 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate == 100:
            print("🎉 所有查询模式测试通过！")
        elif success_rate >= 80:
            print("👍 大部分查询模式工作正常")
        else:
            print("⚠️  需要检查查询模式实现")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="GuiXiaoxi RAG 查询模式测试")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务地址")
    parser.add_argument("--mode", help="测试特定模式")
    
    args = parser.parse_args()
    
    tester = QueryModesTester(args.url)
    
    if args.mode:
        # 测试特定模式
        print(f"🎯 测试模式: {args.mode}")
        result = tester.test_basic_query("计算机学院院长是谁？", args.mode)
        print(f"结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        if result["success"]:
            print(f"响应: {result['response']}")
        else:
            print(f"错误: {result['error']}")
    else:
        # 运行全面测试
        tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
