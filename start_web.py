#!/usr/bin/env python3
"""
简单的Streamlit Web服务启动器
解决启动闪退问题
"""

import os
import sys
import subprocess
import time
import signal
import requests
from pathlib import Path

def check_api_service():
    """检查API服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数"""
    print("🤖 GuiXiaoxi RAG Web服务启动器")
    print("=" * 50)
    
    # 检查API服务
    if check_api_service():
        print("✅ API服务正常运行")
    else:
        print("⚠️ API服务未运行，Web界面功能可能受限")
    
    # 设置环境
    current_dir = Path(__file__).parent
    env = os.environ.copy()
    env['PYTHONPATH'] = str(current_dir)
    
    # 确定应用文件
    app_files = [
        current_dir / "server" / "web" / "minimal_app.py",
        current_dir / "server" / "web" / "simple_streamlit_app.py", 
        current_dir / "server" / "web" / "streamlit_app.py"
    ]
    
    app_path = None
    for path in app_files:
        if path.exists():
            app_path = path
            print(f"📱 使用应用: {path.name}")
            break
    
    if not app_path:
        print("❌ 未找到Streamlit应用文件")
        return 1
    
    # 构建启动命令
    cmd = [
        sys.executable,
        "-m", "streamlit",
        "run",
        str(app_path),
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--browser.gatherUsageStats", "false",
        "--logger.level", "warning"
    ]
    
    print("🎨 启动Streamlit Web界面...")
    print("🌐 访问地址: http://localhost:8501")
    print("=" * 50)
    print("按 Ctrl+C 停止服务")
    print()
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=current_dir,
            env=env
        )
        
        # 等待用户中断或进程结束
        while True:
            if process.poll() is not None:
                print(f"❌ 服务进程异常退出，返回码: {process.returncode}")
                break
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n👋 正在停止服务...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        print("✅ 服务已停止")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
