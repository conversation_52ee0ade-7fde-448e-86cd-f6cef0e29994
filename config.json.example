{"llm": {"api_base": "http://localhost:8100/v1", "api_key": "your-api-key-here", "model": "qwen14b", "max_token_size": 2048, "max_tokens": 4096, "stream": true, "temperature": 0.7}, "embedding": {"api_base": "http://localhost:8200/v1", "api_key": "your-api-key-here", "model": "embedding_qwen", "embedding_dim": 2560, "max_token_size": 2048}, "rag": {"working_dir": "./knowledgeBase", "default_kb": "default", "language": "中文", "max_gleaning": 1, "force_llm_summary_on_merge": true, "enable_stream": true, "enable_general_qa": true}, "knowledge_bases": {"default": {"name": "默认知识库", "description": "系统默认知识库", "working_dir": "./knowledgeBase/default", "enabled": true}, "cs_college": {"name": "计算机学院", "description": "贵州大学计算机科学与技术学院知识库", "working_dir": "./knowledgeBase/cs_college", "enabled": true}, "general": {"name": "通用知识", "description": "通用知识库", "working_dir": "./knowledgeBase/general", "enabled": true}, "custom": {"name": "自定义知识库", "description": "用户自定义知识库", "working_dir": "./knowledgeBase/custom", "enabled": false}}, "api": {"host": "0.0.0.0", "port": 8000, "debug": false, "cors_origins": ["*"], "max_upload_size": 104857600, "temp_dir": "temp_uploads", "enable_stream": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/guixiaoxi.log", "max_file_size": 10485760, "backup_count": 5}}