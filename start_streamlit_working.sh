#!/bin/bash

# GuiXiaoxi RAG Streamlit 工作启动脚本
# 这是经过测试的可工作版本

echo "🤖 GuiXiaoxi RAG Web服务启动器"
echo "=================================================="

# 检查API服务
echo "🔍 检查API服务状态..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ API服务正常运行"
else
    echo "⚠️ API服务未运行，建议先启动API服务"
    echo "   使用命令: ./restart_server_with_env.sh"
fi

# 设置环境变量
export PYTHONPATH=$(pwd)

# 确定应用文件
APP_FILE=""
if [ -f "server/web/complete_streamlit_app.py" ]; then
    APP_FILE="server/web/complete_streamlit_app.py"
    echo "📱 使用完整功能应用"
elif [ -f "server/web/streamlit_app.py" ]; then
    APP_FILE="server/web/streamlit_app.py"
    echo "📱 使用标准应用"
elif [ -f "server/web/simple_streamlit_app.py" ]; then
    APP_FILE="server/web/simple_streamlit_app.py"
    echo "📱 使用简化应用"
elif [ -f "server/web/minimal_app.py" ]; then
    APP_FILE="server/web/minimal_app.py"
    echo "📱 使用最小化应用"
else
    echo "❌ 未找到Streamlit应用文件"
    exit 1
fi

echo "🎨 启动Streamlit Web界面..."
echo "📂 应用文件: $APP_FILE"
echo "🌐 访问地址: http://localhost:8501"
echo "=================================================="
echo "按 Ctrl+C 停止服务"
echo ""

# 启动Streamlit (不使用headless模式，这是关键!)
python -m streamlit run "$APP_FILE" \
    --server.port 8501 \
    --server.address 0.0.0.0 \
    --browser.gatherUsageStats false \
    --logger.level warning
