#!/usr/bin/env python3
"""
GuiXiaoxi RAG 服务启动脚本
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from server.config.config import config_manager


def setup_environment():
    """设置必要的环境变量"""
    config_file = current_dir / "config.json"

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 设置 LLM 相关环境变量
            llm_config = config.get('llm', {})
            os.environ['OPENAI_API_KEY'] = llm_config.get('api_key', 'sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q')
            os.environ['OPENAI_API_BASE'] = llm_config.get('api_base', 'http://localhost:8100/v1')
            os.environ['LLM_MODEL'] = llm_config.get('model', 'qwen14b')

            # 设置嵌入相关环境变量
            embedding_config = config.get('embedding', {})
            os.environ['EMBEDDING_API_KEY'] = embedding_config.get('api_key', 'sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q')
            os.environ['EMBEDDING_API_BASE'] = embedding_config.get('api_base', 'http://localhost:8200/v1')
            os.environ['EMBEDDING_MODEL'] = embedding_config.get('model', 'embedding_qwen')

            print(f"✅ 环境变量设置完成")
            print(f"   OPENAI_API_KEY: {os.environ['OPENAI_API_KEY']}")
            print(f"   OPENAI_API_BASE: {os.environ['OPENAI_API_BASE']}")

        except Exception as e:
            print(f"⚠️  配置文件读取失败，使用默认环境变量: {e}")
            # 设置默认环境变量
            os.environ.setdefault('OPENAI_API_KEY', 'sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q')
            os.environ.setdefault('OPENAI_API_BASE', 'http://localhost:8100/v1')
    else:
        print(f"⚠️  配置文件不存在，使用默认环境变量")
        # 设置默认环境变量
        os.environ.setdefault('OPENAI_API_KEY', 'sk-gdXw028PJ6JtobnBLeQiArQLnmqahdXUQSjIbyFgAhJdHb1Q')
        os.environ.setdefault('OPENAI_API_BASE', 'http://localhost:8100/v1')


def main():
    # 首先设置环境变量
    setup_environment()

    parser = argparse.ArgumentParser(description="启动 GuiXiaoxi RAG 服务")
    parser.add_argument("--host", default=None, help="服务器主机地址")
    parser.add_argument("--port", type=int, default=None, help="服务器端口")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--log-level", default=None, help="日志级别")

    args = parser.parse_args()
    
    # 更新配置
    if args.config != "config.json":
        config_manager.config_file = args.config
        config_manager.load_config()
    
    if args.host:
        config_manager.api.host = args.host
    if args.port:
        config_manager.api.port = args.port
    if args.debug:
        config_manager.api.debug = True
    if args.log_level:
        config_manager.logging.level = args.log_level
    
    # 设置日志
    config_manager.setup_logging()
    logger = logging.getLogger("guixiaoxi_api")
    
    logger.info("启动 GuiXiaoxi RAG 服务...")
    logger.info(f"配置文件: {config_manager.config_file}")
    logger.info(f"服务地址: {config_manager.api.host}:{config_manager.api.port}")
    logger.info(f"工作目录: {config_manager.rag.working_dir}")
    logger.info(f"调试模式: {config_manager.api.debug}")
    
    try:
        import uvicorn
        from server.api.app import app
        
        uvicorn.run(
            app,
            host=config_manager.api.host,
            port=config_manager.api.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level=config_manager.logging.level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
        print("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        print(f"❌ 服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        logger.info("🧹 清理资源...")
        print("🧹 清理资源...")


if __name__ == "__main__":
    main()
